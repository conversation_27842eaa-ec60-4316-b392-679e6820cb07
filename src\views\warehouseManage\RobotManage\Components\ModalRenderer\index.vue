<template>
  <div>
    <canvas class="webgl" />
  </div>
</template>

<script>
import * as THREE from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
// import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader.js';

export default {
  name: "ModalRenderer",
  props: {
    modalUrl: {
      type: String,
      default() {
        return "http://localhost:3000/PopPickStation.glb";
      },
    },
    width: {
      type: Number,
      default() {
        return 100;
      },
    },
    height: {
      type: Number,
      default() {
        return 100;
      },
    },
  },
  data() {
    return {
      scene: new THREE.Scene(),
      renderer: null,
      camera: null,
    };
  },
  mounted() {
    this.init();
    this.render();
  },
  methods: {
    init() {
      //setup the camera
      this.camera = new THREE.PerspectiveCamera(
        45,
        window.innerWidth / window.innerHeight,
        0.25,
        20,
      );
      this.camera.position.set(-1.8, 0.6, 2.7);

      //load and create the environment
      // eslint-disable-next-line
      const that = this;
      // new RGBELoader()
      //   .setDataType(THREE.UnsignedByteType)
      //   .load(
      //     'https://cdn.jsdelivr.net/gh/mrdoob/three.js@master/examples/textures/equirectangular/royal_esplanade_1k.hdr',
      //     function(texture) {
      //       const pmremGenerator = new THREE.PMREMGenerator(that.renderer);
      //       pmremGenerator.compileEquirectangularShader();
      //       const envMap = pmremGenerator.fromEquirectangular(texture).texture;
      //
      //       that.scene.background = envMap; //this loads the envMap for the background
      //       that.scene.environment = envMap; //this loads the envMap for reflections and lighting
      //
      //       texture.dispose(); //we have envMap so we can erase the texture
      //       pmremGenerator.dispose(); //we processed the image into envMap so we can stop this
      //     }
      //   );
      //点光源
      const point = new THREE.PointLight(0xffffff);
      point.position.set(400, 200, 300); //点光源位置
      this.scene.add(point); //点光源添加到场景中

      // 点光源2  位置和point关于原点对称
      const point2 = new THREE.PointLight(0xffffff);
      point2.position.set(-400, -200, -300); //点光源位置
      this.scene.add(point2); //点光源添加到场景中
      // // 环境光
      // const light = new THREE.AmbientLight(0xffffff); // soft white light
      // this.scene.add(light);
      // 平行光
      // const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
      // this.scene.add(directionalLight);
      // load the model
      const loader = new GLTFLoader();

      loader.load(
        // this.modalUrl,
        "http://test-7292.local.k8s.ops.geekplus.cc/log/tomcat-rms/athena/file/maps/1647863112481.glb",
        // 'https://cdn.jsdelivr.net/gh/mrdoob/three.js@master/examples/models/gltf/DamagedHelmet/glTF/DamagedHelmet.gltf',
        function (gltf, gltfContent) {
          console.log(gltfContent);
          that.scene.add(gltf.scene);
          that.render(); //render the scene for the first time
        },
      );

      //setup the renderer
      this.renderer = new THREE.WebGLRenderer({
        antialias: true,
        canvas: document.querySelector("canvas.webgl"),
      });
      this.renderer.setPixelRatio(window.devicePixelRatio);
      this.renderer.setSize(this.width, this.height);
      this.renderer.toneMapping = THREE.ACESFilmicToneMapping; //added contrast for filmic lookColor
      this.renderer.toneMappingExposure = 1;
      this.renderer.outputEncoding = THREE.sRGBEncoding; //extended color space for the hdr

      const controls = new OrbitControls(this.camera, this.renderer.domElement);
      controls.addEventListener("change", this.render); // use if there is no animation loop to render after any changes
      controls.minDistance = 2;
      controls.maxDistance = 10;
      controls.target.set(0, 0, -0.2);
      controls.update();

      window.addEventListener("resize", this.onWindowResize);
    },
    onWindowResize() {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();

      this.renderer.setSize(window.innerWidth, window.innerHeight);

      this.render();
    },
    render() {
      this.renderer.render(this.scene, this.camera);
    },
  },
};
</script>

<style scoped></style>
