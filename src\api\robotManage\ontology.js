/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2022-01-07 17:38:44
 * @Description: 本体模型页面相关接口
 */
import request from "../request";

// ~~~~~~~~~~~~机器人本体~~~~~~~~~~~~~
// 开始查询数据
export function ontologyModelSearch(
  params = { name: "", chassisCode: "", series: "", batteryType: 0 },
) {
  return request({ url: "/athena/robot/manage/chassisPageList", method: "get", params });
}

// 创建机器人模型
export function createRobotModel(data = {}) {
  return request({ url: "/athena/robot/manage/chassisSave", method: "post", data });
}

// 查询机器人模型
export function getRobotRobotModelDetailInfo(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/chassisDetail", method: "get", data, params });
}

// 复制机器人模型
export function copyRobotRobotModelDetailInfo(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/chassisCopy", method: "get", data, params });
}

// 删除机器人模型
export function deleteRobotItem(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/chassisDelete", method: "get", data, params });
}

// ~~~~~~~~~~~~机器人实例~~~~~~~~~~~~~
// 机器人实例查询接口
export function getRobotPageList(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/robotPageList", method: "post", data, params });
}

// 机器人实例添加（修改）接口
export function addEditRobotItem(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/robotSave", method: "post", data, params });
}

// 机器人实例详情接口
export function getRobotDetailInfo(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/robotDetail", method: "get", data, params });
}

// 机器人实例删除接口
export function delRobotItem(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/robotDelete", method: "get", data, params });
}

// ~~~~~~~~~~~~机器人协议~~~~~~~~~~~~~
// 机器人协议分页查询接口
export function getProtocolPageList(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/protocolPageList", method: "post", data, params });
}

// 机器人协议新增（修改）接口
export function addEditProtocolItem(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/protocolSave", method: "post", data, params });
}

// 机器人协议详情接口
export function getProtocolDetailInfo(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/protocolDetail", method: "get", data, params });
}

// 机器人协议复制接口
export function copyProtocolDetailInfo(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/protocolCopy", method: "get", data, params });
}

// 机器人协议删除接口
export function deleteProtocolItem(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/protocolDelete", method: "get", data, params });
}

// ~~~~~~~~~~~~机器人业务~~~~~~~~~~~~~
// 机器人业务分页查询接口
export function getBusinessPageList(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/businessPageList", method: "post", data, params });
}

// 机器人业务新增（修改）接口
export function addEditBusinessItem(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/businessSave", method: "post", data, params });
}

// 机器人业务详情接口
export function getBusinessDetailInfo(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/businessDetail", method: "get", data, params });
}

// 机器人业务复制接口
export function copyBusinessDetailInfo(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/businessCopy", method: "get", data, params });
}

// 机器人业务删除接口
export function deleteBusinessItem(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/businessDelete", method: "get", data, params });
}

// ~~~~~~~~~~~~机器人机构组件~~~~~~~~~~~~~
// 机器人机构组件分页查询接口
export function getMechanismComponentPageList(data = {}, params = {}) {
  return request({
    url: "/athena/robot/manage/mechanismComponentPageList",
    method: "post",
    data,
    params,
  });
}

// 机器人机构组件新增（修改）接口
export function addEditMechanismComponentItem(data = {}, params = {}) {
  return request({
    url: "/athena/robot/manage/mechanismComponentSave",
    method: "post",
    data,
    params,
  });
}

// 机器人机构组件详情接口
export function getMechanismComponentDetailInfo(data = {}, params = {}) {
  return request({
    url: "/athena/robot/manage/mechanismComponentDetail",
    method: "get",
    data,
    params,
  });
}

// 机器人机构组件复制接口
export function copyMechanismComponentDetailInfo(data = {}, params = {}) {
  return request({
    url: "/athena/robot/manage/mechanismComponentCopy",
    method: "get",
    data,
    params,
  });
}

// 机器人机构组件删除接口
export function deleteMechanismComponentItem(data = {}, params = {}) {
  return request({
    url: "/athena/robot/manage/mechanismComponentDelete",
    method: "get",
    data,
    params,
  });
}

// ~~~~~~~~~~~~机器人机构模型~~~~~~~~~~~~~
// 机器人机构模型分页查询接口
export function getMechanismPageList(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/mechanismPageList", method: "post", data, params });
}

// 机器人机构模型添加（修改）接口
export function addEditMechanismItem(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/mechanismSave", method: "post", data, params });
}

// 机器人机构模型详情接口
export function getMechanismDetailInfo(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/mechanismDetail", method: "get", data, params });
}

// 机器人机构模型复制接口
export function copyMechanismDetailInfo(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/mechanismCopy", method: "get", data, params });
}

// 机器人机构模型删除接口
export function deleteMechanismItem(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/mechanismDelete", method: "get", data, params });
}

// ~~~~~~~~~~~~机器人模型~~~~~~~~~~~~~
// 机器人模型分页查询接口
export function getRobotModalPageList(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/modelPageList", method: "post", data, params });
}

// 机器人模型添加（修改）接口
export function addEditRobotModalItem(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/modelSave", method: "post", data, params });
}

// 机器人模型详情接口
export function getRobotModalDetailInfo(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/robotModelDetail", method: "get", data, params });
}

// 机器人模型复制接口
export function copyRobotModalDetailInfo(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/robotModelCopy", method: "get", data, params });
}

// 机器人模型删除接口
export function deleteRobotModalItem(data = {}, params = {}) {
  return request({ url: "/athena/robot/manage/robotModelDelete", method: "get", data, params });
}

// 业务特征 sizeTypeArr
export function getSizeTypeArr() {
  return request({ url: "/athena/robot/manage/findDistinctSizeType", method: "get" });
}
