<template>
  <div v-show="visible" class="map-select-dialog">
    <div class="map-select-body">
      <h5 class="title">
        <span>{{ $t("auth.rms.monitor.button.cellSelect") }}</span>
        <span style="cursor: pointer" @click.stop="visible = false">
          <i class="el-icon-close" />
        </span>
      </h5>

      <div class="left-operate">
        <el-input v-model="querySearchVal" :placeholder="$t('lang.rms.fed.searchCode')" size="mini">
          <el-button slot="append" size="mini" icon="el-icon-search" @click="searchCellCode" />
        </el-input>

        <div v-show="!!cellCode" class="code-list">
          <el-tag size="small" closable @close="handleClose">{{ cellCode }}</el-tag>
        </div>

        <div class="btn-group">
          <el-button type="primary" size="mini" @click.stop="confirm">
            {{ $t("lang.rms.fed.confirm") }}
          </el-button>
        </div>
      </div>
      <div id="J_Monitor2DSelectMap" class="select-map"></div>
    </div>
  </div>
</template>

<script>
import { MapRender, MapWorker } from "@geek_map/ts-map-fe/libs/monitor2d.min.js";

let mapRender = null;
let mapWorker = null;
export default {
  props: {
    value: [String, Array],
    multiple: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      visible: false,
      querySearchVal: "",
      cellCode: "",
    };
  },
  deactivated() {
    if (mapRender) {
      mapRender.destroy();
      mapRender = null;
    }
    if (mapWorker) {
      mapWorker.destroy();
      mapWorker = null;
    }
  },
  methods: {
    open() {
      this.visible = true;
      this.$nextTick(() => {
        const $dom = document.getElementById("J_Monitor2DSelectMap");
        if (!mapRender || !mapWorker) {
          $dom && this.initMap($dom);
        }
      });
    },
    confirm() {
      this.$emit("change", this.cellCode);
      this.visible = false;
    },
    handleClose() {
      if (!mapRender || !mapWorker) return;
      mapRender.clearSelects();
      this.querySearchVal = "";
      this.cellCode = "";
    },
    searchCellCode() {
      if (!mapRender || !mapWorker || !this.querySearchVal) return;
      const params = { layer: "cell", code: this.querySearchVal };
      const data = mapWorker.getQueryData(params);
      if (data?.cellCode) {
        mapRender.trigger("click", { cell: [data.cellCode] });
        mapRender.setEleCenter({ layer: "cell", code: data.cellCode });
      }
    },
    initMap($dom) {
      mapRender = new MapRender($dom);
      mapRender.ready(() => {
        mapWorker = new MapWorker(this.getWsUrl());
        mapWorker.onCallBack((dataType, data) => {
          if (dataType !== "wsInitFloors") return;
          mapRender && mapRender.renderFloors(data.floorsData);
        });
        mapWorker.init();
        mapWorker.reqFloors();
      });
      mapRender.rendered(renderType => {
        if (renderType !== "floorRendered") return;
        mapRender.triggerLayers(["cell"]);
      });
      mapRender.click(data => {
        if (data?.layer !== "cell") return;
        const code = data.code;
        this.querySearchVal = code;
        this.cellCode = code;
      });

      mapRender.init();
    },

    getWsUrl() {
      let protocol = window.location.protocol === "http:" ? "ws" : "wss";
      let hostname = window.location.host;
      if ($req.isDev) {
        hostname = new URL($req.API_URL).hostname;
      }

      const RMSPermission = $utils.Data.getRMSPermission();
      const token = RMSPermission ? `?token=${$utils.Data.getToken()}` : "";

      return `${protocol}://${hostname}/athena-monitor${token}`;
    },
  },
};
</script>

<style lang="less" scoped>
.map-select-dialog {
  width: 100%;
  position: fixed;
  z-index: 999;
  top: 0;
  background: rgba(0, 0, 0, 0.6);
  left: 0;
  height: 100%;
  .map-select-body {
    width: 80%;
    margin-top: 1.3rem;
    background: #fff;
    margin-left: 10%;
    height: calc(100vh - 2.6rem);
    position: relative;
    .title {
      .g-flex();
      font-weight: 700;
      height: 36px;
      padding: 6px 10px;
      border-bottom: 1px solid #eee;
    }
    .left-operate {
      position: absolute;
      width: 200px;
      top: 36px;
      left: 0;
      bottom: 0;
      margin: 0;
      padding: 10px 6px;
      border-right: 1px solid #eee;
    }
    .select-map {
      position: absolute;
      top: 36px;
      left: 200px;
      bottom: 0;
      right: 0;
      margin: 0;
      padding: 0;
    }
    .code-list {
      padding: 6px 0 50px;
      .el-tag {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .btn-group {
      position: absolute;
      .g-flex();
      justify-content: flex-end;
      padding-top: 10px;
      padding-right: 10px;
      bottom: 10px;
      width: 100%;
      left: 0;
    }
  }
}
</style>
