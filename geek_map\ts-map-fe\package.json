{"name": "ts-rms-map2d", "version": "1.0.0", "description": "极智嘉地图监控ts", "main": "./libs/monitor2d.min.js", "scripts": {"_untrack:git": "node ./build/tools/untrack_git.js", "_webpack:lib": "webpack --config ./build/webpack.lib.config.js --color", "_webpack:pro": "cross-env CONF_ENV=pro webpack --config ./build/webpack.config.js --color", "dev": "webpack --config ./build/webpack.lib.config.js --color --watch", "build:pro": "npm run _webpack:pro", "build:lib": "npm run _webpack:lib", "start": "cross-env CONF_ENV=serve webpack serve --config ./build/webpack.config.js --color"}, "repository": {"type": "git", "url": "http://gitlab.geekplus.cc/rms-fed/ts-map-fe.git"}, "author": "GeekPlus", "license": "ISC", "dependencies": {"antd": "^4.23.1", "axios": "^1.3.2", "i18next": "^21.9.1", "js-cookie": "^3.0.1", "js-event-bus": "^1.1.1", "js-md5": "^0.7.3", "moment": "^2.29.4", "pixi-viewport": "^4.34.4", "pixi.js": "^6.4.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^11.18.5", "react-router-dom": "^6.3.0"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@babel/runtime-corejs3": "^7.20.13", "@types/js-cookie": "^3.0.2", "@types/js-md5": "^0.4.3", "@types/node": "^18.13.0", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "autoprefixer": "^10.4.13", "babel-loader": "^9.1.2", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^4.2.2", "cssnano": "^5.1.14", "eslint": "^8.33.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-webpack-plugin": "^3.2.0", "fork-ts-checker-webpack-plugin": "^7.2.13", "html-webpack-plugin": "^5.5.0", "less": "^4.1.3", "less-loader": "^11.1.0", "mini-css-extract-plugin": "^2.7.2", "postcss-loader": "^7.0.2", "prettier": "^2.8.3", "sass": "^1.58.0", "sass-loader": "^13.2.0", "sass-resources-loader": "^2.2.5", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.4.2", "tslint": "^6.1.3", "typescript": "^4.9.5", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1"}}