import { usePureNumber, useCodeQualified, useRequired } from "@packages/hook/useRules";
import { NodeAttrEditConf } from "@packages/type/editUiType";
import DICT from "@packages/configure/dict";
import { useEditMap } from "@packages/hook/useEdit";

const editMap = useEditMap();
export const AREA_BASE_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  return {
    name: "base",
    tabTitle: "lang.rms.fed.basis",
    labelWidth: "80px",
    labelPosition: "left",
    formItem: [
      // 区域ID
      {
        prop: "areaId",
        label: "lang.rms.fed.region",
        component: "elInput",
        maxlength: 9,
        showWordLimit: true,
        disabled() {
          return true;
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) !== DICT.AREA_SHELF,
          };
        },
        rules: [usePureNumber()],
      },
      // 区域ID - 货架区域专属
      {
        prop: "areaId",
        label: "lang.rms.fed.region",
        component: "elSelect",
        appendAttrsFn(value: string, allData: any) {
          const areaDataList = editMap.value?.getLayerData("AREA") || [];
          const areaIdsMap = new Map();
          areaDataList.forEach(({ areaId }) => {
            areaIdsMap.set(areaId, {
              label: `${areaId}`,
              value: areaId,
            });
          });
          const dataVal = [...areaIdsMap.values()];
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_SHELF,
            data: dataVal,
          };
        },
        rules: [usePureNumber()],
      },
      // 区域编码
      {
        prop: "areaCode",
        label: "lang.rms.fed.mapArea.code",
        component: "elInput",
        maxlength: 9,
        showWordLimit: true,
        disabled: true,
        rules: [usePureNumber()],
      },
      // 区域名称
      {
        prop: "areaName",
        label: "lang.rms.fed.areaName",
        component: "elInput",
        maxlength: 13,
        showWordLimit: true,
        rules: [useRequired()],
      },
      // 区域描述
      {
        prop: "desc",
        label: "lang.rms.fed.areaDescript",
        component: "elSelect",
        showWordLimit: true,
        disabled: true,
        data: DICT.AREA_TYPE_DICT,
      },
      // 已选点位
      {
        prop: "cellLength",
        label: "lang.rms.fed.selctedPoint",
        component: "elInput",
        suffixName: "lang.rms.web.piece",
        disabled: true,
        get(data: any) {
          return data?.cellCodes?.length || 0;
        },
      },
      // 设备ID
      {
        prop: "deviceID",
        label: "lang.rms.fed.deviceID",
        component: "elInput",
        maxlength: 13,
        showWordLimit: true,
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.deviceID = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.deviceID || "";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_TRAFFIC_LIGHT,
          };
        },
      },
      // 系统急停ID
      {
        prop: "deviceID",
        label: "lang.rms.fed.function.systemStopID",
        labelWidth: "90px",
        component: "elInput",
        maxlength: 13,
        showWordLimit: true,
        set(value: string, allData: { [k: string]: any }) {
          allData.extendJson || (allData.extendJson = {});
          allData.extendJson.stopButtonId = value;
        },
        get(allData: { [k: string]: any }) {
          return allData.extendJson?.stopButtonId || "";
        },
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_STOP,
          };
        },
      },
      // 单行道类型
      {
        prop: "singleLaneType",
        label: "lang.rms.fed.singleWayType",
        labelWidth: "90px",
        component: "elSelect",
        data: DICT.SINGLE_LANG_TYPE,
        appendAttrsFn(value: string, allData: any) {
          console.log("areaType >>> ", allData, allData.areaType);
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_SINGLE_LANE,
          };
        },
      },
      // 空负载模式
      {
        prop: "isLoad",
        label: "lang.rms.fed.emptyLoadMode",
        labelWidth: "90px",
        component: "elSelect",
        data: DICT.SINGLE_LANG_LOADTYPE,
        appendAttrsFn(value: string, allData: any) {
          return {
            condition: (allData?.areaType || attrStore.drawAreaType) === DICT.AREA_SINGLE_LANE,
          };
        },
      },
    ],
  };
};
