/* ! <AUTHOR> at 2022/07/11 */
import MapCore from "./map/core";

class MapRender implements MRender.Main {
  private mapCore: MapCore;
  private mapReady: () => void;
  private mapRendered: (renderType: MRender.renderedType, data?: any) => void;
  private isFloorRender: boolean = false;
  constructor($dom: HTMLElement) {
    if (!$dom) throw new Error("没有dom");
    this.mapCore = new MapCore($dom);
  }
  init() {
    const mapReady = this.mapReady || null;
    this.mapCore.init(mapReady);
  }
  renderFloors(floorsData: floorsData): void {
    this.isFloorRender = true;
    this.mapCore.renderFloors(floorsData);
    this.mapRendered && this.mapRendered("floorRendered");
  }
  renderDisplays(displays: displays): void {
    this.mapCore.renderDisplays(displays);
    if (displays.isInitFinish) {
      this.mapRendered && this.mapRendered("displayRendered");
      this.isFloorRender = false;
    }
  }
  updateDisplays(displays: displays): void {
    if (this.isFloorRender) return;
    const timeMax = 30;
    const time = Date.now();

    this.mapCore.updateDisplays(displays);

    const intervalTime = Date.now() - time;
    let sleepTime;
    if (intervalTime >= timeMax) sleepTime = 10;
    else {
      sleepTime = timeMax - intervalTime;
      if (sleepTime < 10) sleepTime = 10;
    }
    setTimeout(() => {
      this.mapRendered && this.mapRendered("displayRendered");
    }, sleepTime);
  }

  renderArea(areaType: string, areaData: Array<any>, isInit = true) {
    switch (areaType) {
      case "STOP":
        this.mapCore.renderAreaStop(areaData, isInit);
        break;

      case "REAL_TIME_SPEED_LIMIT_AREA":
        this.mapCore.renderAreaSpeedLimit(areaData, isInit);
        break;

      //renderAreaSpeedLimit
    }
  }

  renderFeature(type: "deadRobotPath", data: any) {
    const mapCore = this.mapCore;
    switch (type) {
      case "deadRobotPath":
        // data 为robotCodes: Array<code>
        mapCore.mapData.deadRobot.setCodes(data || []);
        break;
    }
  }

  trigger(type: MRender.triggerType, arg?: MRender.layerElements | callback): void {
    switch (type) {
      case "click":
        this.mapCore.mapEvent.triggerClick(arg as MRender.layerElements);
        break;
      case "rect":
        this.mapCore.mapEvent.triggerRect(arg as callback);
        break;
      case "ranging":
        this.mapCore.mapEvent.triggerRanging(arg as callback);
        break;
      case "stop":
        this.mapCore.mapEvent.uninstall();
        break;
    }
  }

  triggerFastFilter(
    type: MRender.mapSearchFilterType,
    data: {
      action: "remove" | "render";
      cellCodes?: Array<code>;
      sizeType?: string;
      areaType?: string;
      areasData?: Array<any>;
    },
  ): void {
    const mapCore = this.mapCore;
    const action = data.action || "render";

    mapCore.renderCellFilter({ clear: true });
    mapCore.renderAreaFilter({ clear: true });
    if (action === "remove") return;

    let floorCells: { [propName: floorId]: mCellData[] };
    switch (type) {
      case "FUNCTIONAL_CELL":
        floorCells = mapCore.mapData.cell.getFloorDataByCodes(data.cellCodes);
        mapCore.renderCellFilter({ type, floorCells });
        break;
      case "CELL_SIZE_TYPE":
        floorCells = mapCore.mapData.cell.getFloorDataBySizeType(data.sizeType);
        mapCore.renderCellFilter({ type, floorCells });
        break;
      case "WAREHOUSE_AREA":
        if (data.areaType === "STOP") {
          mapCore.renderAreaStop(data.areasData, false);
        } else if (data.areaType === "REAL_TIME_SPEED_LIMIT_AREA") {
          mapCore.renderAreaSpeedLimit(data.areasData, false);
        } else {
          mapCore.renderAreaFilter({ areaData: data.areasData });
        }
        break;
    }
  }

  triggerLayers(layerNames: MRender.layerName[]): void {
    this.mapCore.triggerLayers(layerNames);
  }

  toggleLayer(
    layerNames: MRender.toggleLayerName,
    isShow: boolean,
    data?: shelfHeatApiData | Array<code>,
  ): void {
    this.mapCore.toggleLayer(layerNames, isShow, data);
  }
  clearSelects(layerName: MRender.layerName | "all" = "all", codes: Array<any> = []): void {
    this.mapCore.mapEvent.clearSelects(layerName, codes);
    this.mapCore.mapView.renderAll();
  }

  enableMultiClick(flag: boolean, layerNames?: Array<MRender.layerName>): void {
    let mapCore = this.mapCore;
    if (flag) {
      mapCore.mapEvent.removeDestCircle();
      mapCore.mapConfig.data.setMultiLayers(layerNames);
    }
    mapCore.mapConfig.setRenderConfig("isMultiSelect", flag);
  }
  enableScreenPositions(
    enable: boolean,
    params: { type: "stations" | "robots"; codes?: Array<code> },
  ): void {
    const mapCore = this.mapCore;
    const type = params?.type;
    switch (type) {
      case "stations":
        mapCore.mapConfig.setRenderConfig("showStationPop", enable);
        mapCore.mapView.renderAll();
        break;
    }
  }

  renderFeatureColor(params: MRender.mapColorParam) {
    const mapCore = this.mapCore;
    switch (params.type) {
      case "cell":
        mapCore.renderCellColor(params);
        break;
      case "shelf":
        mapCore.renderShelfColor(params);
        break;
    }
  }

  getMapColors(): MRender.MapColorConfig {
    return this.mapCore.utils.getConfig("color");
  }
  getSelectCodes(type: "all" | "cell" | "robot" = "all"): Array<code> {
    return this.mapCore.mapEvent.getSelectsByType(type);
  }

  setEleCenter(params: { layer: MRender.layerName; code: code }): void {
    this.mapCore.mapEvent.triggerCenter(params);
  }

  setMapPosition(mapPosition: MRender.mapPosition = null) {
    const mapCore = this.mapCore;
    if (!mapPosition) {
      mapPosition = mapCore.mapConfig.getRenderConfig("initMapPosition");
    }
    mapCore.mapView.setMapPosition(mapPosition);
  }
  setMapCenter(): void {
    this.mapCore.mapView.setMapCenter();
  }
  zoom(val: number): void {
    this.mapCore.mapView.zoom(val);
  }
  resize(): void {
    this.mapCore.mapView.resize();
  }
  rerender() {
    this.mapCore.mapView.renderAll();
  }
  repaint(): void {
    this.mapCore.repaint();
    this.isFloorRender = false;
  }
  destroy(): void {
    this.repaint();
    this.mapCore.destroy();
    this.mapCore = null;
  }

  ready(cb: () => void): void {
    this.mapReady = cb;
  }
  rendered(cb: (renderType: MRender.renderedType, data?: any) => void): void {
    this.mapRendered = cb;
    this.mapCore.mapView.setPositionChange(cb);
  }
  click(cb: (data: MRender.clickParams) => void): void {
    this.mapCore.mapEvent.setSelectCallback(cb);
  }
}

export default MapRender;
