/**
 * 默认的chart图形实例与展示(暂时)
 * 后续可能会废弃, 转为使用dashboard.js
 */
import * as echarts from "echarts";
import { DEF_OPTION, CHART_REQUEST_DEF, LINE_COLORS } from "./config";

class Chart {
  constructor(element, config = {}) {
    this.$chart = null;
    const { id, theme, echartsOptions, ...data } = config;
    this.id = id;
    this.data = data;
    this.theme = theme || 'dark';
    this.element = element;
    this.echartsOptions = echartsOptions || { height: 500 };

    if (element) this.createChart(element);
  }

  /**
 * 创建echarts实例, 并设置初始值
 * @param { Element } element 
 */
  createChart(element) {
    let $chart = echarts.init(element, this.theme, this.echartsOptions);
    $chart.setOption(DEF_OPTION);
    this.$chart = $chart;
  }

  setOption(options) {
    const replaceMerge = Object.keys(options);
    this.$chart && this.$chart.setOption(options, {
      replaceMerge
    });

    console.log(this.$chart.getOption())
  }

  resetChartData() {
    this.$chart.setOption(
      {
        title: { text: "" },
        xAxis: { data: [] },
        legend: { data: [] },
        series: [],
      },
      {
        replaceMerge: ["title", "xAxis", "legend", "series"],
      },
    );
  }

  resize() {
    this.$chart && this.$chart.resize();
  }

  /**
   * 销毁实例
   */
  destroy() {
    this.$chart && this.$chart.dispose();
    this.$chart = null;
  }

}

/**
 * ChartGroup 用于管理一组chart图表, 减少重复创建echarts实例
 */
class ChartGroup {
  /**
   * 初始化ChartGroup
   * @param {*} globalConfig 全局配置, 如果chart没有配置则自动集成group
   */
  constructor(globalConfig = {}) {
    this.$chartGroup = [];
    this.theme = globalConfig.theme || 'dark';
    this.echartsOptions = globalConfig.echartsOptions || { height: 500 };
    this.element = null;
  }

  setChartGroupElement(element) {
    this.element = element;
  }

  /**
   * 初始化一个chart实例, 并加入到group中
   * @param { Element } element 
   * @param { Object } config
   */
  addChart(config = {}) {
    const element = this.element;
    const appendChart = document.createElement('div');
    appendChart.style.height = '500px';
    element.appendChild(appendChart);

    const $chart = new Chart(appendChart, {
      ...config,
      id: config.id || `${parseInt(Math.random() * 10000)}_${+ new Date}`,
      theme: config.theme || this.theme,
      echartsOptions: config.echartsOptions || this.echartsOptions,
    });

    this.$chartGroup.push($chart);

    return $chart;
  }

  /**
   * 根据ID来卸载一个chart实例
   * @param {*} chartId 
   */
  removeChartById(chartId) {
    const index = this.$chartGroup.findIndex(item => item.id === chartId);
    if (index > -1) {
      const $chart = this.$chartGroup[index];
      // 销毁并卸载元素
      $chart.destroy();
      this.element.removeChild($chart.element);
      this.$chartGroup.splice(index, 1);
    }
  }

  // 仅更新某个图标
  updateChartById(chartId, options = {}) {
    const $chart = this.getChartById(chartId);
    if ($chart) {
      $chart.setOption(options);
    }
  }

  // 同时更新多个图表的数据
  // 没有则创建, 有则覆盖
  updateChartGroup(options) {
    const { $chartGroup } = this;
    const len = options.length;
    const groupLen = $chartGroup.length;

    $chartGroup.forEach(($chart, index) => {
      $chart.setOption(options[index]);
    });

    if (len >= groupLen) {
      // 新增
      options.slice(groupLen).forEach((item) => {
        this.addChart().setOption(item);
        
      });
    } else {
      // 销毁
      $chartGroup.slice(len).forEach(($chart) => {
        $chart.destroy();
      });
    }
  }

  /**
   * 根据自定义条件来查找chart实例Id
   * @param {*} callback 
   * @returns 
   */
  getChartId(callback) {
    return this.$chartGroup.find(callback);
  }

  getChartById(chartId) {
    return this.$chartGroup.find(item => item.id === chartId) || null;
  }

  _resolveChartData(data) {

  }

  resize() {
    this.$chartGroup.forEach($chart => {
      $chart.resize();
    });
  }

  destroy() {
    this.$chartGroup.forEach($chart => {
      $chart.destroy();
      this.element.removeChild($chart.element);
    });
    this.$chartGroup = [];
  }
}

export const resolveChartData = (data) => {
  const colorsP40 = [].concat(LINE_COLORS["a"]);
  const colorsRS = [].concat(LINE_COLORS["c"]);
  const colorsO = [].concat(LINE_COLORS["b"], LINE_COLORS["d"], LINE_COLORS["e"]);

  let xAxisData = []; // 横轴data
  let seriesObj = {}; // 折线数据Obj集合
  let defaultTypes = [];
  for (let index = 0, len = data.length; index < len; index++) {
    const item = data[index];
    if (!item.haveData) continue;

    xAxisData.push($utils.Tools.formatDate(item["snapshotTime"], "hh:mm:ss"));

    for (let key in item) {
      if (key == "snapshotTime" || key == "batchId" || key == "haveData") continue;

      let name = key;
      if (key.substring(0, 8) === "DEFAULT_") {
        name = key.substring(8);
        if (defaultTypes.indexOf(name) === -1) defaultTypes.push(name);
      }

      if (!seriesObj[name]) {
        seriesObj[name] = _getSingleSeries(name, colorsP40, colorsRS, colorsO);
      }
      seriesObj[name].data.push(item[key]);
    }
  }

  return { xAxisData, defaultTypes, seriesObj };
}

export const _getSingleSeries = (name, colorsP40, colorsRS, colorsO) => {
  let color;
  if (name.indexOf("P40") != -1) color = colorsP40.shift();
  else if (name.indexOf("RS") != -1) color = colorsRS.shift();
  else color = colorsO.shift();

  let singleSeries = {
    datasetId: name,
    name,
    showSymbol: false,
    type: "line",
    lineStyle: {
      width: 1,
    },
    data: [],
  };
  if (color) singleSeries.color = color;

  return singleSeries;
}

export const parseChartOptions = (config, options) => {
  if (config.dataKey === 'collet') {
    const jobMinuteGroupList = options.jobMinuteGroupList;
    return Object.keys(jobMinuteGroupList).map(key => {
      const item = jobMinuteGroupList[key];
      const xAxisData = Object.keys(item);
      xAxisData.sort((a, b) => a - b);
      return {
        xAxis: { type: 'category', data: xAxisData },
        yAxis: { type: 'value' },
        series: [
          {
            data: xAxisData.map(x => item[x]),
            type: 'bar',
          }
        ]
      }
    });
  } else {
    const data = options[options.dataKey] || [];
    const { xAxisData, defaultTypes, seriesObj } = resolveChartData(data);
    return [
      {
        title: { text: options.title },
        xAxis: { data: xAxisData },
        legend: { data: defaultTypes },
        series: defaultTypes.map(key => seriesObj[key]),
      }
    ];
  }
  // types
};

export default ChartGroup;