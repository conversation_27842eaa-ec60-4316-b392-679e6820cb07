export default {
  x: 24, 
  y: 41,
  width: 24, 
  height: 12,

  // 地图数据信息
  chart: {
    type: 'bar', // 图表类型

    // 地图数据来源
    request: {
      url: '/athena/stats/query/job/collect',  // 请求接口
      filters: ['date', 'showJobList', 'showJobGroupList', 'showJobGroupMinuteList', 'showJobMinuteIdList'], // 筛选条件
      defFilters: {
        date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
        showJobList: false,
        showJobGroupList: false,
        showJobGroupMinuteList: true,
        showJobMinuteIdList: true
      },
      timer: 5000,  // 轮询时间, 如果是0, 则不轮询
    },

    // 地图数据处理
    dataHandler: {  // 数据处理
      handler: 'getJobCollect',
      params: {
        type: 'bar',
        paramsName: 'rsJobStartMoveToDestLatticeCostHigh',
        title: 'rs开始移动到箱子到达目标货位'
      },
    },
  }
}