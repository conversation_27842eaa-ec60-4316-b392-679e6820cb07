<template>
  <div class="number-item">
    <h6>{{ title }}</h6>
    <span ref="content" :style="{ color }">
      <span class="title" :style="{ fontSize }">{{ number }}{{ append }}</span>
    </span>
  </div>
</template>

<script>
export default {
  name: "statisticsNumberItem",
  props: {
    option: {
      type: Object,
    },
  },
  data() {
    return {
      fontSize: '0px',
    }
  },
  computed: {
    number() {
      return this.option?.number || 0;
    },
    title() {
      return this.option?.title || "";
    },
    color() {
      return this.option?.color || "";
    },
    append() {
      return this.option?.append || "";
    }
  },
  mounted() {
    setTimeout(() => {
      const { offsetWidth } = this.$refs.content;
      this.fontSize = `${offsetWidth / 5}px`;
    }, 50)
  },
};
</script>

<style lang="less" scoped>
.number-item {
  border-radius: 3px;
  background: #fff;
  border: 1px solid #eee;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  > h6 {
    padding: 8px 12px;
    font-weight: 600;
    color: #666;
    text-align: center;
    font-size: 14px;
    border-bottom: 1px solid #eee;
  }
  > span {
    padding: 8px 12px;
    display: block;
    text-align: center;
    font-size: 30px;
    position: relative;
    flex: 1;

    .title {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
