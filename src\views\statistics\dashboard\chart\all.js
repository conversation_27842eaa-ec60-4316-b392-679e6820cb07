import TaskStatisticsCred from "./cred/taskStatisticsCred";
import TaskBeenIssuedCred from "./cred/taskBeenIssuedCred";
// import TaskNotExecutedCred from "./cred/taskNotExecutedCred";
import TaskCompleteCred from "./cred/taskCompleteCred";
import TaskCompletionRateBar from "./bar/taskCompletionRateBar";
// import TaskExecutionTimeCred from "./cred/taskExecutionTimeCred";
import TaskStationTotalNumBar from "./bar/taskStationTotalNumBar";
import TaskStationComStackBar from "./bar/taskStationComStackBar";
import RobotTotalCredByRs from "./cred/robotTotalCredByRs";
import RobotTotalCredByP40 from "./cred/robotTotalCredByP40";
import RobotWorkCredByP40 from "./cred/robotWorkCredByP40";
import RobotWorkCredByRs from "./cred/robotWorkCredByRs";
import RobotIdleCredByRs from "./cred/robotIdleCredByRs";
import RobotIdleCredByP40 from "./cred/robotIdleCredByP40";

import QRCodeLossRateTable from "./table/qRCodeLossRateTable";
import QRCodeLossRateLine from "./line/qRCodeLossRateLine";
import PlanPathNumCred from "./cred/planPathNumCred";
import AverageSpeedCred from "./cred/averageSpeedCred";
import AverageSpeedLine from "./line/averageSpeedLine";
import RobotChargingTable from "./table/robotChargingTable";
import StatAbnormalitiesTable from "./table/statAbnormalitiesTable"
import RobotChargingSumTable from "./table/robotChargingSumTable"
import RobotChargeBar from "./bar/robotChargeBar"
import RobotChargeNumBar from "./bar/robotChargeNumBar"
import RobotChargeLine from "./line/robotChargeLine"
import QRCodeBitLossRateTable from "./table/qRCodeBitLossRateTable";
import QRCodeBitLossRateLine from "./line/qRCodeBitLossRateLine";
/**
 * 未开发的图表
 * 2.1.3未执行任务
 * 2.1.4执行中任务
 * 2.1.6已取消任务
 * 2.1.8平均任务分配时长
 * 2.1.9平均任务执行时长
 * 2.1.10平均任务生命周期时长
 * 2.1.11平均取货架时长
 * 2.1.12平均送货架时长
 * 2.1.13平均还货架时长
 * 2.1.14平均排队时长
 * 2.1.15已取消任务平均执行耗时
 * 2.1.16平均任务距离
 * 2.1.19按任务类型统计任务总量
 * 2.1.20按任务类型统计已完成任务量
 * 2.1.21按巷道统计任务总量
 * 2.1.22巷道命中率分布
 * 2.1.23按货位类型统计任务量
 * 2.1.24货位命中率分布
 * 2.1.25任务分时吞吐量
 * 2.1.26Job数量
 * 2.1.27Job占比
 * 2.1.28库区货位命中率
 * 2.1.29缓存货位命中率
 * 2.1.30命中缓存货位执行耗时
 * 2.1.31命中库区货位执行耗时
 * 2.1.32任务执行阶段平均耗时分布
 * 2.1.33任务执行阶段平均里程分布
 * 2.1.34按任务类型分布
 * 2.1.35机器人大任务执行时间分布
 * 2.1.36任务创建到分配机器人时间分布
 * 2.2.1机器人利用率(部分)
 * 2.2.2机器人工作状态
 * 2.3.3路径重规划占比
 * 2.3.4实际路径/A*路径比
 * 2.3.8路径规划比率
 * 2.4.1货位状态总体分布
 * 2.4.2库区货位状态分布
 * 2.4.3缓存货位状态分布
 * 2.4.4货位占用明细表
 */

export default [
  new TaskStatisticsCred({ width: 8, height: 6 }),
  new TaskBeenIssuedCred({ width: 8, height: 6 }),
  // new TaskNotExecutedCred({ width: 8, height: 6 }),
  new TaskCompleteCred({ width: 8, height: 6 }),
  new TaskCompletionRateBar({ width: 8, height: 6 }),
  // new TaskExecutionTimeCred({ width: 8, height: 6 }),
  new TaskStationTotalNumBar({ width: '50%', height: '300px' }),
  new TaskStationComStackBar({ width: '50%', height: '300px' }),
  new RobotTotalCredByRs({ width: 8, height: 6 }),
  new RobotTotalCredByP40({ width: 8, height: 6 }),
  new RobotTotalCredByRs({ width: 8, height: 6 }),
  new RobotWorkCredByP40({ width: 8, height: 6 }),
  new RobotWorkCredByRs({ width: 8, height: 6 }),
  new RobotIdleCredByRs({ width: 8, height: 6 }),
  new RobotIdleCredByP40({ width: 8, height: 6 }),
  new PlanPathNumCred({ width: 8, height: 6 }),
  new AverageSpeedCred({ width: 8, height: 6 }),
  new AverageSpeedLine({ width: '50%', height: '300px' }),
  new QRCodeLossRateTable({ width: '50%', height: '300px' }),
  new QRCodeLossRateLine({ width: '50%', height: '300px' }),
  new QRCodeBitLossRateTable({ width: '50%', height: '300px' }),
  new QRCodeBitLossRateLine({ width: '50%', height: '300px' }),
  new RobotChargingTable({ width: '50%', height: '300px' }),
  new StatAbnormalitiesTable({ width: '50%', height: '300px' }),
  new RobotChargingSumTable({ width: '50%', height: '300px' }),
  new RobotChargeBar({ width: '50%', height: '300px' }),
  new RobotChargeNumBar({ width: '50%', height: '300px' }),
  new RobotChargeLine({ width: '50%', height: '300px' }),
]