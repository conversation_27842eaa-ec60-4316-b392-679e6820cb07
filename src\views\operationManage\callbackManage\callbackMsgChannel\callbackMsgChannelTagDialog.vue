<template>
  <el-dialog
    :title="$t('lang.rms.fed.callbackConfigChannelTag')"
    :visible.sync="channelTagDialog"
    width="80%"
    :before-close="closeDialog"
    center
  >
    <div class="app-container">
      <el-card>
        <el-form
          label-position="top"
          label-width="80px"
          :model="searchData"
        >
          <el-row :gutter="20">
            <el-col :span="4" class="btnwarp">
              <!--<el-button type="primary" @click="onSearch()">{{ $t('lang.rms.fed.query') }}</el-button>
              <el-button @click="resetSearchData">{{ $t('lang.rms.fed.reset') }}</el-button>-->
              <el-button @click="itemClick({}, false)">{{ $t('lang.rms.fed.add') }}</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <el-dialog
        :title="$t('lang.rms.fed.edit')"
        :visible.sync="editDialog"
        center
        :append-to-body="true"
        width="50%"
      >
        <el-form
          ref="ruleForm"
          label-position="top"
          label-width="80px"
          :model="itemData"
          class="padding_20"
        >
          <el-form-item :label="$t('lang.rms.fed.channelId')">
            <el-input
              v-model="propsChannelId"
              type="text"
              readonly="readonly"
            />
          </el-form-item>
          <el-form-item
            :label="$t('lang.rms.fed.callbackChannelTagProperty')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
            prop="property"
          >
            <el-input v-model="itemData.property" type="text" />
          </el-form-item>
          <el-form-item
            :label="$t('lang.rms.fed.callbackChannelTagFilterType')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseChoose') }]"
            prop="filterType"
          >
            <el-select v-model="itemData.filterType">
              <el-option
                v-for="(item, key) in filterTypeList"
                :key="key"
                :label="item"
                :value="key"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('lang.rms.fed.callbackChannelTagMatchType')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseChoose') }]"
            prop="matchType"
          >
            <el-select v-model="itemData.matchType">
              <el-option
                v-for="(item, key) in matchTypeList"
                :key="key"
                :label="item"
                :value="key"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('lang.rms.fed.callbackChannelTagPattern')"
            :rules="[{ required: true, message:$t('lang.rms.fed.pleaseEnter') }]"
            prop="pattern"
          >
            <el-input v-model="itemData.pattern" type="text" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="closeEditDialog">{{ $t('lang.common.cancel') }}</el-button>
          <el-button type="primary" @click="save">{{ $t('lang.rms.fed.save') }}</el-button>
        </span>
      </el-dialog>

      <el-card class="mt-20">
        <!-- 列表信息 -->
        <el-table :data="tableData" style="width: 100%">
          <el-table-column
            :label="$t('lang.rms.fed.lineNumber')"
            width="50"
            align="center"
            :formatter="formatIndex"
          />
          <!--通道ID-->
          <el-table-column
            prop="channelId"
            :label="$t('lang.rms.fed.channelId')"
            align="center"
          />
          <el-table-column
            prop="property"
            :label="$t('lang.rms.fed.callbackChannelTagProperty')"
            align="center"
          />
          <!-- 过滤类型 -->
          <el-table-column
            prop="filterType"
            :label="$t('lang.rms.fed.callbackChannelTagFilterType')"
            align="center"
          >
            <template slot-scope="scope">{{ filterTypeList[scope.row.filterType] }}</template>
          </el-table-column>
          <!-- 值匹配类型 -->
          <el-table-column
            prop="matchType"
            :label="$t('lang.rms.fed.callbackChannelTagMatchType')"
            align="center"
          >
            <template slot-scope="scope">{{ matchTypeList[scope.row.matchType] }}</template>
          </el-table-column>
          <!-- 匹配值 -->
          <el-table-column
            prop="pattern"
            :label="$t('lang.rms.fed.callbackChannelTagPattern')"
            align="center"
          />

          <el-table-column :label="$t('lang.rms.fed.operation')" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="itemClick(scope.row, true)"
              >
                {{ $t('lang.rms.fed.edit') }}
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="deleteData(scope.row, true)"
              >
                {{ $t('lang.rms.fed.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageOption.pagecurrent"
          :page-sizes="[10, 25, 50, 100]"
          :page-size="pageOption.pagesize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="taskCount"
          @size-change="pageSizeChange"
          @current-change="goPage"
        />
      </el-card>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'CallbackChannelTag',
  components: {},
  props: {
    channelTagDialog: {
      type: Boolean,
      default() {
        return false
      }
    },
    itemChannelData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      // 搜索内容
      searchData: {
        channelId: '',
        channelType: ''
      },

      // 设置table的数据
      tableData: [],
      // 当前数据总数
      taskCount: 0,
      pageOption: {
        // 当前页数
        pagecurrent: 1,
        // 每页展示数
        pagesize: 10
      },
      itemData: {},
      filterTypeList: {
        AND: 'AND',
        OR: 'OR',
        NOT: 'NOT'
      },
      matchTypeList: {
        REGEX: this.$t('lang.rms.fed.callbackChannelTagMatchTypeRegex'),
        EQUAL: this.$t('lang.rms.fed.callbackChannelTagMatchTypeEqual')
      },
      isEdit: false,
      editDialog: false,
      propsChannelId: undefined
    }
  },
  computed: {},
  watch: {
    itemChannelData(data) {
      this.propsChannelId = data.channelId
    }
  },
  created() {
    this.getTableList()
    this.propsChannelId = this.itemChannelData.channelId
  },
  methods: {
    /* 格式化内容 */
    formatterTags(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue.join(',')
      } else {
        return cellValue
      }
    },

    formatImmediate(row, column, cellValue, index) {
      const { immediateList } = this
      for (let index = immediateList.length - 1; index >= 0; index -= 1) {
        if (immediateList[index].key === cellValue + '') {
          return immediateList[index].value
        }
      }
      return cellValue
    },

    formatIndex(row, column, cellValue, index) {
      return ((this.pageOption.pagecurrent - 1) * this.pageOption.pagesize || 0) + index + 1
    },

    /* 翻页 */
    pageSizeChange(data) {
      // this.pageOption.pagecurrent = 1
      this.pageOption.pagesize = data
      this.getTableList()
    },
    // 跳页
    goPage(pageCurrent) {
      this.pageOption.pagecurrent = pageCurrent
      this.getTableList()
    },

    // 编辑
    itemClick(data, isEdit) {
      this.isEdit = isEdit
      this.editDialog = true
      // 编辑前先重置属性框
      this.$nextTick(() => {
        this.$refs['ruleForm'].resetFields()
        this.itemData = data
      })
    },
    // 机器人参数配置
    itemRobotRecord(data) {
      this.itemData = data
    },
    deleteData(data) {
      const msgChannelTagIds = []
      msgChannelTagIds.push(data.id)
      const para = { msgChannelTagIds }
      $req.post('/athena/apiCallback/deleteMsgChannelTag', para).then(json => {
        if (json.code === 0) {
          this.$message.success(this.$t(json.msg))
          this.getTableList()
        }
      })
    },
    // 重置搜索参数
    resetSearchData() {
      for (const key in this.searchData) {
        if (Object.prototype.hasOwnProperty.call(this.searchData, key)) {
          this.searchData[key] = ''
        }
      }
      this.onSearch()
    },
    // 查询
    onSearch() {
      this.pageOption.pagecurrent = 1
      this.getTableList()
    },
    getTableList() {
      const data = { language: $utils.Data.getLocalLang() }
      const pageData =
        '?currentPage=' + this.pageOption.pagecurrent + '&pageSize=' + this.pageOption.pagesize
      data.channelId = this.itemChannelData.channelId
      $req.post('/athena/apiCallback/msgChannelTagPageList' + pageData, data).then((data = {}) => {
        const { currentPage = 0, pageSize = 0, recordCount = 0, recordList = [] } = data.data || {}
        this.tableData = recordList.map(item => {
          const descr = this.$t(item.descr)
          return { ...item, descr }
        })
        // id
        this.pageOption.pagesize = pageSize
        this.pageOption.pagecurrent = currentPage === 0 ? 1 : currentPage
        this.taskCount = recordCount
      })
    },
    // 保存通道规则
    save() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          const data = this.itemData
          data.channelId = this.itemChannelData.channelId
          data.msgChannelTagId = this.itemData.id
          $req.post('/athena/apiCallback/saveMsgChannelTag', data).then(json => {
            if (json.code === 0) {
              this.$message.success(this.$t(json.msg))
              this.editDialog = false
              this.getTableList()
            }
          })
        }
      })
    },
    closeDialog() {
      this.$emit('update:channelTagDialog', false)
    },
    closeEditDialog() {
      this.editDialog = false
    }
  }
}
</script>
<style scoped>
.mt-20 {
  margin-top: 20px;
}

.btnwarp {
  padding: 43px 0 0;
}
</style>
