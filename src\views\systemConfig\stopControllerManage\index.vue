<template>
  <div class="app-container ui-box">
    <!-- 查询条件 -->
    <el-card>
      <el-form ref="searchForm" :inline="true" label-position="top" :model="searchForm">
        <el-form-item :label="$t('lang.rms.fed.controllerId')" prop="deviceId">
          <el-input
            v-model.trim="searchForm.deviceId"
            :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterBusinessNo')"
          />
        </el-form-item>
        <el-form-item class="search-form">
          <el-button type="primary" @click="submit">{{ $t("lang.rms.fed.query") }}</el-button>
          <el-button @click="resetForm">{{ $t("lang.rms.fed.reset") }}</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <!-- 按钮操作 -->
    <div class="tr mt20 mb20">
      <!-- 新增   -->
      <el-button type="primary" class="w100 mr10" :loading="addBtnLoading" @click="addItem">
        {{ $t("lang.rms.fed.addController") }}
      </el-button>
    </div>
    <!-- 查询列表 -->
    <el-card>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column type="index" :label="$t('lang.rms.fed.listSerialNumber')" />
        <el-table-column :label="$t('lang.rms.fed.controllerId')" prop="deviceId" />
        <el-table-column :label="$t('lang.rms.fed.IPAdress')" prop="ip" />
        <el-table-column :label="$t('lang.rms.fed.boundLogicIdArea')" prop="referBy">
          <template slot-scope="scope">
            {{ scope.row.referBy === "null" ? "" : scope.row.referBy }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('lang.rms.fed.channelAndWorkstation')" prop="channelsStr" />
        <el-table-column
          v-if="!isRoleGuest"
          fixed="right"
          :label="$t('lang.rms.fed.textOperation')"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="editItem(scope.row)">
              {{ $t("lang.rms.fed.buttonEdit") }}
            </el-button>
            <el-popover v-model="scope.row.visible" placement="top">
              <p style="padding: 5px 10px">
                <i class="el-icon-warning" style="color: #e6a23c" />
                {{ $t("lang.rms.fed.pleaseConfirmDeleteController") }}?
              </p>
              <div style="text-align: right; margin: 0">
                <el-button size="mini" @click="scope.row.visible = false">
                  {{ $t("lang.rms.fed.cancel") }}
                </el-button>
                <el-button type="primary" size="mini" @click="deleteItem(scope.row)">
                  {{ $t("lang.rms.fed.confirm") }}
                </el-button>
              </div>
              <el-button slot="reference" type="text" @click.stop>
                {{ $t("lang.rms.fed.delete") }}
              </el-button>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 新增 编辑弹框 -->
    <edit-dialog
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :mode="dialogMode"
      :init-row="dialogInitRow"
      @saveSuccess="dialogSaveSuccess"
    />
  </div>
</template>
<script>
import { queryBaseDeviceList, queryBaseDeviceDelete } from "@/api/stopControllerManage";
import editDialog from "./components/editDialog";

export default {
  name: "StopControllerManage",
  components: {
    editDialog,
  },
  data() {
    return {
      addBtnLoading: false,
      // form表单
      // table相关配置
      tableData: [],
      pageData: {
        currentPage: 1,
        pageSize: 10,
        recordCount: 0,
      },
      // dialog相关配置
      dialogVisible: false,
      dialogMode: "add",
      dialogInitRow: {},
      visible: false,
      searchForm: {},
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  async created() {
    this.queryList();
  },
  methods: {
    addItem() {
      this.dialogVisible = true;
      this.dialogMode = "add";
      this.dialogInitRow = {};
    },

    editItem(row) {
      this.dialogInitRow = row;
      this.dialogVisible = true;
      this.dialogMode = "edit";
    },

    submit() {
      this.queryList(this.searchForm);
    },

    resetForm() {
      this.searchForm = {};
      this.queryList();
    },
    deleteItem(row) {
      queryBaseDeviceDelete({ id: row.id }).then(res => {
        const { code } = res;
        if (code) return;
        this.queryList();
        this.visible = false;
      });
    },
    dialogSaveSuccess() {
      this.queryList();
    },

    queryList(queryInfo) {
      queryBaseDeviceList(queryInfo).then(res => {
        const {
          code,
          data: { data },
        } = res;
        if (code) return;
        this.tableData = data;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.ui-box {
  padding: 15px;
}
</style>

<style lang="scss">
.mr10 {
  margin-right: 10px;
}
.ui-mytable .ui-mytable__header {
  background-color: #fff !important;
}
.search-form {
  margin-top: 33px;
}
</style>
