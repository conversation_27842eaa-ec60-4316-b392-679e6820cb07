<template>
  <div class="dashboard" ref="dashboardRef" v-loading="isDataLoad">
    <template v-if="!isDataLoad">
      <DashboardChartView
        v-for="(item, index) in options"
        :key="index"
        :disabled="item.isActive || !disabled"
        :option="item"
        :gridWidth="gridWidth"
      />
    </template>
  </div>
</template>

<script>
import DashboardChartView from './dashboard-chart.view.vue'

export default {
  name: "chartViewMain",
  props: {
    options: {
      type: Array
    }
  },
  components: { DashboardChartView },
  data() {
    return {
      width: 0,
      gridNumber: 48,
      isDataLoad: true,
      disabled: true,
    };
  },
  mounted() {
    this.resize();
    this.isDataLoad = false;
    window.addEventListener('resize', this.resize);
  },
  computed: {
    gridWidth() {
      return this.width / this.gridNumber;
    },
  },
  destroyed() {
    window.removeEventListener('resize', this.resize);
  },
  methods: {
    resize() {
      this.width = this.$refs.dashboardRef.offsetWidth;
    }
  },
};
</script>

<style lang="less" scoped>
.dashboard {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow-x: hidden;
  position: relative;
  padding-bottom: 30px;
}
</style>
