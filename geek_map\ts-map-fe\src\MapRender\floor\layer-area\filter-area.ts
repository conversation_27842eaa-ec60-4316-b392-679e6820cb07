/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

class LayerFilterArea implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  private fillStyle: any = new PIXI.FillStyle();
  private lineStyle: any = new PIXI.LineStyle();
  private meshList: Array<any> = [];
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  init(): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "areaOther";
    container.interactiveChildren = false;
    container.visible = true;
    container.alpha = 0.7;
    container.zIndex = utils.getLayerZIndex("area");
    this.container = container;

    this.fillStyle.visible = true;
    this.fillStyle.color = 0xff0000;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  render(arr: Array<any>): void {
    let item;

    let sum = 0;
    let graphicsGeometry: any = new PIXI.GraphicsGeometry();

    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      if (item["cellCodes"]) {
        this.drawAreaCellCodes(item["cellCodes"], graphicsGeometry, sum);
      } else if (item["polygons"]) {
        this.drawAreaPolygon(item["polygons"], graphicsGeometry, sum);
      }
    }

    graphicsGeometry.BATCHABLE_SIZE = sum;
    const graphics: any = new PIXI.Graphics(graphicsGeometry);
    graphics.name = "areaOther";
    graphics.mapType = "areaOther";
    graphics.interactive = graphics.buttonMode = false;

    this.meshList.push(graphics);
    this.container.addChild(graphics);
  }

  update(arr: Array<code>) {}

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
    this.fillStyle = null;
    this.lineStyle = null;
    this.meshList = null;
  }

  private drawAreaCellCodes(cellCodes: Array<code>, graphicsGeometry: any, sum: number) {
    const _this = this;
    const mapData = _this.mapCore.mapData;
    const fillStyle = _this.fillStyle;
    const lineStyle = _this.lineStyle;

    sum += cellCodes.length;

    let options: mCellData, nsPosition, rect;
    for (let i = 0, len = cellCodes.length; i < len; i++) {
      options = mapData.cell.getData(cellCodes[i]);
      if (!options) continue;
      nsPosition = options["nsPosition"];

      rect = new PIXI.Polygon(...nsPosition);
      graphicsGeometry.drawShape(rect, fillStyle, lineStyle);
    }
  }

  private drawAreaPolygon(polygons: Array<any>, graphicsGeometry: any, sum: number) {
    const fillStyle = this.fillStyle;
    const lineStyle = this.lineStyle;

    let result = [];
    let point;
    for (let i = 0, len = polygons.length; i < len; i++) {
      point = polygons[i];
      result.push(point.x);
      result.push(-point.y);
    }

    const polygon = new PIXI.Polygon(result);
    graphicsGeometry.drawShape(polygon, fillStyle, lineStyle);
  }
}
export default LayerFilterArea;
