/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseCircleFilled } from "@ant-design/icons";
import { Input, Radio } from "antd";
import { getMap2D, $eventBus } from "../../../../singleton";

import ChargerAbnormal from "./charger-abnormal";
import ChargerCommon from "./charger-common";

const { Search } = Input;
type PropsOrderData = {
  isCurrent: boolean;
};
let QueryTimmer: any = null
function OrderCharger(props: PropsOrderData) {
  const { t } = useTranslation();
  const [chargerId, setChargerId] = useState("");
  const [chargerData, setChargerData] = useState<chargerData>(null);
  const [operate, setOperate] = useState<"common" | "abnormal">("common");
  const [allowedFullChargeStatus, setAllowedFullChargeStatus] = useState("DISABLED");

  // 接收wsDataQuery
  useEffect(() => {
    if (!props.isCurrent) return;
    $eventBus.on("wsDataQueryRightTab", wsData => {
      const { params, data } = wsData;
      if (!wsData || params?.layer !== "charger") return;
      if (data) setChargerData(data);
      else setChargerData(null);
    });
    return () => {
      $eventBus.off("wsDataQueryRightTab");
    };
  }, [props.isCurrent]);

  const queryMonitorCharger = () => {
    console.log(QueryTimmer, chargerId)
    clearTimeout(QueryTimmer)
    QueryTimmer = null
    const sleepTime = 1500
    if (!chargerId) {
      QueryTimmer = setTimeout(queryMonitorCharger, sleepTime)
      return
    }
    const url = "/athena/equipment/charger/monitorCharger?chargerId=" + chargerId;
    _$utils.reqGet(url).then(res => {
      const data = res?.data || {};
      let status = ""
      if (data && data.allowedFullCharge) {
        status = data.allowedFullCharge
      } else {
        status = 'DISABLED'
      } 
      setAllowedFullChargeStatus(status)
      QueryTimmer = setTimeout(queryMonitorCharger, sleepTime)
      console.log(11111)
    });
  };

  // 地图点击
  useEffect(() => {
    if (!props.isCurrent) return;

    const map2D = getMap2D();
    $eventBus.on("mapClick", (data: MRender.clickParams) => {
      const layer = data?.layer;
      if (layer !== "charger") return;

      setChargerData(null);
      setChargerId(data.code.toString());
      map2D.mapWorker.reqQuery({ layer, code: data.code });
    });

    return () => {
      $eventBus.off("mapClick");
      clearTimeout(QueryTimmer)
      QueryTimmer = null
    };
  }, [props.isCurrent]);

  useEffect(() => {
    if (!props.isCurrent) return;

    queryMonitorCharger();
    return () => {
      clearTimeout(QueryTimmer)
      QueryTimmer = null
    };
  }, [chargerId]);

  // 清空数据 地图切换可点击层
  useEffect(() => {
    if (!props.isCurrent) return;

    const map2D = getMap2D();
    map2D.mapRender.triggerLayers(["charger"]);
    return () => {
      clearCharger();
    };
  }, [props.isCurrent]);

  // 充电站清除
  const clearCharger = () => {
    setChargerId("");
    setChargerData(null);
    setOperate("common");
    const map2D = getMap2D();
    map2D.mapWorker.stopQuery();
    map2D.mapRender.clearSelects();
  };

  const chargerSearch = (value: string) => {
    if (!value) return;
    const map2D = getMap2D();
    map2D.mapWorker.reqQuery({ layer: "charger", code: value });
    map2D.mapRender.trigger("click", { charger: [value] });
    map2D.mapRender.setEleCenter({ layer: "charger", code: value });
    // queryMonitorCharger();
  };

  return (
    <div
      style={props.isCurrent ? {} : { transform: "translate(-9999px, 0px)", position: "absolute" }}
      className="map2d-order-group-component"
    >
      <div className="panel-right-title">{t("lang.rms.fed.tabChargingStationManagement")}</div>
      <Search
        value={chargerId}
        placeholder={t("lang.rms.fed.chargerId")}
        onSearch={chargerSearch}
        enterButton
        allowClear={{ clearIcon: <CloseCircleFilled onClick={() => clearCharger()} /> }}
        onChange={(e) => {
          setChargerId(e.target.value)
        }}
      />

      <Radio.Group
        onChange={({ target: { value } }) => setOperate(value)}
        value={operate}
        optionType="button"
        className="charger-radio"
      >
        <Radio value="common">{t("lang.rms.fed.tabCommonlyUsed")}</Radio>
        <Radio value="abnormal">{t("lang.rms.fed.textAbnormal")}</Radio>
      </Radio.Group>

      <ChargerCommon visible={operate === "common"} charger={chargerData} allowedFullChargeStatus={allowedFullChargeStatus}/>
      <ChargerAbnormal visible={operate === "abnormal"} errors={chargerData?.errorList || null} />
    </div>
  );
}

export default OrderCharger;
