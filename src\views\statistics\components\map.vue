<template>
  <section style="height: calc(100% - 32px); position: relative">
    <map-loading v-if="loading" />
    <div class="map-tools">
      <el-date-picker
        v-model="dataRange"
        type="datetimerange"
        size="mini"
        align="right"
        :start-placeholder="$t('lang.rms.fed.startTime')"
        :end-placeholder="$t('lang.rms.fed.endTime')"
        :default-time="['00:00:00', '23:59:59']"
      />
      <el-select v-model="heatType" size="mini" :placeholder="$t('lang.rms.fed.pleaseChoose')">
        <el-option v-for="item in heatTypes" :key="item.value" :label="$t(item.label)" :value="item.value" />
      </el-select>
      <el-select v-model="robotType" size="mini" :placeholder="$t('lang.rms.fed.pleaseChoose')">
        <el-option v-for="item in robotTypes" :key="item.value" :label="$t(item.label)" :value="item.value" />
      </el-select>

      <el-button type="primary" size="mini" @click="getHeatData">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </div>
    <div id="J_Monitor2DMapBox" class="map-box"></div>

    <div class="tu-li-list">
      <p v-for="(item, key) in heatColors" :key="key">
        <span :style="{ background: `#${item.toString(16)}` }" />
        <label>{{ gradientRange[key] ? gradientRange[key].join(" ~ ") : "--" }}</label>
      </p>
    </div>
  </section>
</template>

<script>
import MapLoading from "../../components/map-loading.vue";
import { MapRender, MapWorker } from "@geek_map/ts-map-fe/libs/monitor2d.min.js";

let mapRender = null;
let mapWorker = null;
export default {
  name: "statisticsMap",
  components: { MapLoading },
  data() {
    const now = new Date();
    const y = now.getFullYear();
    const m = now.getMonth() + 1;
    const d = now.getDate();
    return {
      loading: false,
      heatTypes: [],
      robotTypes: [],
      gradientRange: {},
      heatColors: {},

      dataRange: [`${y}-${m}-${d} 00:00:00`, `${y}-${m}-${d} 23:59:59`],
      robotType: "ALL",
      heatType: "",
    };
  },
  mounted() {
    const $dom = document.getElementById("J_Monitor2DMapBox");
    if (!mapRender || !mapWorker) {
      $dom && this.initMap($dom);
    }
    this.getHeatTypes();
    this.getRobotTypes();
  },
  destroyed() {
    if (mapRender) {
      mapRender.destroy();
      mapRender = null;
    }
    if (mapWorker) {
      mapWorker.destroy();
      mapWorker = null;
    }
  },
  methods: {
    getHeatData() {
      const dataRange = this.dataRange;
      const heatType = this.heatType;
      if (!dataRange || !dataRange[0] || !dataRange[1]) {
        this.$warning(this.$t("lang.rms.fed.chooseTimeRange"));
        return;
      }
      if (!heatType) {
        this.$warning(this.$t("lang.rms.fed.pleaseChoose"));
        return;
      }

      mapRender.renderFeatureColor({ type: "cell", clear: true });
      mapRender.rerender();
      this.gradientRange = {};

      $req
        .post("/athena/map/monitor/stat/heat", {
          startTime1: new Date(dataRange[0]).getTime(),
          endTime1: new Date(dataRange[1]).getTime(),
          statType: heatType,
        })
        .then(res => {
          const data = res?.data?.dataMap || {};
          const robotType = this.robotType;
          this.gradientRange = res?.data?.gradientRange || {};

          let cells1;
          if (robotType === "ALL") cells1 = data?.time1["DEFAULT"] || {};
          else cells1 = data?.time1[robotType] || {};

          let fCells = {};
          cells1.forEach(cell => {
            if (!fCells[cell.gradient]) fCells[cell.gradient] = [];
            fCells[cell.gradient].push(cell.code);
          });

          for (let key in fCells) {
            mapRender.renderFeatureColor({
              type: "cell",
              color: this.heatColors[key],
              codes: fCells[key],
            });
          }
          mapRender.rerender();
        });
    },

    initMap($dom) {
      mapRender = new MapRender($dom);
      const color = mapRender.getMapColors();
      this.heatColors = color.HOT_CONF;
      mapRender.ready(() => {
        mapWorker = new MapWorker(this.getWsUrl());
        mapWorker.onCallBack((dataType, data) => {
          if (dataType !== "wsInitFloors") return;
          mapRender && mapRender.renderFloors(data.floorsData);
        });
        mapWorker.init();
        mapWorker.reqFloors();
      });
      mapRender.rendered(renderType => {
        if (renderType !== "floorRendered") return;
        this.loading = false;
      });

      mapRender.init();
    },

    getHeatTypes() {
      $req.get("/athena/map/monitor/stat/getAllStatTypes").then(res => {
        const data = res.data || {};

        let types = [];
        for (let key in data) {
          types.push({
            label: data[key],
            value: key,
          });
        }
        this.heatTypes = types;
      });
    },

    getRobotTypes() {
      $req.get("/athena/map/monitor/stat/getAllRobotTypes").then(res => {
        const data = res.data || [];

        this.robotTypes = [{ label: "ALL", value: "ALL" }].concat(
          data.map(item => {
            return {
              label: item,
              value: item,
            };
          }),
        );
      });
    },
    hexToRgb(val) {
      //HEX十六进制颜色值转换为RGB(A)颜色值
      // 16进制颜色值的正则
      var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
      // 把颜色值变成小写
      var color = val.toLowerCase();
      var result = "";
      if (reg.test(color)) {
        // 如果只有三位的值，需变成六位，如：#fff => #ffffff
        if (color.length === 4) {
          var colorNew = "#";
          for (var i = 1; i < 4; i += 1) {
            colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
          }
          color = colorNew;
        }
        // 处理六位的颜色值，转为RGB
        var colorChange = [];
        for (var i = 1; i < 7; i += 2) {
          colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
        }
        result = "rgb(" + colorChange.join(",") + ")";
        return { rgb: result, r: colorChange[0], g: colorChange[1], b: colorChange[2] };
      } else {
        result = "无效";
        return { rgb: result };
      }
    },

    getWsUrl() {
      let protocol = window.location.protocol === "http:" ? "ws" : "wss";
      let hostname = window.location.host;
      if ($req.isDev) {
        hostname = new URL($req.API_URL).hostname;
      }

      const RMSPermission = $utils.Data.getRMSPermission();
      const token = RMSPermission ? `?token=${$utils.Data.getToken()}` : "";

      return `${protocol}://${hostname}/athena-monitor${token}`;
    },
  },
};
</script>

<style lang="less" scoped>
@tool-height: 36px;
.map-tools {
  height: @tool-height;
  line-height: @tool-height - 4;
  padding: 0 5px;
  margin-top: -4px;
  .g-box-shadow-no-top();
}
.map-box {
  position: absolute;
  top: @tool-height;
  left: 0;
  bottom: 0;
  right: 0;
  margin: 0;
  padding: 0;
}
.tu-li-list {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 3px 6px;
  .g-box-shadow-no-top();
  background: #fff;
  opacity: 0.6;
  p {
    .g-flex();
    justify-content: flex-start;
    margin: 3px 0;
    span {
      display: inline-block;
      margin-right: 5px;
      width: 22px;
      height: 22px;
    }
    label {
      font-size: 14px;
    }
  }
}
</style>
