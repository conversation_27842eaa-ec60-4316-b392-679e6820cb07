import Selected from "./Selected";
import EventBus from "../eventBus/EventBus";
import {getId} from '../utils/utils'
export default class SingleSelected {
  constructor() {

  }
  //选中触发
  selected(e){
    const target = e.event.target
    if(target && target.name !== 'viewport'){
      const $el = (target.isSprite || target.isSingleLane) ? target : target.parent
      // const {nodeId,segmentId,id} = $el
      // const selectedId = nodeId || segmentId || id
      const selectedId = getId($el)
      Selected.renderSelected(selectedId,$el)
    }else{
      Selected.resetAllSelected()
      //点击空白回调
      EventBus.$emit('selected',null)
    }
  }
}
