import { NodeAttrEditConf } from "@packages/type/editUiType";
import DICT from "@packages/configure/dict";
import { useEditMap } from "@packages/hook/useEdit";
import { useI18n } from "@packages/hook/useI18n";

const editMap = useEditMap();
export const ADD_BATCH_BASE_CONF_FN = (attrStore: any): NodeAttrEditConf => {
  const { t } = useI18n();
  return {
    name: "base",
    tabTitle: "lang.rms.fed.basis",
    labelWidth: "80px",
    labelPosition: "left",
    formItem: [
      // 节点类型
      {
        prop: "cellType",
        label: "lang.rms.fed.cellType",
        component: "elSelect",
        showWordLimit: true,
        data: DICT.CELL_TYPE_DICT,
      },
      // 单元格宽
      {
        prop: "length",
        label: "lang.rms.fed.cellWidth",
        describe: `${t("lang.rms.fed.cellSize")}/m`,
        component: "elInputNumber",
        min: 0,
        max: 10000,
        step: 0.1,
        precision: 3,
      },
      // 单元格高
      {
        prop: "width",
        label: "lang.rms.fed.cellHeight",
        describe: `${t("lang.rms.fed.cellSize")}/m`,
        component: "elInputNumber",
        min: 0,
        max: 10000,
        step: 0.1,
        precision: 3,
      },
      // 元素间距
      {
        prop: "spacing",
        label: "元素间距",
        describe: `${t("元素间距")}/m`,
        component: "elInputNumber",
        min: 0.1,
        max: 10000,
        step: 0.1,
        precision: 3,
        set(value: number, fromData: { [k: string]: any }) {
          fromData.spacing = value
          const {points} = fromData
          const [p1,p2] = points
          const {x:x1,y:y1} = p1
          const {x:x2,y:y2} = p2
          const sliceX = x2 - x1
          const sliceY = y2 - y1
          const dis = Math.sqrt(sliceX * sliceX + sliceY * sliceY)
          const num = Math.floor(dis / value) < 2 ? 2 : Math.floor(dis / value)
          fromData.cellNum = num
        },
      },
      // 元素个数
      {
        prop: "cellNum",
        label: "元素个数",
        component: "elInputNumber",
        min: 2,
        max: 1000,
        step: 1,
        set(value: number, fromData: { [k: string]: any }) {
          fromData.cellNum = value
          const {points} = fromData
          const [p1,p2] = points
          const {x:x1,y:y1} = p1
          const {x:x2,y:y2} = p2
          const sliceX = x2 - x1
          const sliceY = y2 - y1
          const dis = Math.sqrt(sliceX * sliceX + sliceY * sliceY)
          const space = Number((dis / (value - 1)).toFixed(3))
          fromData.spacing = space
        },
      },
      // 分割
      // 精准点位坐标功能
      {
        component: "elDivider",
        onlyComponent: true,
      },
      // 元素间距
      {
        prop: "lineCoordX1",
        label: "线段坐标X1",
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 0.1,
        precision: 3,
        get(fromData: { [k: string]: any }) {
          return fromData.points[0]?.x || 0;
        },

        set(value: number, fromData: { [k: string]: any }) {
          return (fromData.points[0]!.x = value);
        },
      },
      {
        prop: "lineCoordY1",
        label: "线段坐标Y1",
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 0.1,
        precision: 3,
        get(fromData: { [k: string]: any }) {
          return fromData.points[0]?.y || 0;
        },

        set(value: number, fromData: { [k: string]: any }) {
          return (fromData.points[0]!.y = value);
        },
      },
      {
        prop: "lineCoordX2",
        label: "线段坐标X2",
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 0.1,
        precision: 3,
        get(fromData: { [k: string]: any }) {
          return fromData.points[1]?.x || 0;
        },

        set(value: number, fromData: { [k: string]: any }) {
          return (fromData.points[1]!.x = value);
        },
      },
      {
        prop: "lineCoordY2",
        label: "线段坐标Y2",
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 0.1,
        precision: 3,
        get(fromData: { [k: string]: any }) {
          return fromData.points[1]?.y || 0;
        },

        set(value: number, fromData: { [k: string]: any }) {
          return (fromData.points[1]!.y = value);
        },
      },
      {
        prop: "isCellLine",
        label: "绘制节点线段",
        component: "elSwitch",
      },
    ],
  };
};
