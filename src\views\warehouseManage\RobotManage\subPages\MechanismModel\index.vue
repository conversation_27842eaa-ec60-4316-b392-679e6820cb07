<template>
  <div>
    <SearchWrap>
      <SearchForm @onsubmit="onSubmit" />
    </SearchWrap>
    <div style="text-align: right">
      <CreateDialog
        :prop-show="showCreatDialog"
        :edit-prop="editCreatDialog"
        :button-text="$t('lang.rms.api.result.warehouse.robotMechanismModel')"
        :title-text="$t(creatDialogTitle)"
        @createconfirm="createConfirm"
        @createcancel="createCancel"
      >
        <CreatForm ref="createForm" :slop-props="slopProps" :edit-prop="editCreatDialog" />
      </CreateDialog>
    </div>
    <el-table :loading="loading" :data="recordList" style="width: 100%">
      <!-- <el-table-column
        :label="$t('lang.rms.api.result.warehouse.orgCode')"
        prop="mechanismCode"
        min-width="120"
      /> -->
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.mechanism.spuName')"
        prop="name"
        min-width="140"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.lengthWidthHeight')"
        prop="length"
        min-width="160"
      >
        <template slot-scope="scope">
          <span>{{ `${scope.row.length}/${scope.row.width}/${scope.row.height}` }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.maxLoad')"
        prop="maxLoad"
        min-width="120"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.maxWorkingHeight')"
        prop="maxWorkHeight"
        min-width="140"
      />
      <!-- 旋转直径 -->
      <el-table-column
        :label="$t('lang.rms.fed.rotationDiameter')"
        prop="diameter"
        min-width="140"
      />
      <!-- 坐标偏移值 -->
      <el-table-column
        :label="$t('lang.rms.fed.locationOffset')"
        prop="locationOffset"
        min-width="140"
      />
      <!-- 滚筒上装对接面 -->
      <el-table-column
        :label="$t('lang.rms.fed.dockingSide')"
        prop="dockingSides"
        min-width="140"
      >
      <template slot-scope="scope">
       <el-tag
            v-for="item in scope.row.dockingSides"
            :key="item"
            type="info"
            style="margin-right: 10px; margin-bottom: 2px"
            >{{ item }}</el-tag
          >
        </template>
      </el-table-column>
      <!-- 是否A面取A面放 -->
      <el-table-column
        :label="$t('lang.rms.fed.deliverStrategy.aGetaDrop')"
        prop="deliverStrategy"
        min-width="140"
      >
      <template slot-scope="scope">
         {{scope.row.deliverStrategy == 1 ? $t('lang.rms.fed.yes') : $t('lang.rms.fed.no') }}
        </template>
      </el-table-column>
      <!-- 是否有后退传感器 -->
      <el-table-column
        :label="$t('lang.rms.fed.isHasBackupSensor')"
        prop="hasBackupSensor"
        min-width="140"
      >
        <template slot-scope="scope">
          <div>
            <span v-show="scope.row.hasBackupSensor === 0">{{ $t('lang.venus.common.dict.no') }}</span>
            <span v-show="scope.row.hasBackupSensor === 1">{{ $t('lang.rms.api.result.edit.map.yes') }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.movementAbility')"
        prop="controlAbilities"
        min-width="200"
      >
        <template slot-scope="scope">
          <el-tag
            v-for="item in scope.row.controlAbilities"
            :key="item"
            type="info"
            style="margin-right: 10px; margin-bottom: 2px"
            >{{ item }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.capacity')"
        prop="actionAbilities"
        min-width="200"
      >
        <template slot-scope="scope">
          <el-tag
            v-for="item in scope.row.actionAbilities"
            :key="item"
            type="info"
            style="margin-right: 10px; margin-bottom: 2px"
            >{{ item }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        :label="$t('lang.rms.fed.textOperation')"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="checkListItemInfo(scope.row)">
            {{ $t("lang.rms.fed.buttonView") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="copyListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.web.map.version.copy") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="editListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.fed.buttonEdit") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="deleteListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.fed.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="text-align: right; margin-top: 30px">
      <el-pagination
        background
        layout="total, prev, pager, next, sizes, jumper"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="paginationParams.total"
        :page-size="paginationParams.pageSize"
        :current-page="paginationParams.currentPage"
        @current-change="paginationChange"
        @size-change="paginationPageChange"
      />
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import SearchWrap from "../../Components/SearchWrap";
import CreateDialog from "../../Components/CreateDialog";
import CreatForm from "./Components/CreatForm";
import SearchForm from "./Components/SearchForm";
import robotManageRequest from "@/api/robotManage";

export default {
  name: "MechanismModel",
  components: {
    CreatForm,
    SearchWrap,
    CreateDialog,
    SearchForm,
  },
  data() {
    return {
      slopProps: {},
      showCreatDialog: false,
      editCreatDialog: true,
      creatDialogTitle: "lang.rms.api.result.warehouse.createRobotMechanismModel",
      copyDataOpen: false,
      loading: false,
      recordList: [],
      paginationParams: { pageSize: 20, currentPage: 1, total: 0 },
      searchFormData: {},
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  watch: {},
  methods: {
    onSubmit(searchFormData) {
      const { paginationParams } = this;
      this.searchFormData = searchFormData;

      this.reqTableList(searchFormData, paginationParams);
    },
    reqTableList(searchFormData, paginationParams) {
      this.loading = true;
      robotManageRequest.getMechanismPageList(searchFormData, paginationParams).then(({ data }) => {
        const { pageSize, currentPage, recordList, recordCount } = data;
        this.recordList = recordList.map(item => {
          // localCover字段去掉 z
          item.localCover = Array.isArray(item.localCover)
            ? item.localCover.map(itemIn => ({ x: itemIn.x, y: itemIn.y }))
            : [];
          return item;
        });
        console.log(' this.recordList ', this.recordList );
        this.paginationParams = {
          pageSize,
          currentPage,
          total: recordCount,
        };
        this.loading = false;
      });
    },
    reReqTableList() {
      this.paginationParams.currentPage = 1;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationChange(currentPage) {
      this.paginationParams.currentPage = currentPage;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationPageChange(pageSize) {
      this.paginationParams.pageSize = pageSize;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    createConfirm() {
      if (this.editCreatDialog) {
        this.showCreatDialog = true;
        const $createForm = this.$refs["createForm"];
        $createForm.getFormValues().then(val => {
          if (val) {
            const { modelData } = $createForm;
            const paramsObj = { ...modelData, localEnvelope: JSON.stringify(modelData.localCover) };
            if (!this.slopProps.id || this.copyDataOpen) {
              delete paramsObj.id;
            }
            if (Object.prototype.toString.call(paramsObj.file) === "[object String]") {
              delete paramsObj.file;
            }
            delete paramsObj.mechanismImage;
            delete paramsObj.localCover;

            const formData = new FormData();
            for (const key in paramsObj) {
              formData.append(key, paramsObj[key] ?? "");
            }
            robotManageRequest.addEditMechanismItem(formData, {}).then(({ code }) => {
              if (+code === 0) {
                console.log("添加/修改成功！");
              }
              this.createCancel();
              this.reReqTableList();
              this.confirmNext();
            });
          }
        });
      }
    },
    createCancel() {
      this.$refs["createForm"].resetFormValues();
      this.slopProps = {};
      this.editCreatDialog = true;
      this.showCreatDialog = false;
      this.copyDataOpen = false;
      this.creatDialogTitle = "lang.rms.api.result.warehouse.createRobotMechanismModel";
    },
    async confirmNext() {
      try {
        await this.$confirm(
          this.$t("lang.rms.fed.gotoSomePage", [
            this.$t("lang.rms.api.result.warehouse.mechanismComponentModel"),
          ]),
        );
        this.$emit("goNextPage");
      } catch (e) {}
    },
    openDialogInSomeType(edit, data) {
      const {
        id,
        name,
        // mechanismCode,
        mechanismImage,
        length,
        width,
        height,
        weight,
        maxLoad,
        maxWorkHeight,
        headOffsetRatio,
        tailOffsetRatio,
        localCover,
        controlAbilities,
        actionAbilities,
        locationOffset,
        diameter,
        hasBackupSensor,
        deliverStrategy,
        dockingSides
      } = data;
      this.slopProps = {
        id,
        name,
        // mechanismCode,
        mechanismImage,
        length,
        width,
        height,
        weight,
        maxLoad,
        maxWorkHeight,
        headOffsetRatio,
        tailOffsetRatio,
        localCover,
        controlAbilities,
        actionAbilities,
        locationOffset,
        diameter,
        hasBackupSensor,
        deliverStrategy,
        dockingSides
      };
      this.editCreatDialog = edit;
      this.showCreatDialog = true;
      this.creatDialogTitle = edit
        ? "lang.rms.api.result.warehouse.editRobotMechanismModel"
        : "lang.rms.api.result.warehouse.viewRobotMechanismModel";
    },
    // 查看
    checkListItemInfo(robotData) {
      robotManageRequest.getMechanismDetailInfo({}, { id: robotData.id }).then(({ data }) => {
        this.openDialogInSomeType(false, data);
      });
    },
    // 复制
    copyListItemInfo(robotData) {
      robotManageRequest.getMechanismDetailInfo({}, { id: robotData.id }).then(({ data }) => {
        this.copyDataOpen = true;
        this.openDialogInSomeType(true, data);
      });
    },
    // 编辑
    editListItemInfo(robotData) {
      robotManageRequest.getMechanismDetailInfo({}, { id: robotData.id }).then(({ data }) => {
        this.openDialogInSomeType(true, data);
      });
    },
    // 删除
    deleteListItemInfo(robotData) {
      this.$confirm(
        this.$t("lang.rms.api.result.warehouse.willDeleteToContinue"),
        this.$t("lang.rms.fed.prompt"),
        {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.rms.fed.cancel"),
          type: "warning",
        },
      )
        .then(() => {
          robotManageRequest.deleteMechanismItem({}, { id: robotData.id }).then(({ code }) => {
            if (+code === 0) {
              this.$message({
                type: "success",
                message: this.$t("lang.venus.web.common.successfullyDeleted"),
              });
            }
            this.reReqTableList();
          });
        })
        .catch(() => null);
    },
  },
};
</script>
<style lang="scss" scoped>
.align-bottom {
  vertical-align: bottom;
}
</style>
