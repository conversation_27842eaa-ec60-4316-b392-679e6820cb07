/* ! <AUTHOR> at 2022/09/02 */
import * as PIX<PERSON> from "pixi.js";

class LayerRealtimeObstacle implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  private lineStyle: any = new PIXI.LineStyle();
  private meshList: Array<any> = [];
  private geometries: Array<any> = [];
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  init(): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "realtimeObstacle";
    container.interactiveChildren = true;
    container.visible = false;
    container.alpha = 0.7;
    container.zIndex = utils.getLayerZIndex("realtimeObstacle");
    this.container = container;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  render(arr: Array<realtimeObstacleData>): void {
    const _this = this;
    const mapCore = _this.mapCore,
      utils = mapCore.utils,
      lineStyle = _this.lineStyle;

    const len = arr.length;
    let graphicsGeometry: any = new PIXI.GraphicsGeometry();
    graphicsGeometry.BATCHABLE_SIZE = len;

    let item, options, position, circle, fillStyle;
    for (let i = 0; i < len; i++) {
      item = arr[i];
      options = utils.formatRealtimeObstacle(item);
      position = options["position"];
      circle = new PIXI.Circle(position.x, position.y, options["radius"]);

      fillStyle = new PIXI.FillStyle();
      fillStyle.color = options["statusColor"];
      fillStyle.alpha = 0.6;
      fillStyle.visible = true;
      graphicsGeometry.drawShape(circle, fillStyle, lineStyle);
      _this.geometries.push({ circle, options });
    }

    const graphics: any = new PIXI.Graphics(graphicsGeometry);
    graphics.name = "realtimeObstacle";
    graphics.floorId = this.floorId;
    graphics.mapType = "realtimeObstacle";
    graphics.interactive = graphics.buttonMode = true;
    this.meshList.push(graphics);
    this.container.addChild(graphics);
    this.geometries = [];
  }

  update(arr: Array<realtimeObstacleData>) {
    this.repaint();
    this.render(arr);
  }

  getAreaCode(x: number, y: number) {
    const geometries = this.geometries;

    let item;
    for (let i = 0, len = geometries.length; i < len; i++) {
      item = geometries[i];
      if (item.circle.contains(x, y)) {
        return item.options.code;
      }
    }
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    this.geometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
    this.lineStyle = null;
    this.meshList = null;
    this.geometries = null;
  }
}
export default LayerRealtimeObstacle;
