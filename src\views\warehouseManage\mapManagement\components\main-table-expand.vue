<template>
  <el-table :key="updateTimestamp" class="expend-table" :show-header="false" :data="floods">
    <el-table-column width="320">
      <template slot-scope="scope">{{ $t("lang.rms.fed.floor") }} {{ scope.row.floorId }}</template>
    </el-table-column>
    <el-table-column width="210">
      <template slot-scope="scope">
        <p :class="infoColor(scope.row.uploadStatus)">
          {{ $t(mapStatus.get(scope.row.uploadStatus)) }}
        </p>
      </template>
    </el-table-column>
    <el-table-column>
      <template slot-scope="scope">
        <p :class="infoColor(scope.row.uploadStatus)">{{ scope.row.phaseI18N }}</p>
      </template>
    </el-table-column>
    <el-table-column width="320">
      <template slot-scope="scope">
        <section class="btn-row">
          <el-button
            v-if="rowData.status >= 4"
            :disabled="btnDisabled(scope.row.uploadStatus)"
            type="text"
            @click="importFloor(scope.row.floorId)"
          >
            {{ $t("lang.rms.fed.importFloor") }}
          </el-button>
          <el-button
            v-if="[4, 5, 6].includes(rowData.status)"
            :disabled="btnDisabled(scope.row.uploadStatus)"
            type="text"
            @click="drawFloor(scope.row.floorId)"
          >
            {{ $t("lang.rms.fed.drawingAMap") }}
          </el-button>
          <el-button
            :disabled="btnDisabled(scope.row.uploadStatus)"
            type="text"
            @click="exportFloor(scope.row.floorId)"
          >
            {{ $t("lang.rms.fed.exportFloor") }}
          </el-button>
          <!--        <el-button-->
          <!--          v-if="[4, 5, 6].includes(rowData.status)"-->
          <!--          type="text"-->
          <!--          @click="editViewFloor(scope.row.floorId)"-->
          <!--        >-->
          <!--          {{ $t("lang.rms.fed.edit") }}-->
          <!--        </el-button>-->
          <!--状态5，为当前激活地图，当前激活地图不允许编辑-->
          <el-button
            v-if="[4, 5, 6].includes(rowData.status)"
            :disabled="btnDisabled(scope.row.uploadStatus)"
            type="text"
            @click="editViewFloor(scope.row.floorId)"
          >
            {{ $t("lang.rms.fed.edit") }}
          </el-button>
          <el-button
            v-if="[1, 2, 3].includes(rowData.status)"
            :disabled="btnDisabled(scope.row.uploadStatus)"
            type="text"
            @click="editViewFloor(scope.row.floorId)"
          >
            {{ $t("lang.rms.fed.buttonView") }}
          </el-button>
          <!-- <el-button type="text" size="small" @click="handleImportMask(scope.row.floorId)">
            {{
              rowData.floods[scope.$index].maskState == 1
                ? $t("lang.rms.fed.updateMaskResource")
                : $t("lang.rms.fed.importMaskResource")
            }}
          </el-button> -->
          <el-button
            v-if="[4, 5, 6].includes(rowData.status)"
            :disabled="btnDisabled(scope.row.uploadStatus)"
            type="text"
            @click="deleteFloor(scope.row.floorId)"
          >
            {{ $t("lang.rms.web.map.version.deleteFloor") }}
          </el-button>
        </section>
        <section class="btn-row">
          <el-button
            v-if="[4, 5, 6].includes(rowData.status)"
            :disabled="btnDisabled(scope.row.uploadStatus)"
            type="text"
            @click="$emit('openManageQrCode', String(rowData.id), String(scope.row.floorId))"
          >
            {{ $t("lang.rms.fed.manageQrCode") }}
          </el-button>
          <el-button
            :disabled="btnDisabled(scope.row.uploadStatus)"
            type="text"
            @click="$emit('openManageWaitTaskGroup', String(rowData.id), String(scope.row.floorId))"
          >
            {{ $t("lang.rms.fed.manageWaitaskGroup") }}
          </el-button>
        </section>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { mapMutations } from "vuex";
import dealImage from "../../../../libs/dealImage";

export default {
  name: "MainTableExpand",
  data() {
    return {
      updateTimestamp: null,
      mapStatus: new Map([
        [0, "lang.rms.fe.map.import.status.waiting"],
        [1, "lang.rms.fe.map.import.status.processing"],
        [2, "lang.rms.fe.map.import.status.success"],
        [3, "lang.rms.fe.map.import.status.failed"],
      ]),
    };
  },
  computed: {
    btnDisabled() {
      return uploadStatus => {
        if (uploadStatus === undefined) {
          return false;
        }
        return ![2, 3].includes(uploadStatus);
      };
    },
    floods() {
      this.updateTimestamp = Date.now();
      return this.rowData.floods || [];
    },
    infoColor() {
      return uploadStatus => {
        if ([1, 2].includes(uploadStatus)) {
          return "uploading";
        }
        if (uploadStatus === 3) {
          return "error";
        }
        if (uploadStatus === 0) {
          return "ready";
        }
        return "";
      };
    },
  },
  props: {
    rowData: {
      type: Object,
      require: true,
    },
  },
  methods: {
    ...mapMutations("mapManagement", ["showDialog"]),
    // 导入楼层
    importFloor(floorId) {
      this.showDialog({
        currentComponent: "dialogImportFloor",
        title: this.$t("lang.rms.fed.importFloor"),
        rowData: { ...this.rowData },
        floorId,
      });
    },
    // 绘制地图
    drawFloor(floorId) {
      this.$router.push({
        path: "/warehouseManage/drawMapFloor",
        query: {
          mapId: this.rowData.id,
          floorId,
          status: this.rowData.status,
        },
      });
    },
    // 导出楼层
    exportFloor(floorId) {
      this.showDialog({
        currentComponent: "dialogExportFloor",
        title: this.$t("lang.rms.fed.exportFloor"),
        rowData: { ...this.rowData },
        floorId,
      });
      // $req
      //   .post("/athena/map/manage/exportMap", {
      //     mapId: this.rowData.id,
      //     floorId,
      //   })
      //   .then(res => {
      //     const data = res.data;
      //     let url = data.url ? data.url : data;
      //     if ($req.isDev) window.open($req.API_URL + url);
      //     else window.open(window.location.origin + url);
      //   });
    },
    // 编辑 & 查看
    editViewFloor(floorId) {
      console.log("rowData", this.rowData);
      const { status, id } = this.rowData;
      // 5为激活态，激活的地图不允许编辑，只能复制编辑
      // if (status === 5) {
      //   this.$alert('当前地图正在应用，无法编辑，如需编辑请复制当前地图进行编辑。', this.$t('lang.rms.fed.optionWarning'), {
      //     confirmButtonText: '确定',
      //   });
      //   return
      // }
      this.getMap(id, floorId);
      this.$router.push({
        path: "/warehouseManage/editMap",
        query: {
          mapId: id,
          floorId,
          status,
        },
      });
    },
    getMap(mapId, floorId) {
      $req.postParams("/athena/map/draw/getMap", { mapId, floorId }).then(res => {
        if (!res.data.compressData && res.data.base64Text) this.getImg(res.data);
      });
    },
    getImg(data) {
      const newData = data;
      const printing = (base64, newImage) => {
        // 获取压缩后的base64大小
        newData.width = newImage.width;
        newData.height = newImage.height;
        newData.compressData = base64;
        // this.updateBg(newData);
      };
      dealImage(data.base64Text, printing);
    },
    updateBg(val) {
      const data = {
        mapId: val.mapId,
        floorId: val.floorId,
        robotType: val.robotType,
        display: val.display,
        imageData: val.base64Text,
        compressData: val.compressData,
        x: val.offsetX,
        y: val.offsetY,
        yaw: val.yaw,
        resolution: val.resolution,
        width: val.width,
        height: val.height,
        id: val.backgroundId,
      };
      $req
        .post("/athena/map/draw/importBackgroundImage", data)
        .then(res => {
          this.initFun();
        })
        .catch(e => {
          console.log(e);
        });
    },
    // 导入蒙层
    handleImportMask(floorId) {
      console.log(this.rowData);
      // this.menuClick("importMask");
      // let floorInfo = {
      //     mapId: row.mapId,
      //     floorId: row.floorId
      // };
      // setTimeout(() => {
      //     TeventBus.$emit("dialogRow", floorInfo);
      // }, 400);
      this.showDialog({
        currentComponent: "dialogImportMask",
        title: this.$t("lang.rms.fed.updateMaskResource"),
        rowData: this.rowData,
        floorId,
      });
    },
    // 删除楼层
    deleteFloor(floorId) {
      this.$geekConfirm(this.$t("lang.rms.fed.whetherOrNotToDeleteFloor")).then(() => {
        $req
          .postParams("/athena/map/draw/deleteFloor", {
            mapId: this.rowData.id,
            floorId,
          })
          .then(res => {
            this.$message({
              message: this.$t(res.msg),
              type: "success",
              duration: 8000,
            });
            this.$emit("refreshList");
          });
      });
    },
    // 判断上传状态
  },
};
</script>
<style lang="less" scoped>
//去除滚动条
.expend-table {
  ::-webkit-scrollbar-thumb {
    display: none !important;
  }
}
.uploading {
  color: #2ac039;
}
.error {
  color: #d9001b;
}
.ready {
  color: #ef973b;
}
</style>
