/* ! <AUTHOR> at 2021/01 */

import service, { _isDev, _API_URL } from "./_create";
import reqCommon from "./_req_common";

export default {
  isDev: _isDev,
  API_URL: _API_URL,

  /**
   * 接口get请求
   * @param url
   * @param params
   * @param obj: {
   *     intercept: 是否直接拦截提示错误，默认为true
   *     headers: 默认null
   * }
   * @returns {Promise}
   */
  get(url, params, obj = {}) {
    const opt = Object.assign({ intercept: true, headers: null }, obj);

    let options = { method: "get", url, intercept: opt.intercept };
    if (params) options.params = params;
    if (opt.headers) options.headers = opt.headers;

    return new Promise((resolve, reject) => {
      service(options)
        .then(xhr => {
          return xhr.data;
        })
        .then(res => {
          if (!options.intercept) {
            resolve(res);
          } else {
            if (res.code === 0) resolve(res);
            else reject(res);
          }
        })
        .catch(error => {
          if (opt.intercept) $app.$error(error.message);
          reject(error);
        });
    });
  },

  /**
   * 接口post请求
   * @param url
   * @param data
   * @param obj: {
   *     intercept: 是否直接拦截提示错误，默认为true
   *     headers: 默认null
   * }
   * @returns {Promise<any>}
   */
  post(url, data, obj = {}) {
    const opt = Object.assign({ intercept: true, headers: null }, obj);

    let options = { method: "post", url, intercept: opt.intercept, timeout: obj.timeout };
    if (data) options.data = data;
    if (opt.headers) options.headers = opt.headers;

    return new Promise((resolve, reject) => {
      service(options)
        .then(xhr => {
          return xhr.data;
        })
        .then(res => {
          if (!options.intercept) {
            resolve(res);
          } else {
            if (res.code === 0) resolve(res);
            else reject(res);
          }
        })
        .catch(error => {
          if (opt.intercept) $app.$error(error.message);
          reject(error);
        });
    });
  },

  /**
   * 接口post请求 传参为params
   * @param url
   * @param data
   * @param obj: {
   *     intercept: 是否直接拦截提示错误，默认为true
   *     headers: 默认null
   * }
   * @returns {Promise<any>}
   */
  postParams(url, data, obj = {}) {
    const opt = Object.assign({ intercept: true, headers: null }, obj);

    let options = { method: "post", url, intercept: opt.intercept };
    if (data) options.params = data;
    if (opt.headers) options.headers = opt.headers;

    return new Promise((resolve, reject) => {
      service(options)
        .then(xhr => {
          return xhr.data;
        })
        .then(res => {
          if (!options.intercept) {
            resolve(res);
          } else {
            if (res.code === 0) resolve(res);
            else reject(res);
          }
        })
        .catch(error => {
          if (opt.intercept) $app.$error(error.message);
          reject(error);
        });
    });
  },

  /**
   * 上传文件请求
   * @param url
   * @param data
   * @param obj: {
   *     intercept: 是否直接拦截提示错误，默认为true
   *     headers: 默认null
   *     timeout：默认0，0表示无超时时间
   * }
   * @returns {Promise}
   */
  postFile(url, data, obj = {}) {
    const opt = Object.assign(
      {
        intercept: true,
        headers: {
          "Content-Type": "multipart/form-data",
        },
        timeout: 0,
      },
      obj,
    );

    let options = { method: "post", url, intercept: opt.intercept };
    if (data) options.data = data;
    if (opt.headers) options.headers = opt.headers;
    return new Promise((resolve, reject) => {
      service(options)
        .then(xhr => {
          return xhr.data;
        })
        .then(res => {
          if (!options.intercept) {
            resolve(res);
          } else {
            if (res.code === 0) resolve(res);
            else reject(res);
          }
        })
        .catch(error => {
          if (opt.intercept) $app.$error(error.message);
          reject(error);
        });
    });
  },

  /**
   * 获取前端本地json静态文件请求
   * @param url
   * @param obj: {
   *     intercept: 是否直接拦截提示错误，默认为true
   * }
   * @returns {Promise}
   */
  getStatic(url, obj = {}) {
    const opt = Object.assign({ intercept: true }, obj);
    let options = {
      method: "get",
      url,
      isStaticReq: true,
      intercept: opt.intercept,
    };
    return new Promise((resolve, reject) => {
      service(options)
        .then(xhr => {
          return xhr.data;
        })
        .then(res => {
          resolve(res);
        })
        .catch(error => {
          if (opt.intercept) $app.$error(error.message);
          reject(error);
        });
    });
  },

  ...reqCommon,
};
