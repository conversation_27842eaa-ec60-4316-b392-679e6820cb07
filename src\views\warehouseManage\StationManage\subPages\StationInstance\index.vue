<template>
  <div>
    <SearchWrap>
      <SearchForm @onsubmit="onSubmit" />
    </SearchWrap>
    <div>
      <div class="queue-dialog">
        <el-dialog
          :title="$t('lang.rms.web.station.queueNumber')"
          :visible.sync="dialogVisible"
          :show-close="false"
        >
          <el-form
            ref="queueForm"
            :model="currentStation"
            label-position="right"
            label-width="135px"
          >
            <el-form-item :label="$t('lang.rms.fed.maxQueueNumber')">
              <el-input
                v-model="dialogQueueValue"
                :max="99"
                :min="-1"
                type="number"
                @input="handleQueueValue"
              />
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="handleResetQueue">{{ $t("lang.common.cancel") }}</el-button>
            <el-button type="primary" @click="handleChanageQueue">
              {{ $t("lang.rms.fed.save") }}
            </el-button>
          </span>
        </el-dialog>
      </div>
    </div>
    <div style="text-align: right">
      <el-button type="primary" @click="changeStatus(1)">{{ $t("lang.rms.fed.enable") }}</el-button>
      <el-button type="primary" @click="changeStatus(0)">
        {{ $t("lang.rms.fed.stopStatus") }}
      </el-button>
    </div>
    <el-table ref="selectStations" v-loading="loading" :data="stationList" style="width: 100%">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="stationId" :label="$t('lang.rms.web.station.stationId')" />
      <el-table-column prop="hostCode" :label="$t('lang.rms.fed.hostCode')" />
      <el-table-column prop="manageStatus" :label="$t('lang.rms.web.station.status')">
        <template slot-scope="scope">
          <span v-if="scope.row.manageStatus === 0">{{ $t("lang.rms.fed.stopStatus") }}</span>
          <span v-if="scope.row.manageStatus === 1">{{ $t("lang.rms.fed.enable") }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="typeDesc" :label="$t('lang.rms.web.station.stationType')">
        <template slot-scope="scope">
          {{ $t(scope.row.typeDesc) }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="mapName" :label="$t('lang.rms.web.station.inMap')" /> -->
      <!-- <el-table-column prop="floorId" :label="$t('lang.rms.web.station.location')">
        <template slot-scope="scope">
          <span v-if="scope.row.floorId">{{
            scope.row.floorId + $t("lang.rms.web.edit.floor") + "," + scope.row.locationXY
          }}</span>
        </template>
      </el-table-column> -->
      <el-table-column prop="cellCode" :label="$t('lang.rms.web.station.cellCode')" />
      <el-table-column prop="place" :label="$t('lang.rms.fed.WorkStationFace')">
        <template slot-scope="scope">
          <span v-if="scope.row.place == 'east'">{{ $t("lang.rms.fed.east") }}</span>
          <span v-if="scope.row.place == 'south'">{{ $t("lang.rms.fed.south") }}</span>
          <span v-if="scope.row.place == 'west'">{{ $t("lang.rms.fed.west") }}</span>
          <span v-if="scope.row.place == 'north'">{{ $t("lang.rms.fed.north") }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="preDeployRobot" :label="$t('lang.rms.fed.isPreDeployRobot')">
        <template slot-scope="scope">
          {{ $t(!scope.row.preDeployRobot ? "lang.rms.fed.no" : "lang.rms.fed.yes") }}
        </template>
      </el-table-column>
      <!-- 布局 -->
      <el-table-column prop="layOut" :label="$t('lang.rms.fed.layout')" width="180" />
      <el-table-column prop="maxRobotQueueSize" :label="$t('lang.rms.fed.maxQueueNumber')" />
      <!-- 正在进行的机器人 -->
      <el-table-column
        prop="deliverRobotIds"
        :label="$t('lang.rms.fed.ongoingRobot')"
        :formatter="ongoingRobotFormatter"
      />
      <el-table-column :label="$t('lang.rms.fed.operation')" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="showEditQueueList(scope.row)">
            {{ $t("lang.rms.web.station.queueNumber") }}
          </el-button>
          <el-button type="text" size="small" @click="showCacheRackDialog(scope.row)">
            {{ $t("lang.rms.fed.cacheShelfSet") }}
          </el-button>
          <el-button
            v-if="scope.row.type === 4 && scope.row.manageStatus === 0"
            type="text"
            size="small"
            @click="enableStations(scope.row.stationId)"
          >
            {{ $t("lang.rms.fed.enable") }}
          </el-button>
          <el-button
            v-if="scope.row.type === 4 && scope.row.manageStatus === 1"
            type="text"
            size="small"
            @click="disableStations(scope.row.stationId)"
          >
            {{ $t("lang.rms.fed.stopStatus") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="text-align: right; margin-top: 30px">
      <el-pagination
        background
        layout="total, prev, pager, next, sizes, jumper"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="paginationParams.total"
        :page-size="paginationParams.pageSize"
        :current-page="paginationParams.currentPage"
        @current-change="paginationChange"
        @size-change="paginationPageChange"
      />
    </div>
    <cache-rack-dialog
      v-if="dialogRackVisible"
      :visible.sync="dialogRackVisible"
      :init-row="dialogInitRow"
      @save="() => reqTableList(searchFormData, paginationParams)"
    ></cache-rack-dialog>
    <EditUnionQueue ref="unionQueueDialog" @updateMainList="reReqTableList" />
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import SearchWrap from "../../Components/SearchWrap";
import SearchForm from "./Components/SearchForm";
import stationManageRequest from "@/api/stationManage";
import CacheRackDialog from "./Components/cacheRackDialog";
import EditUnionQueue from "./Components/editUnionQueue";

export default {
  name: "StationInstance",
  components: {
    SearchWrap,
    SearchForm,
    CacheRackDialog,
    EditUnionQueue,
  },
  data() {
    return {
      ruleForm: {
        user: "",
        region: "",
      },
      loading: false,
      stationList: [{}],
      paginationParams: { pageSize: 10, currentPage: 1, total: 0 },
      searchFormData: {},
      dialogVisible: false,
      currentStation: {},
      dialogQueueValue: "",
      dialogInitRow: {},
      dialogRackVisible: false,
    };
  },
  computed: {},
  watch: {},
  created: function () {
    this.reReqTableList();
  },
  methods: {
    handleQueueValue(value) {
      if (Number(value) < -1) {
        this.dialogQueueValue = value.replace(/[^0-9.]/g, "");
      } else if (Number(value) > 99) {
        this.dialogQueueValue = 99;
      } else {
        this.dialogQueueValue = value;
      }
    },
    onSubmit(searchFormData) {
      console.log(searchFormData);
      const { paginationParams } = this;
      this.searchFormData = searchFormData;

      this.reqTableList(searchFormData, paginationParams);
    },
    reqTableList(searchFormData, paginationParams) {
      this.loading = true;
      stationManageRequest
        .getStationPageList(Object.assign(searchFormData, paginationParams))
        .then(({ data }) => {
          if (data !== null) {
            const { pageSize, currentPage, recordList, recordCount } = data;
            this.stationList = recordList;
            this.paginationParams = {
              pageSize,
              currentPage,
              total: recordCount,
            };
          }
          this.loading = false;
        });
    },
    reReqTableList() {
      this.stationList = [{}];
      this.paginationParams.currentPage = 1;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationChange(currentPage) {
      this.paginationParams.currentPage = currentPage;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationPageChange(pageSize) {
      this.paginationParams.pageSize = pageSize;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    showEditQueueList(row) {
      if (row.type === 4) {
        //联合工位
        this.$refs.unionQueueDialog.open(row);
      } else {
        this.currentStation = row;
        this.dialogQueueValue = this.currentStation.maxRobotQueueSize;
        this.dialogVisible = true;
      }
    },
    showCacheRackDialog(row) {
      this.dialogRackVisible = true;
      this.dialogInitRow = row;
    },
    handleResetQueue() {
      this.dialogVisible = false;
    },
    handleChanageQueue() {
      const data = {
        mapId: this.currentStation.mapId,
        stationId: this.currentStation.stationId,
        maxRobotQueueSize: this.dialogQueueValue,
      };
      stationManageRequest.updateMaxRobotQueueSize(data).then(() => {
        this.currentStation.maxRobotQueueSize = this.dialogQueueValue;
        this.$message({
          showClose: false,
          message: this.$t("lang.rms.api.result.ok"),
          type: "success",
        });
        this.dialogVisible = false;
      });
    },
    createCancel() {
      this.$refs["createForm"].resetFormValues();
      this.editCreatDialog = true;
      this.showCreatDialog = false;
    },
    enableStations(stationIds) {
      let ids;
      if (stationIds instanceof Array) {
        ids = stationIds;
      } else {
        ids = [stationIds];
      }
      stationManageRequest.enableStation(ids).then(() => {
        this.$message({
          showClose: false,
          message: this.$t("lang.rms.api.result.ok"),
          type: "success",
        });
        this.reReqTableList();
      });
    },
    disableStations(stationIds) {
      let ids;
      if (stationIds instanceof Array) {
        ids = stationIds;
      } else {
        ids = [stationIds];
      }
      stationManageRequest.disableStation(ids).then(() => {
        this.$message({
          showClose: false,
          message: this.$t("lang.rms.api.result.ok"),
          type: "success",
        });
        this.reReqTableList();
      });
    },
    changeStatus(status) {
      const stationIds = [];
      if (this.$refs.selectStations.selection.length < 1) {
        this.$message({
          showClose: false,
          message: this.$t("lang.rms.api.result.warehouse.pleaseEnterOperatorStation"),
          type: "warning",
        });
        return;
      }
      for (const index in this.$refs.selectStations.selection) {
        const item = this.$refs.selectStations.selection[index];
        if (item.manageStatus !== status) {
          stationIds.push(item.stationId);
        }
      }
      if (stationIds.length < 1) {
        this.$message({
          showClose: false,
          message: this.$t("lang.rms.api.result.ok"),
          type: "success",
        });
        return;
      }

      if (status === 1) {
        this.enableStations(stationIds);
      } else {
        this.disableStations(stationIds);
      }
    },
    ongoingRobotFormatter(row, column, cellValue, index) {
      if ($utils.Type.isArray(cellValue)) {
        return cellValue.join(", ");
      } else {
        return cellValue;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.align-bottom {
  vertical-align: bottom;
}
</style>
