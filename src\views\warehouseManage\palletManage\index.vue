<template>
  <geek-main-structure>
    <el-tabs v-model="activeName" class="tab-class">
      <!-- 货箱查询 -->
      <el-tab-pane
        v-if="tabNamePerssion['palletRackManage']"
        :label="$t('auth.rms.palletRackManage.palletRackManage')"
        name="palletRackManage"
      >
        <palletRackManage />
      </el-tab-pane>
      <!-- 货箱轨迹查询 -->
      <el-tab-pane
        v-if="tabNamePerssion['palletLatticeManage']"
        :label="$t('auth.rms.palletPositionManage.palletLatticeManage')"
        name="palletLatticeManage"
      >
        <palletPositionManage />
      </el-tab-pane>
    </el-tabs>
  </geek-main-structure>
</template>

<script>
import palletRackManage from "./palletRackManage";
import palletPositionManage from "./palletPositionManage";
export default {
  components: { palletRackManage, palletPositionManage },
  data() {
    return {
      tabNamePerssion: {
        palletRackManage: this.getTabPermission("TabPalletRackPage", "palletManage"),
        palletLatticeManage: this.getTabPermission("TabPalletLatticePage", "palletManage"),
      },

      defaultActive: "palletRackManage",
    };
  },
  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
  },
  mounted() {
    this.defaultActive = $utils.Tools.getRouteQueryTabName(
      this.defaultActive,
      this.tabNamePerssion,
    );
  },
};
</script>
<style lang="scss" scoped></style>
