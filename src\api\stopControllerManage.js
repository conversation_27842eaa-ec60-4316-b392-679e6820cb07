import axios from "axios"

// 急停控制器配置列表接口
export function queryBaseDeviceList(data) {
  return axios({
    url: "/athena/baseDevice/findAll",
    method: "get",
    params: data,
  })
}

// 急停控制器配置新增和修改接口
export function queryBaseDeviceSave(data, params) {
  return axios({
    url: "/athena/baseDevice/saveOrUpDate",
    method: "post",
    data,
    params,
  })
}

// 急停控制器配置删除接口
export function queryBaseDeviceDelete(data) {
  return axios({
    url: "/athena/baseDevice/delete",
    method: "get",
    params: data,
  })
}

// 急停控制器配置详情接口
export function queryBaseDeviceDetail(data) {
  return axios({
    url: "/athena/baseDevice/detail",
    method: "get",
    data,
  })
}
