export default {
  data: {
    robotSnapshotList: [
      {
        haveData: false,
        batchId: "ROBOT202303170100",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170105",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170110",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170115",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170120",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170125",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170130",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170135",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170140",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170145",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170150",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170155",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170200",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170205",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170210",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170215",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170220",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170225",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170230",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170235",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170240",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170245",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170250",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170255",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170300",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170305",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170310",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170315",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170320",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170325",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170330",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170335",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170340",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170345",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170350",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170355",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170400",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170405",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170410",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170415",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170420",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170425",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170430",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170435",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170440",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170445",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170450",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170455",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170500",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170505",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170510",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170515",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170520",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170525",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170530",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170535",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170540",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170545",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170550",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170555",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170600",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170605",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170610",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170615",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170620",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170625",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170630",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170635",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170640",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170645",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170650",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170655",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170700",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170705",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170710",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170715",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170720",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170725",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170730",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170735",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170740",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170745",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170750",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170755",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170800",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170805",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170810",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170815",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170820",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170825",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170830",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170835",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170840",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170845",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170850",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170855",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170900",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170905",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170910",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170915",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170920",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170925",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170930",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170935",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170940",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170945",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170950",
      },
      {
        haveData: false,
        batchId: "ROBOT202303170955",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171000",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171005",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171010",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171015",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171020",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171025",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171030",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171035",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171040",
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 103,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 324,
        snapshotTime: "2023-03-17T02:45:05.000+00:00",
        RS_WORK: 26,
        robotIdleCount: 107,
        batchId: "ROBOT202303171045",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 5,
        robotWorkingCount: 350,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 271,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 41,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 3,
        P40_TO_TRANSFER_FETCH_BOX: 12,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 4,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 9,
        robotChargingCount: 5,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171050",
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 142,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 282,
        snapshotTime: "2023-03-17T02:55:25.000+00:00",
        RS_WORK: 22,
        robotIdleCount: 152,
        batchId: "ROBOT202303171055",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 1,
        robotWorkingCount: 304,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 271,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 9,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 6,
        P40_TO_TRANSFER_FETCH_BOX: 2,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 10,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 5,
        robotChargingCount: 1,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171100",
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 138,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 287,
        snapshotTime: "2023-03-17T03:05:49.000+00:00",
        RS_WORK: 21,
        robotIdleCount: 150,
        batchId: "ROBOT202303171105",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 1,
        robotWorkingCount: 308,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 271,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 13,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 6,
        P40_TO_TRANSFER_FETCH_BOX: 3,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 12,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 4,
        robotChargingCount: 1,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171110",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T03:15:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171115",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171120",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171125",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171130",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T03:35:03.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171135",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171140",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171145",
      },
      {
        haveData: false,
        batchId: "ROBOT202303171150",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T03:55:05.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171155",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171200",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T04:05:03.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171205",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171210",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T04:15:08.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171215",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171220",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T04:25:08.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171225",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171230",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T04:35:06.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171235",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171240",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T04:45:05.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171245",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171250",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T04:55:10.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171255",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T05:00:00.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171300",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T05:05:09.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171305",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171310",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T05:15:09.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171315",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171320",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T05:25:05.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171325",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171330",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T05:35:07.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171335",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171340",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T05:45:04.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171345",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171350",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T05:55:06.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171355",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171400",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T06:05:04.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171405",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171410",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T06:15:00.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171415",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171420",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T06:25:08.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171425",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171430",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T06:35:06.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171435",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171440",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T06:45:05.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171445",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171450",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T06:55:03.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171455",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171500",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T07:05:06.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171505",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171510",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T07:15:06.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171515",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171520",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T07:25:04.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171525",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 310,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 1,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 117,
        snapshotTime: "2023-03-17T07:30:05.000+00:00",
        RS_WORK: 35,
        robotIdleCount: 311,
        batchId: "ROBOT202303171530",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 152,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 103,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 3,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 11,
        P40_TO_TRANSFER_FETCH_BOX: 11,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 1,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 4,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 232,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 2,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 178,
        snapshotTime: "2023-03-17T07:35:05.000+00:00",
        RS_WORK: 36,
        robotIdleCount: 232,
        batchId: "ROBOT202303171535",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 214,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 43,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 10,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 8,
        P40_TO_TRANSFER_FETCH_BOX: 23,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 7,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 101,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 390,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 25,
        snapshotTime: "2023-03-17T07:40:05.000+00:00",
        RS_WORK: 36,
        robotIdleCount: 390,
        batchId: "ROBOT202303171540",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 61,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 11,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 5,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 7,
        P40_TO_TRANSFER_FETCH_BOX: 3,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 6,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 6,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 410,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 15,
        snapshotTime: "2023-03-17T07:45:06.000+00:00",
        RS_WORK: 36,
        robotIdleCount: 410,
        batchId: "ROBOT202303171545",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 51,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 4,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 1,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 4,
        P40_TO_TRANSFER_FETCH_BOX: 6,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 4,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 4,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 416,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 2,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 9,
        snapshotTime: "2023-03-17T07:50:06.000+00:00",
        RS_WORK: 36,
        robotIdleCount: 416,
        batchId: "ROBOT202303171550",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 45,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 2,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 2,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 2,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 4,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 3,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 414,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 12,
        snapshotTime: "2023-03-17T07:55:06.000+00:00",
        RS_WORK: 36,
        robotIdleCount: 414,
        batchId: "ROBOT202303171555",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 48,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 2,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 3,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 6,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 1,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 418,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 7,
        snapshotTime: "2023-03-17T08:00:06.000+00:00",
        RS_WORK: 35,
        robotIdleCount: 419,
        batchId: "ROBOT202303171600",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 42,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 1,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 4,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 2,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 1,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 419,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 8,
        snapshotTime: "2023-03-17T08:05:06.000+00:00",
        RS_WORK: 36,
        robotIdleCount: 419,
        batchId: "ROBOT202303171605",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 44,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 3,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 7,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 3,
        P40_TO_TRANSFER_FETCH_BOX: 1,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 420,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 6,
        snapshotTime: "2023-03-17T08:10:06.000+00:00",
        RS_WORK: 34,
        robotIdleCount: 421,
        batchId: "ROBOT202303171610",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 40,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 1,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 2,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 3,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 1,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 424,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 3,
        snapshotTime: "2023-03-17T08:15:06.000+00:00",
        RS_WORK: 35,
        robotIdleCount: 424,
        batchId: "ROBOT202303171615",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 38,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 1,
        P40_FROM_TRANSFER_TO_STATION_BOX: 2,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 1,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 7,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 415,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 12,
        snapshotTime: "2023-03-17T08:20:06.000+00:00",
        RS_WORK: 34,
        robotIdleCount: 416,
        batchId: "ROBOT202303171620",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 46,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 1,
        P40_FROM_TRANSFER_TO_STATION_BOX: 1,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 1,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 3,
        P40_TO_TRANSFER_FETCH_BOX: 3,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 1,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 6,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 419,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 6,
        snapshotTime: "2023-03-17T08:25:06.000+00:00",
        RS_WORK: 34,
        robotIdleCount: 420,
        batchId: "ROBOT202303171625",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 40,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 1,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 4,
        P40_TO_TRANSFER_FETCH_BOX: 6,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 1,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 424,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 3,
        snapshotTime: "2023-03-17T08:30:07.000+00:00",
        RS_WORK: 33,
        robotIdleCount: 427,
        batchId: "ROBOT202303171630",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 36,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 3,
        P40_FROM_TRANSFER_TO_STATION_BOX: 1,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 1,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 3,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 1,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 424,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 2,
        snapshotTime: "2023-03-17T08:35:07.000+00:00",
        RS_WORK: 33,
        robotIdleCount: 427,
        batchId: "ROBOT202303171635",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 35,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 3,
        P40_FROM_TRANSFER_TO_STATION_BOX: 1,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 1,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 3,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 414,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 11,
        snapshotTime: "2023-03-17T08:40:07.000+00:00",
        RS_WORK: 33,
        robotIdleCount: 415,
        batchId: "ROBOT202303171640",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 44,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 4,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 1,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 3,
        P40_TO_TRANSFER_FETCH_BOX: 4,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 1,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 2,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 424,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 3,
        snapshotTime: "2023-03-17T08:45:07.000+00:00",
        RS_WORK: 30,
        robotIdleCount: 430,
        batchId: "ROBOT202303171645",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 33,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 1,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 1,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 5,
        P40_TO_TRANSFER_FETCH_BOX: 1,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 6,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 426,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 1,
        snapshotTime: "2023-03-17T08:50:07.000+00:00",
        RS_WORK: 29,
        robotIdleCount: 432,
        batchId: "ROBOT202303171650",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 30,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 6,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 425,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 2,
        snapshotTime: "2023-03-17T08:55:07.000+00:00",
        RS_WORK: 28,
        robotIdleCount: 431,
        batchId: "ROBOT202303171655",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 2,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 30,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 1,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 1,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 6,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 1,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 423,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 3,
        snapshotTime: "2023-03-17T09:00:07.000+00:00",
        RS_WORK: 25,
        robotIdleCount: 430,
        batchId: "ROBOT202303171700",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 28,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 1,
        P40_FROM_TRANSFER_TO_STATION_BOX: 1,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 1,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 7,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 1,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 420,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 6,
        snapshotTime: "2023-03-17T09:05:08.000+00:00",
        RS_WORK: 27,
        robotIdleCount: 426,
        batchId: "ROBOT202303171705",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 33,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 2,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 2,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 2,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 6,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 419,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 6,
        snapshotTime: "2023-03-17T09:10:08.000+00:00",
        RS_WORK: 25,
        robotIdleCount: 429,
        batchId: "ROBOT202303171710",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 31,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 3,
        P40_FROM_TRANSFER_TO_STATION_BOX: 1,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 1,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 1,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 10,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 3,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 426,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 1,
        snapshotTime: "2023-03-17T09:15:08.000+00:00",
        RS_WORK: 24,
        robotIdleCount: 438,
        batchId: "ROBOT202303171715",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 25,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 1,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 12,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171720",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T09:25:01.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171725",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T09:30:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171730",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T09:35:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171735",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T09:40:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171740",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T09:45:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171745",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T09:50:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171750",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T09:55:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171755",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T10:00:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171800",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T10:05:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171805",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T10:10:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171810",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T10:15:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171815",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T10:20:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171820",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T10:25:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171825",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T10:30:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171830",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T10:35:02.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171835",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303171840",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T10:45:06.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171845",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T10:50:06.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303171850",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 427,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T10:55:06.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 427,
        batchId: "ROBOT202303171855",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 427,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 427,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T11:00:06.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 427,
        batchId: "ROBOT202303171900",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 427,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 427,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T11:05:06.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 427,
        batchId: "ROBOT202303171905",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 427,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 427,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T11:10:07.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 427,
        batchId: "ROBOT202303171910",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 427,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 427,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T11:15:07.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 427,
        batchId: "ROBOT202303171915",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 427,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 427,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T11:20:07.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 427,
        batchId: "ROBOT202303171920",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 427,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 427,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 52,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T11:25:07.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 483,
        batchId: "ROBOT202303171925",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 42,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 493,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 265,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 52,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 30,
        P40_WORK: 7,
        snapshotTime: "2023-03-17T11:30:07.000+00:00",
        RS_WORK: 22,
        robotIdleCount: 276,
        batchId: "ROBOT202303171930",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 29,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 5,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 7,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 11,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 493,
        RS_REMOVE: 16,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 265,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 52,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 30,
        P40_WORK: 7,
        snapshotTime: "2023-03-17T11:35:07.000+00:00",
        RS_WORK: 22,
        robotIdleCount: 276,
        batchId: "ROBOT202303171935",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 29,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 5,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 7,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 11,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 493,
        RS_REMOVE: 16,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 265,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 52,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 30,
        P40_WORK: 7,
        snapshotTime: "2023-03-17T11:40:07.000+00:00",
        RS_WORK: 22,
        robotIdleCount: 276,
        batchId: "ROBOT202303171940",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 29,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 5,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 7,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 11,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 493,
        RS_REMOVE: 16,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 265,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 52,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 30,
        P40_WORK: 7,
        snapshotTime: "2023-03-17T11:45:07.000+00:00",
        RS_WORK: 22,
        robotIdleCount: 276,
        batchId: "ROBOT202303171945",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 29,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 5,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 7,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 11,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 493,
        RS_REMOVE: 16,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 265,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 52,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 30,
        P40_WORK: 7,
        snapshotTime: "2023-03-17T11:50:07.000+00:00",
        RS_WORK: 22,
        robotIdleCount: 276,
        batchId: "ROBOT202303171950",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 29,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 5,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 7,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 11,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 493,
        RS_REMOVE: 16,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 415,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 4,
        RS_COUNT: 52,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 30,
        P40_WORK: 5,
        snapshotTime: "2023-03-17T11:55:07.000+00:00",
        RS_WORK: 34,
        robotIdleCount: 417,
        batchId: "ROBOT202303171955",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 3,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 39,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 4,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 1,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 2,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 493,
        RS_REMOVE: 16,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 416,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 52,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 30,
        P40_WORK: 5,
        snapshotTime: "2023-03-17T12:00:08.000+00:00",
        RS_WORK: 22,
        robotIdleCount: 429,
        batchId: "ROBOT202303172000",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 2,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 27,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 3,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 2,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 13,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 493,
        RS_REMOVE: 16,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303172005",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T12:10:07.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303172010",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303172015",
      },
      {
        P40_COUNT: 0,
        P40_IDLE: 0,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 0,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 0,
        snapshotTime: "2023-03-17T12:20:06.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 0,
        batchId: "ROBOT202303172020",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 0,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 0,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 378,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 35,
        snapshotTime: "2023-03-17T12:25:06.000+00:00",
        RS_WORK: 19,
        robotIdleCount: 394,
        batchId: "ROBOT202303172025",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 54,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 3,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 28,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 7,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 16,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 3,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 404,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 16,
        snapshotTime: "2023-03-17T12:30:07.000+00:00",
        RS_WORK: 13,
        robotIdleCount: 427,
        batchId: "ROBOT202303172030",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 29,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 0,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 16,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 23,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 24,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 402,
        snapshotTime: "2023-03-17T12:35:07.000+00:00",
        RS_WORK: 36,
        robotIdleCount: 24,
        batchId: "ROBOT202303172035",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 438,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 170,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 16,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 8,
        P40_TO_TRANSFER_FETCH_BOX: 76,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 0,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 5,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 139,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 39,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 381,
        snapshotTime: "2023-03-17T12:40:07.000+00:00",
        RS_WORK: 31,
        robotIdleCount: 43,
        batchId: "ROBOT202303172040",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 412,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 212,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 7,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 9,
        P40_TO_TRANSFER_FETCH_BOX: 66,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 4,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 93,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 23,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 402,
        snapshotTime: "2023-03-17T12:45:07.000+00:00",
        RS_WORK: 33,
        robotIdleCount: 26,
        batchId: "ROBOT202303172045",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 435,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 206,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 11,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 3,
        P40_TO_TRANSFER_FETCH_BOX: 62,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 3,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 3,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 122,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 19,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 403,
        snapshotTime: "2023-03-17T12:50:07.000+00:00",
        RS_WORK: 33,
        robotIdleCount: 21,
        batchId: "ROBOT202303172050",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 436,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 182,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 14,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 6,
        P40_TO_TRANSFER_FETCH_BOX: 64,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 2,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 3,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 142,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 25,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 397,
        snapshotTime: "2023-03-17T12:55:03.000+00:00",
        RS_WORK: 34,
        robotIdleCount: 26,
        batchId: "ROBOT202303172055",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 431,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 167,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 15,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 7,
        P40_TO_TRANSFER_FETCH_BOX: 81,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 1,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 5,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 132,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 9,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 418,
        snapshotTime: "2023-03-17T13:00:03.000+00:00",
        RS_WORK: 31,
        robotIdleCount: 10,
        batchId: "ROBOT202303172100",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 2,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 449,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 177,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 14,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 5,
        P40_TO_TRANSFER_FETCH_BOX: 75,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 1,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 150,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 9,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 418,
        snapshotTime: "2023-03-17T13:05:03.000+00:00",
        RS_WORK: 27,
        robotIdleCount: 15,
        batchId: "ROBOT202303172105",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 445,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 161,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 13,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 97,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 6,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 147,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 124,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 303,
        snapshotTime: "2023-03-17T13:10:04.000+00:00",
        RS_WORK: 27,
        robotIdleCount: 132,
        batchId: "ROBOT202303172110",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 330,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 290,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 11,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 4,
        P40_TO_TRANSFER_FETCH_BOX: 2,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 8,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 4,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 129,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 5,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 297,
        snapshotTime: "2023-03-17T13:15:44.000+00:00",
        RS_WORK: 26,
        robotIdleCount: 138,
        batchId: "ROBOT202303172115",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 323,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 3,
        P40_FROM_TRANSFER_TO_STATION_BOX: 292,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 4,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 4,
        P40_TO_TRANSFER_FETCH_BOX: 1,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 9,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        haveData: false,
        batchId: "ROBOT202303172120",
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 125,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 302,
        snapshotTime: "2023-03-17T13:25:08.000+00:00",
        RS_WORK: 18,
        robotIdleCount: 141,
        batchId: "ROBOT202303172125",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 320,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 292,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 8,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 2,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 16,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 38,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 386,
        snapshotTime: "2023-03-17T13:30:09.000+00:00",
        RS_WORK: 23,
        robotIdleCount: 50,
        batchId: "ROBOT202303172130",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 409,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 225,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 11,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 43,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 12,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 106,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 20,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 403,
        snapshotTime: "2023-03-17T13:35:20.000+00:00",
        RS_WORK: 24,
        robotIdleCount: 31,
        batchId: "ROBOT202303172135",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 427,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 1,
        P40_FROM_TRANSFER_TO_STATION_BOX: 227,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 22,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 51,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 11,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 6,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 98,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 47,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 5,
        robotRemoveCount: 0,
        P40_WORK: 377,
        snapshotTime: "2023-03-17T13:40:01.000+00:00",
        RS_WORK: 25,
        robotIdleCount: 57,
        batchId: "ROBOT202303172140",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 2,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 402,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 3,
        P40_FROM_TRANSFER_TO_STATION_BOX: 235,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 20,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 7,
        P40_TO_TRANSFER_FETCH_BOX: 52,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 10,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 3,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 65,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 149,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 278,
        snapshotTime: "2023-03-17T13:45:03.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 185,
        batchId: "ROBOT202303172145",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 278,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 278,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 36,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 139,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 288,
        snapshotTime: "2023-03-17T13:50:04.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 175,
        batchId: "ROBOT202303172150",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 288,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 288,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 36,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 139,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 288,
        snapshotTime: "2023-03-17T13:55:08.000+00:00",
        RS_WORK: 0,
        robotIdleCount: 171,
        batchId: "ROBOT202303172155",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 1,
        robotWorkingCount: 288,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 0,
        P40_FROM_TRANSFER_TO_STATION_BOX: 288,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 0,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 32,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 1,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 0,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 33,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 392,
        snapshotTime: "2023-03-17T14:00:02.000+00:00",
        RS_WORK: 25,
        robotIdleCount: 42,
        batchId: "ROBOT202303172200",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 2,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 417,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 246,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 9,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 43,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 9,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 93,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 31,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 393,
        snapshotTime: "2023-03-17T14:05:03.000+00:00",
        RS_WORK: 24,
        robotIdleCount: 37,
        batchId: "ROBOT202303172205",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 417,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 237,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 7,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 4,
        P40_TO_TRANSFER_FETCH_BOX: 49,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 6,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 99,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 42,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 3,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 383,
        snapshotTime: "2023-03-17T14:10:03.000+00:00",
        RS_WORK: 22,
        robotIdleCount: 53,
        batchId: "ROBOT202303172210",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 405,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 230,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 6,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 52,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 11,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 93,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 47,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 376,
        snapshotTime: "2023-03-17T14:15:03.000+00:00",
        RS_WORK: 21,
        robotIdleCount: 61,
        batchId: "ROBOT202303172215",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 397,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 1,
        P40_FROM_TRANSFER_TO_STATION_BOX: 247,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 8,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 3,
        P40_TO_TRANSFER_FETCH_BOX: 39,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 14,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 82,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 56,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 364,
        snapshotTime: "2023-03-17T14:20:03.000+00:00",
        RS_WORK: 18,
        robotIdleCount: 71,
        batchId: "ROBOT202303172220",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 3,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 382,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 1,
        P40_FROM_TRANSFER_TO_STATION_BOX: 230,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 6,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 51,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 15,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 76,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 67,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 355,
        snapshotTime: "2023-03-17T14:25:03.000+00:00",
        RS_WORK: 17,
        robotIdleCount: 84,
        batchId: "ROBOT202303172225",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 1,
        robotWorkingCount: 372,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 3,
        P40_FROM_TRANSFER_TO_STATION_BOX: 232,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 4,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 38,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 17,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 1,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 80,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 55,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 365,
        snapshotTime: "2023-03-17T14:30:03.000+00:00",
        RS_WORK: 19,
        robotIdleCount: 70,
        batchId: "ROBOT202303172230",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 1,
        robotWorkingCount: 384,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 235,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 4,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 41,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 15,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 1,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 84,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 74,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 346,
        snapshotTime: "2023-03-17T14:35:04.000+00:00",
        RS_WORK: 17,
        robotIdleCount: 90,
        batchId: "ROBOT202303172235",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 2,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 363,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 228,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 9,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 40,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 16,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 69,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 67,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 352,
        snapshotTime: "2023-03-17T14:40:04.000+00:00",
        RS_WORK: 16,
        robotIdleCount: 85,
        batchId: "ROBOT202303172240",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 368,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 5,
        P40_FROM_TRANSFER_TO_STATION_BOX: 234,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 12,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 40,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 18,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 65,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 47,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 4,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 374,
        snapshotTime: "2023-03-17T14:45:04.000+00:00",
        RS_WORK: 21,
        robotIdleCount: 60,
        batchId: "ROBOT202303172245",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 395,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 236,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 9,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 5,
        P40_TO_TRANSFER_FETCH_BOX: 51,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 13,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 78,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 48,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 373,
        snapshotTime: "2023-03-17T14:50:04.000+00:00",
        RS_WORK: 25,
        robotIdleCount: 57,
        batchId: "ROBOT202303172250",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 4,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 398,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 3,
        P40_FROM_TRANSFER_TO_STATION_BOX: 221,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 9,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 60,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 9,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 81,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 51,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 370,
        snapshotTime: "2023-03-17T14:55:04.000+00:00",
        RS_WORK: 29,
        robotIdleCount: 56,
        batchId: "ROBOT202303172255",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 399,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 208,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 13,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 5,
        P40_TO_TRANSFER_FETCH_BOX: 67,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 5,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 82,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 44,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 3,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 380,
        snapshotTime: "2023-03-17T15:00:04.000+00:00",
        RS_WORK: 20,
        robotIdleCount: 59,
        batchId: "ROBOT202303172300",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 6,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 400,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 205,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 11,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 74,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 15,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 89,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 25,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 3,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 397,
        snapshotTime: "2023-03-17T15:05:04.000+00:00",
        RS_WORK: 20,
        robotIdleCount: 39,
        batchId: "ROBOT202303172305",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 417,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 5,
        P40_FROM_TRANSFER_TO_STATION_BOX: 197,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 15,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 80,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 14,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 104,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 35,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 3,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 384,
        snapshotTime: "2023-03-17T15:10:04.000+00:00",
        RS_WORK: 19,
        robotIdleCount: 52,
        batchId: "ROBOT202303172310",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 2,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 403,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 2,
        P40_FROM_TRANSFER_TO_STATION_BOX: 205,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 7,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 73,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 17,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 98,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 64,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 5,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 357,
        snapshotTime: "2023-03-17T15:15:04.000+00:00",
        RS_WORK: 25,
        robotIdleCount: 75,
        batchId: "ROBOT202303172315",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 3,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 382,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 221,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 17,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 40,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 11,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 77,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 116,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 5,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 293,
        snapshotTime: "2023-03-17T15:20:05.000+00:00",
        RS_WORK: 27,
        robotIdleCount: 124,
        batchId: "ROBOT202303172320",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 2,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 320,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 7,
        P40_FROM_TRANSFER_TO_STATION_BOX: 159,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 12,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 37,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 8,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 85,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 228,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 7,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 195,
        snapshotTime: "2023-03-17T15:25:05.000+00:00",
        RS_WORK: 31,
        robotIdleCount: 233,
        batchId: "ROBOT202303172325",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 3,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 226,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 1,
        P40_FROM_TRANSFER_TO_STATION_BOX: 94,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 7,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 0,
        P40_TO_TRANSFER_FETCH_BOX: 39,
        P40_CHARGING: 1,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 5,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 1,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 55,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 41,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 358,
        snapshotTime: "2023-03-17T15:30:05.000+00:00",
        RS_WORK: 26,
        robotIdleCount: 49,
        batchId: "ROBOT202303172330",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 384,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 5,
        P40_FROM_TRANSFER_TO_STATION_BOX: 219,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 5,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 5,
        P40_TO_TRANSFER_FETCH_BOX: 77,
        P40_CHARGING: 23,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 8,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 1,
        robotChargingCount: 23,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 56,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 66,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 0,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 356,
        snapshotTime: "2023-03-17T15:35:05.000+00:00",
        RS_WORK: 26,
        robotIdleCount: 76,
        batchId: "ROBOT202303172335",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 382,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 1,
        P40_FROM_TRANSFER_TO_STATION_BOX: 209,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 5,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 3,
        P40_TO_TRANSFER_FETCH_BOX: 60,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 10,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 82,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 27,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 1,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 391,
        snapshotTime: "2023-03-17T15:40:05.000+00:00",
        RS_WORK: 28,
        robotIdleCount: 34,
        batchId: "ROBOT202303172340",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 0,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 419,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 5,
        P40_FROM_TRANSFER_TO_STATION_BOX: 212,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 11,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 3,
        P40_TO_TRANSFER_FETCH_BOX: 59,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 7,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 108,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 45,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 377,
        snapshotTime: "2023-03-17T15:45:05.000+00:00",
        RS_WORK: 21,
        robotIdleCount: 59,
        batchId: "ROBOT202303172345",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 398,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 228,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 7,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 1,
        P40_TO_TRANSFER_FETCH_BOX: 51,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 14,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 3,
        robotChargingCount: 0,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 89,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 54,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 2,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 365,
        snapshotTime: "2023-03-17T15:50:05.000+00:00",
        RS_WORK: 23,
        robotIdleCount: 64,
        batchId: "ROBOT202303172350",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 2,
        robotExceptionCount: 0,
        RS_CHARGING: 0,
        robotWorkingCount: 388,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 214,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 13,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 2,
        P40_TO_TRANSFER_FETCH_BOX: 45,
        P40_CHARGING: 2,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 10,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 2,
        robotChargingCount: 2,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 91,
      },
      {
        P40_COUNT: 427,
        P40_IDLE: 38,
        P40_TO_STATION_FETCH_BOX: 0,
        RS_FROM_TRANSFER_TO_TRANSFER_BOX: 0,
        RS_FROM_COMMON_TO_COMMON_BOX: 4,
        RS_COUNT: 36,
        P40_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotRemoveCount: 0,
        P40_WORK: 385,
        snapshotTime: "2023-03-17T15:55:06.000+00:00",
        RS_WORK: 22,
        robotIdleCount: 50,
        batchId: "ROBOT202303172355",
        RS_FROM_UNKNOWN_TO_COMMON_BOX: 1,
        robotExceptionCount: 0,
        RS_CHARGING: 1,
        robotWorkingCount: 407,
        RS_FROM_TRANSFER_TO_COMMON_BOX: 4,
        P40_FROM_TRANSFER_TO_STATION_BOX: 221,
        P40_STAYING_STATION_PICKING: 0,
        P40_FROM_TRANSFER_TO_TRANSFER_BOX: 16,
        RS_FROM_COMMON_TO_TRANSFER_BOX: 3,
        P40_TO_TRANSFER_FETCH_BOX: 60,
        P40_CHARGING: 0,
        RS_TO_COMMON_FETCH_BOX: 0,
        RS_IDLE: 12,
        haveData: true,
        RS_FROM_UNKNOWN_TO_TRANSFER_BOX: 0,
        robotChargingCount: 1,
        P40_REMOVE: 0,
        robotCount: 463,
        RS_REMOVE: 0,
        P40_STAYING_STATION_INBOUND: 0,
        RS_TO_TRANSFER_FETCH_BOX: 0,
        P40_FROM_STATION_TO_TRANSFER_BOX: 88,
      },
      {
        haveData: false,
        batchId: "ROBOT202303172400",
      },
      {
        haveData: false,
        batchId: "ROBOT202303172405",
      },
      {
        haveData: false,
        batchId: "ROBOT202303172410",
      },
      {
        haveData: false,
        batchId: "ROBOT202303172415",
      },
      {
        haveData: false,
        batchId: "ROBOT202303172420",
      },
      {
        haveData: false,
        batchId: "ROBOT202303172425",
      },
      {
        haveData: false,
        batchId: "ROBOT202303172430",
      },
      {
        haveData: false,
        batchId: "ROBOT202303172435",
      },
      {
        haveData: false,
        batchId: "ROBOT202303172440",
      },
      {
        haveData: false,
        batchId: "ROBOT202303172445",
      },
      {
        haveData: false,
        batchId: "ROBOT202303172450",
      },
      {
        haveData: false,
        batchId: "ROBOT202303172455",
      },
    ],
  },
  resultCode: "0",
  resultMsg: "SUCCESS",
};
