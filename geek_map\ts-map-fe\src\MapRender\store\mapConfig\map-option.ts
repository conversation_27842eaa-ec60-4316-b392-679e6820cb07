/* ! <AUTHOR> at 2023/04/21 */

class MapOptions {
  private configs: MRender.renderConfigs = {
    fragmentLength: 5000,
    isSingleFloor: true,
    showStationPop: false,
    isMultiSelect: false,
    initMapPosition: null,
  };

  setConfig(configs: MRender.renderConfigs) {
    const curConfigs = this.configs;

    let nowConfigs: any = null;
    let key: keyof MRender.renderConfigs;
    for (key in curConfigs) {
      if (!configs.hasOwnProperty(key)) continue;
      if (!nowConfigs) nowConfigs = {};
      nowConfigs[key] = configs[key];
    }
    Object.assign(this.configs, nowConfigs);
  }

  getConfig(key: MRender.mapConfigKey = null): any {
    if (!key) {
      return Object.assign({}, this.configs, {
        mapAngle: this.getAngle(),
        showTopologicalGraph: this.isShowTopologicalGraph(),
      });
    } else if (key === "mapAngle") return this.getAngle();
    else if (key === "showTopologicalGraph") return this.isShowTopologicalGraph();
    else return this.configs[key];
  }

  /**
   * 获取地图旋转角度
   * @returns
   */
  getAngle(): number {
    let config: any = {};
    let RMSConfig = localStorage.getItem("Geek_RMSConfig");
    if (RMSConfig) config = JSON.parse(RMSConfig);
    const angle = config?.mapRotateAngle || 0;

    let newAngle = 0;
    if ((angle > 0 && angle <= 45) || (angle > 316 && angle <= 360)) {
      newAngle = 0;
    } else if (angle > 46 && angle <= 135) {
      newAngle = 90;
    } else if (angle > 91 && angle <= 225) {
      newAngle = 180;
    } else if (angle > 226 && angle <= 315) {
      newAngle = 270;
    }

    return newAngle;
  }

  /**
   * 拓扑图 是否初始化显示线段
   * @returns
   */
  isShowTopologicalGraph(): boolean {
    let config: any = {};
    let RMSConfig = localStorage.getItem("Geek_RMSConfig");
    if (RMSConfig) config = JSON.parse(RMSConfig);

    if (config.hasOwnProperty("showTopologicalGraph")) {
      return config.showTopologicalGraph;
    } else {
      return true;
    }
  }

  uninstall() {
    this.configs = {
      fragmentLength: 5000,
      isSingleFloor: true,
      showStationPop: false,
      isMultiSelect: false,
      initMapPosition: null,
    };
  }

  destroy() {
    this.uninstall();
  }
}
export default MapOptions;
