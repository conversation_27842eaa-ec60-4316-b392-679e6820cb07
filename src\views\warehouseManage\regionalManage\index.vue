<template>
  <div class="app-container ui-box">
    <!-- 查询条件 -->
    <el-card>
      <el-form
        ref="formModel"
        :inline="true"
        class="demo-form-inline"
        label-position="top"
        :model="formModel"
      >
        <el-form-item :label="$t('lang.rms.fed.mapArea.id')" prop="logicId">
          <el-input v-model.trim="formModel.logicId" />
        </el-form-item>
        <span class="btnwarp">
          <el-button type="primary" @click="submit">{{ $t("lang.rms.fed.query") }}</el-button>
          <el-button @click="resetForm">{{ $t("lang.rms.fed.reset") }}</el-button>
        </span>
      </el-form>
    </el-card>
    <!-- 查询列表 -->
    <el-card class="mt20">
      <m-table
        :table-item="tableItem"
        :table-data="tableData"
        :page-data="null"
        :extend-config="isRoleGuest ? {} : tableExtendConfig"
        @editItem="editItem"
        @deleteItem="deleteItem"
      />
    </el-card>
    <!-- 编辑弹框 -->
    <edit-dialog
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :init-row="dialogInitRow"
      @saveSuccess="dialogSaveSuccess"
    />
  </div>
</template>
<script>
import { getSearchTableItem } from "./data";
import editDialog from "./components/editDialogs";

export default {
  name: "StopControllerManage",
  components: {
    editDialog,
  },
  data() {
    return {
      addBtnLoading: false,
      // form表单
      formModel: {
        logicId: undefined,
      },
      // table相关配置
      tableData: [],
      pageData: {
        currentPage: 1,
        pageSize: 10,
        recordCount: 0,
      },
      tableExtendConfig: {
        checkBox: false,
        mtablePageData: false,
        operate: [
          {
            event: "editItem",
            label: this.$t("lang.rms.fed.edit"),
          },
        ],
      },
      // dialog相关配置
      dialogVisible: false,
      dialogInitRow: {},
      timer: null,
    };
  },
  computed: {
    // Table的item
    tableItem() {
      return getSearchTableItem(this, { model: this.model_dict });
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  async created() {
    this.queryList();
  },
  activated() {
    if (this.timer) clearInterval(this.timer);
    this.timer = setInterval(() => {
      this.queryList();
    }, 6000);
  },
  deactivated() {
    if (this.timer) clearInterval(this.timer);
    this.timer = null;
  },
  methods: {
    addItem() {
      this.dialogVisible = true;
      this.dialogInitRow = {};
    },

    editItem({ row }) {
      this.dialogVisible = true;
      this.dialogInitRow = row;
    },

    submit() {
      this.queryList();
    },

    resetForm() {
      this.formModel = {};
      this.queryList();
    },
    deleteItem({ row }) {
      //    queryBaseDeviceDelete({id:row.id}).then(res => {
      //     const { code } = res;
      //     if (code) return;
      //     this.queryList();
      //   });
    },
    dialogSaveSuccess() {
      this.queryList();
    },

    queryList() {
      // TODO-服了m-table这个玩意谁封装的，一堆bug
      const tableKeys = [
        "robotDemand",
        // "forkliftActualNumber",
        // "forkliftNeedNumber",
        // "forkliftTaskNumber",
        "robotNeedNumber",
        "robotActualNumber",
        "robotTaskNumber",
      ];
      const params = { logicId: this.formModel?.logicId || "" };
      $req.get("/athena/area/findById", params).then(res => {
        // 工作站查询
        const { code, data } = res;
        if (code) return;
        this.tableData = data;
        this.tableData = data.map(item => {
          tableKeys.forEach(key => {
            item[key] = item[key].toString();
          });
          return item;
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.ui-box {
  padding: 15px;
}
</style>

<style lang="less">
.mt20 {
  margin-top: 20px;
}
.btnwarp {
  line-height: 98px;
}
.ui-mytable .ui-mytable__header {
  background-color: #fff !important;
}
</style>
