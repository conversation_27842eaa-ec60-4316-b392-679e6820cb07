import * as PIXI from "pixi.js";
window.PIXI = PIXI;
import { Viewport } from "pixi-viewport";
//属性管理
import Store from "./store/Store";
//图层管理
import LayerManager from "./layerManager/LayerManager";
//保存viewport
import { setGlobalEditMap, setGlobalApp, setGlobalViewport } from "./global";
//事件管理
import Event from "./event/Event";
//创建模式
import Mode from "./Mode";
//资源管理
import Resource from "./resource/Resource";
//选择
import Selected from "./selected/Selected";
//事件管理
import EventBus from "./eventBus/EventBus";
//历史前进回退
import History from "./history/History";
//图形缓存
import Cache from './cache/Cache'
//坐标转换
import { cad2pixi, isHoverNode, pixi2cad, createId, toFixed } from "./utils/utils";
//控制
import Control from "./control/Control";
import { Sprite } from "pixi.js";
//api模块化
import * as api from "./api";
//功能添加选择事件
import FnChooseEvent from "./event/FnChooseEvent";
import { setCoefficient } from "./config";
class Edit {
  constructor(op) {
    const { id, dom } = op;
    this.app = null;
    this.id = id;
    this.dom = dom;
    this.viewport = null;
    //操作图层
    this.operateLayer = null;
    //编辑控件对象
    this.editor = null;
    this.history = History;
    //绑定api
    this.bindApi();
  }
  //设置分辨率
  setResolution(resolution) {
    setCoefficient(resolution);
  }

  //绑定接口
  bindApi() {
    for (const name in api) {
      this[name] = api[name];
    }
  }

  //初始化地图
  async initMap() {
    const { id, dom } = this;
    this.app = new PIXI.Application({
      // transparent:true,
      // autoResize: true,
      width: dom.offsetWidth,
      height: dom.offsetHeight,
      antialias: true,
      resizeTo: dom,
    });
    // globalThis.__PIXI_APP__ = this.app;
    this.app.stage.width = dom.offsetWidth;
    this.app.stage.height = dom.offsetHeight;
    // this.app.renderer.autoResize = true;
    this.viewport = new Viewport({
      screenWidth: dom.offsetWidth,
      screenHeight: dom.offsetHeight,
      interaction: this.app.renderer.plugins.interaction,
      // stopPropagation: true,
      disableOnContextMenu: true,
      resizeTo: dom,
    });
    this.viewport.name = "viewport";
    this.app.stage.addChild(this.viewport);
    this.viewport.drag().pinch().wheel();
    this.viewport.sortableChildren = true;
    this.app.renderer.backgroundColor = 0xf2f2f2;
    //设置全局变量
    setGlobalApp(this.app);
    setGlobalViewport(this.viewport);
    dom.appendChild(this.app.view);
    await Resource.init();
    //初始化图层
    this.initLayer();
    //初始化事件
    Event.initEvent();
    //初始化事件回调
    this.initEventBus();
  }
  //初始化图层
  initLayer() {
    LayerManager.initLayer();
  }
  //添加空白图层
  addBlock() {
    //添加block图层，空图层添加
    const block = new Sprite();
    block.alpha = 0;
    block.name = "block";
    block.x = 0;
    block.y = 0;
    block.width = this.dom.offsetWidth;
    block.height = this.dom.offsetHeight;
    this.viewport.addChild(block);
  }
  //删除空白图层
  removeBlock() {
    const $block = this.viewport.getChildByName("block");
    if ($block) this.viewport.removeChild($block);
  }

  //添加元素
  addElements(addOps) {
    LayerManager.addElements(addOps);
  }
  //更新元素
  updateElements(updateOps) {
    LayerManager.updateElements(updateOps);
  }
  //删除元素
  deleteElements(deleteOps) {
    LayerManager.deleteElements(deleteOps);
    Selected.resetAllSelected();
  }

  //初始化数据
  initData(obj) {
    console.log("初始化数据", obj);
    //是否为空地图
    let isBlock = true;
    for (const id in obj) {
      const data = obj[id];
      if (data.length) isBlock = false;
      const initOp = { id, data };
      LayerManager.initElements(initOp);
    }
    //空白地图初始化
    if (isBlock) this.addBlock();
    //生成单元格四叉树，需要在元素渲染完成之后生成
    Store.tree.create();
    this.setCenter(isBlock);
    // this.ddd()
    // let x = 0,y = 0,angle = 0
    // setInterval(() => {
    //   angle ++
    //   x++;
    //   y++;
    //   this.globalMove(x,y,angle)
    // },2000)
  }
  //设置中心点
  setCenter(isBlock = false) {
    const { viewport } = this;
    const padding = isBlock ? 0 : 300;
    const { screenWidth, screenHeight } = viewport;
    const { height, width, x, y } = viewport.getLocalBounds();
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    const scale = Math.min((screenWidth - padding) / width, (screenHeight - padding) / height);
    const center = [centerX, centerY];
    viewport.moveCenter(...center);
    viewport.setZoom(scale, true);
  }
  //重置
  resize() {
    const { app, viewport, dom } = this;
    if (app) this.app.resize();
    if (viewport) {
      viewport.resize(dom.offsetWidth, dom.offsetHeight);
    }
  }
  //TODO 已修改api
  setSelected({ layerName, id }) {
    const { $el } = LayerManager.getProperties({ layerName, id });
    Selected.renderSelected(id, $el);
  }
  //设置为多选态
  setMultiSelectedStatus(isMulti = true) {
    Selected.isMultipleSelected = isMulti;
  }
  //获取所有的选中数据
  getAllSelected() {
    return Selected.getAllSelected();
  }
  resetAllSelected() {
    Selected.resetAllSelected();
  }

  //改变模式
  changeMode(mode) {
    Mode.changeMode(mode);
  }

  //历史后退
  historyBack() {
    History.back();
  }
  //历史前进
  historyForward() {
    History.forward();
  }
  //TODO 已修改api
  getProperties({ layerName, id }) {
    return LayerManager.getProperties({ layerName, id });
  }
  //展示空负载
  showDirByType(dirType = "both") {
    Control.changeDir(dirType);
    //线和cell的dir改变
    const cellLayerInstance = LayerManager.get("CELL");
    const lineLayerInstance = LayerManager.get("LINE");
    cellLayerInstance.showDirByType();
    lineLayerInstance.showDirByType();
  }
  //显示图层
  showLayer(ops) {
    LayerManager.showLayer(ops);
  }
  //隐藏图层
  hideLayer(ops) {
    LayerManager.hideLayer(ops);
  }
  //获取所有数据
  getAllData() {
    return LayerManager.getAllData();
  }
  //重置地图装填
  resetMode() {
    Mode.resetMode();
  }
  //获取图层数据
  getLayerData(layerName) {
    return LayerManager.getLayerData(layerName);
  }
  //生成id
  createId() {
    return createId();
  }
  //通过cellCodes获取nodeIds
  getNodeIdsByCellCodes(cellCodes = []) {
    const layerInstance = LayerManager.get("CELL");
    return layerInstance.getNodeIds(cellCodes);
  }
  //全局偏移
  globalMove(offsetX, offsetY, angle = 0) {
    const bgInstance = LayerManager.get("BACKGROUND");
    const newPos = bgInstance.move(offsetX, offsetY, angle);
    return newPos;
  }
  //获取被删除的数据
  getDeleteData() {
    //拼接被删除的数据
    let deleteData = null;
    for (const i in History.deleteData) {
      if (!deleteData) deleteData = {};
      const ids = History.deleteData[i];
      deleteData[i.toLowerCase()] = ids;
    }
    return deleteData;
  }
  //通过cellCodes获取nodeIds
  getNodeIdsByCellCodes(cellCodes = []) {
    const layerInstance = LayerManager.get("CELL");
    return layerInstance.getNodeIds(cellCodes);
  }

  //当功能面板下拉触发时
  fnChoose(nodeIds = []) {
    const { action } = Mode.mode;
    if (action === "FN_CHOOSE") {
      FnChooseEvent.fnChoose(nodeIds);
    }
  }
  fnChooseFinish() {
    return FnChooseEvent.fnChooseFinish();
  }
  //根据面的类型获取面的数据
  getAreaInfoByType(type) {
    const areaInstance = LayerManager.get("AREA");
    const areaInfo = areaInstance.getAreaInfoByType(type);
    return areaInfo;
  }
  setGlobalEditMap() {
    setGlobalEditMap(this);
  }

  destroy() {
    this.app.destroy(true, true);
    this.app = null;
    this.viewport = null;
    History.destroy();
    Control.reset();
    Event.reset();
    Mode.resetMode();
    EventBus.$destroy();
    LayerManager.destroy();
    Resource.destroy();
    Store.destroy();
    Cache.clear()
  }
}

export default Edit;
