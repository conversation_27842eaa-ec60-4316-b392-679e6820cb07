export default {
  x: 24, 
  y: 0,
  width: 24, 
  height: 12,

  // 地图数据信息
  chart: {
    // 改为饼图
    type: 'line', // 图表类型

    // 地图数据来源
    request: {
      url: '/athena/stat/robot/fault/sumByDay',  // 请求接口
      filters: ['startTime', 'endTime', 'statType'], // 筛选条件
      defFilters: {
        // 今日0点的时间戳
        startTime: new Date().setHours(0, 0, 0, 0),
        endTime: new Date().setHours(23, 59, 59, 0),
        statType: 'GROUND_MISSING_COUNT'
      },
      timer: 5000,  // 轮询时间, 如果是0, 则不轮询
    },

    // 地图数据处理
    dataHandler: {  // 数据处理
      handler: 'getRobotFaultSumByDay',
      params: {
        type: 'line',
        title: '机器人类型丢码率'
      },
    },
  }
}