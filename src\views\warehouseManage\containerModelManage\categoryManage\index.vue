<template>
  <div class="containerLocation">
    <m-page ref="myPage" :is-pagination="true" :search-sync="searchSync">
      <div class="form-content">
        <m-form ref="myForm" :form-data="formData"></m-form>
      </div>
      <div class="btn-content tr mt10 mb10">
        <el-button class="w100" type="primary" icon="el-icon-plus" @click="handleAddShelfCategory">
          {{ $t("lang.rms.fed.newlyAdded") }}
        </el-button>
      </div>
      <div class="table-content">
        <m-table
          :table-item="tableItem"
          :table-data="tableData"
          :page-data="pageData"
          :extend-config="tableExtendConfig"
          @view="handleView"
          @edit="handleEdit"
          @delete="handleDelete"
        ></m-table>
      </div>
    </m-page>
    <category-dialog
      v-if="dialogVisble"
      :mode="dialogMode"
      :visible.sync="dialogVisble"
      :init-row="dialogRow"
      :category-type-dict="categoryTypeDict"
      @saveSuccess="handleSaveSuccess"
    />
  </div>
</template>
<script>
import { getSearchFormItem, getSearchTableItem } from "./config";
import CategoryDialog from "./components/categoryDialog";
export default {
  components: { CategoryDialog },
  data() {
    return {
      tableData: [],
      pageData: {
        currentPage: 1,
        pageSize: 10,
        recordCount: 0,
      },
      tableExtendConfig: {
        sortNum: false,
        checkBox: false,
        operateWidth: "150px",
        operate: [
          {
            event: "view",
            label: this.$t("lang.rms.fed.buttonView"),
          },
          {
            event: "edit",
            label: this.$t("lang.rms.fed.edit"),
          },
          {
            event: "delete",
            confirm: true,
            confirmMessage: this.$t("lang.rms.fed.confirmDelete"),
            label: this.$t("lang.rms.fed.delete"),
          },
        ],
      },
      dialogVisble: false,
      dialogRow: {},
      categoryTypeDict: [],
    };
  },
  async created() {
    try {
      await this.fetchCategoryTypeDict();
    } catch (e) {}
  },
  computed: {
    formData() {
      return getSearchFormItem({ categoryType: this.categoryTypeDict });
    },
    tableItem() {
      return getSearchTableItem({ categoryType: this.categoryTypeDict });
    },
  },
  methods: {
    // searchSync
    async searchSync({ params }, next) {
      const { code, data } = await $req.post("/athena/shelfCategory/list", params);
      if (code) return;
      this.tableData = data?.recordList || [];
      next(data || []);
    },
    handleAddShelfCategory() {
      this.dialogMode = "add";
      this.dialogVisble = true;
      this.dialogRow = {};
    },
    handleView({ row }) {
      this.dialogMode = "view";
      this.dialogVisble = true;
      this.dialogRow = row;
    },
    handleEdit({ row }) {
      this.dialogMode = "edit";
      this.dialogVisble = true;
      this.dialogRow = row;
    },
    handleSaveSuccess() {
      this.$refs.myPage.renderTable();
    },
    async handleDelete({ row }) {
      const { code } = await $req.get("/athena/shelfCategory/del", { id: row.id });
      if (code) return;
      this.$message.success(this.$t("lang.common.success"));
      this.$refs.myPage.renderTable();
    },
    async fetchCategoryTypeDict() {
      const { code, data } = await $req.get("/athena/shelfCategory/select");
      if (code) return;
      this.categoryTypeDict = Object.keys(data).map(key => ({
        label: this.$t(key),
        value: data[key],
      }));
      return Promise.resolve();
    },
  },
};
</script>
<style lang="less" scoped>
.form-content {
  padding-bottom: 10px;
  border-bottom: 5px solid #eee;
}

.table-content {
  padding-top: 10px;
}
</style>
