<template>
  <!-- 编辑任务 -->
  <div class="editTask">
    <el-form
      class="mform"
      ref="mformRef"
      label-position="left"
      label-width="120px"
      size="mini"
      :model="editTaskData"
      :rules="editTaskRules"
    >
      <!-- 异形货架模型名称 -->
      <el-form-item :label="$t('lang.rms.fed.shapedShelfModelName')" prop="modelName">
        <el-input v-model="editTaskData.modelName" :disabled="editDisabled"></el-input>
      </el-form-item>
      <!-- input 的货架类别 异形货架类型 -->
      <!-- <el-form-item :label="$t('lang.rms.fed.model.heterShelfModelType')" prop="modelType">
          <el-input v-model="editTaskData.modelType" :disabled="editDisabled || isEdit"></el-input>
        </el-form-item> -->
      <!-- select 的货架类别 异形货架类型 -->
      <el-form-item :label="$t('lang.rms.fed.model.heterShelfModelType')" prop="categoryId">
        <el-select v-model="editTaskData.categoryId" :disabled="editDisabled" :placeholder="$t('lang.rms.fed.choose')">
            <el-option v-for="item in shelfCategoryDict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            >
            </el-option>
        </el-select>
      </el-form-item>

      <!-- 支持移动 -->
      <el-form-item :label="$t('lang.rms.web.container.supportMove')">
        <el-select v-model="editTaskData.move" :disabled="editDisabled" :placeholder="$t('lang.rms.fed.choose')">
          <el-option :label="$t('lang.rms.web.container.canNotMove')" :value="0" />
          <el-option :label="$t('lang.rms.web.container.canMove')" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.isImmediatelySendRobot')">
        <el-select
          v-model="editTaskData.needSendRobot"
          :disabled="editDisabled"
          :placeholder="$t('lang.rms.fed.choose')"
        >
          <el-option :label="$t('lang.rms.fed.no')" :value="0" />
          <el-option :label="$t('lang.rms.fed.yes')" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.containerManage.sendModelId.msg')">
        <template #label>
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('lang.rms.containerManage.sendModelId.tips')"
            placement="top"
          >
            <el-button type="text"><i class="el-icon-question" /></el-button>
          </el-tooltip>
          <span>{{ $t("lang.rms.containerManage.sendModelId.msg") }}</span>
        </template>
        <el-input-number
          step-strictly
          v-model="editTaskData.sendModelId"
          :disabled="editDisabled || String(editTaskData.needSendRobot) === '0'"
          :min="0"
          size="mini"
          :step="1"
        />
      </el-form-item>
      <div class="modelTitle">{{ $t("lang.rms.fed.shelfSize") }}</div>
      <el-form-item :label="`${$t('lang.rms.fed.length')}(mm)`">
        <el-input-number
          step-strictly
          size="mini"
          v-model="editTaskData.length"
          :disabled="editDisabled"
          :min="1"
          :max="9000000"
        ></el-input-number>
      </el-form-item>
      <el-form-item :label="`${$t('lang.rms.fed.width')}(mm)`">
        <el-input-number
          step-strictly
          size="mini"
          v-model="editTaskData.width"
          :disabled="editDisabled"
          :min="1"
          :max="9000000"
        ></el-input-number>
      </el-form-item>
      <el-form-item :label="`${$t('lang.rms.fed.shelfHeight')}(mm)`">
        <el-input-number
          step-strictly
          size="mini"
          v-model="editTaskData.height"
          :disabled="editDisabled"
          :min="1"
          :max="9000000"
        ></el-input-number>
      </el-form-item>
      <div class="modelTitle">{{ $t("lang.rms.fed.shelfFoot") }}</div>
      <div v-for="(item, index) in editTaskData.extendJson.legs" :key="index">
        <div class="delLegsItem">
          {{ $t("lang.rms.fed.shelfFoot") }}{{ index + 1 }}
          <i class="el-icon-delete delLegsIcon" v-if="!editDisabled" @click="delLegs(index)"></i>
        </div>
        <el-form-item :label="`${$t('lang.rms.fed.length')}(mm)`">
          <el-input-number
            step-strictly
            size="mini"
            v-model="item.length"
            :disabled="editDisabled"
            :min="1"
            :max="9000000"
          ></el-input-number>
        </el-form-item>
        <el-form-item :label="`${$t('lang.rms.fed.width')}(mm)`">
          <el-input-number
            step-strictly
            size="mini"
            v-model="item.width"
            :disabled="editDisabled"
            :min="1"
            :max="9000000"
          ></el-input-number>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.baseHeight')">
          <el-input-number
            step-strictly
            size="mini"
            v-model="item.substructureHeight"
            :disabled="editDisabled"
            :min="1"
            :max="9000000"
          ></el-input-number>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.coordinateFromXCenter')">
          <el-input-number
            step-strictly
            size="mini"
            v-model="item.rcX"
            :disabled="editDisabled"
            :min="-9000000"
            :max="9000000"
          ></el-input-number>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.coordinateFromYCenter')">
          <el-input-number
            step-strictly
            size="mini"
            v-model="item.rcY"
            :disabled="editDisabled"
            :min="-9000000"
            :max="9000000"
          ></el-input-number>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.needIdentify')">
          <el-select v-model="item.recognition" :placeholder="$t('lang.rms.fed.choose')">
            <el-option :label="$t('lang.rms.fed.no')" :value="0" />
            <el-option :label="$t('lang.rms.fed.yes')" :value="1" />
          </el-select>
        </el-form-item>
      </div>
      <div>
        <el-button type="primary" class="addLegsBtn" :disabled="editDisabled" @click="addLegs"
          >{{ $t("lang.rms.fed.add") }}{{ $t("lang.rms.fed.shelfFoot") }}</el-button
        >
      </div>
    </el-form>

    <div class="floor">
      <el-button size="mini" @click="cancel">{{ $t("lang.common.cancel") }}</el-button>
      <el-button size="mini" type="primary" :loading="saveLoading" @click="sure">{{
        $t("lang.rms.fed.confirm")
      }}</el-button>
    </div>
  </div>
</template>

<script>
import { mapMutations, mapState, mapActions } from "vuex";
const getDefaultData = () => {
  return {
    width: 100, // 宽
    length: 100, // 长
    height: 100, // 高
    move: 0,
    modelName: "",
    // modelType: "",
    categoryId:"",
    sendModelId: 0,
    needSendRobot: 0,
    modelCategory: "HETEROTYPE_SHELF",
    extendJson: {
      legs: [
        {
          length: 1, // 长
          width: 1, // 宽
          rcX: 0, // 距离中心X轴坐标
          rcY: 0, // Y轴坐标
          substructureHeight: 1, // 底座高度
          recognition: 0, //是否需要识别
        },
      ],
    },
  };
};

export default {
  data() {
    return {
      // 编辑任务数据
      editTaskData: getDefaultData(),
      // loading
      saveLoading: false,
    };
  },
  computed: {
    ...mapState("containerModal", [
      "shelfCategoryDict",
      "specialShapedShelfModelViewType",
      "specialShapedShelfModelViewData",
    ]),
    editDisabled() {
      return this.specialShapedShelfModelViewType === "view";
    },
    isEdit() {
      return !!this.specialShapedShelfModelViewData?.id;
    },
    editTaskRules() {
      return {
        modelName: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnter"),
            trigger: "blur",
          },
        ],
        categoryId: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnter"),
            trigger: "blur",
          },
        ],
      };
    },
  },
  created() {
    this.fetchShelfCategory();
    if (this.specialShapedShelfModelViewData) {
      this.editTaskData = JSON.parse(JSON.stringify(this.specialShapedShelfModelViewData));
    }
  },
  watch: {
    editTaskData: {
      handler() {
        this.$emit("change", this.editTaskData);
      },
      immediate: true,
    },
  },
  methods: {
    ...mapActions("containerModal", ["fetchShelfCategory"]),
    ...mapMutations("containerModal", ["setSpecialShapedShelfModelView"]),
    addLegs() {
      this.editTaskData.extendJson.legs.push({
        length: 1, // 长
        width: 1, // 宽
        rcX: 0, // 距离中心X轴坐标
        rcY: 0, // Y轴坐标
        substructureHeight: 1, // 底座高度
        recognition: 0, //是否需要识别
      });
    },
    delLegs(index) {
      const legList = this.editTaskData.extendJson.legs;
      legList.splice(index, 1);
    },
    cancel() {
      this.setSpecialShapedShelfModelView("mainView");
    },
    sure() {
      const { editTaskData } = this;

      // 仅查看
      if (this.editDisabled) {
        this.setSpecialShapedShelfModelView("mainView");
        return;
      }

      const { mformRef } = this.$refs;

      mformRef.validate(validate => {
        if (validate) {
          this.saveLoading = true;
          return $req
            .post("/athena/shelfModel/save", editTaskData)
            .then(res => {
              this.saveLoading = false;
              res.code !== 0 ? this.$message.error(this.$t(res.msg)) : this.cancel();
            })
            .catch(() => {
              this.saveLoading = false;
            });
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.mform {
  flex: 1;
  position: relative;
  overflow: auto;
  padding-bottom: 10px;
}

.editTask {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  width: 350px;
}

.floor {
  text-align: center;
}

:deep(.el-radio) {
  margin-right: 15px;
}

.addLegsBtn {
  width: 100%;
}

.delLegsItem {
  position: relative;
  .delLegsIcon {
    color: #f56c6c;
    cursor: pointer;
  }
}
</style>
  
  