<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2021-12-27 14:30:15
 * @Description:
-->
<template>
  <el-form
    ref="createForm"
    :model="modelData"
    class="creat-form"
    label-position="right"
    label-width="180px"
    :disabled="!editProp"
    :rules="rules"
  >
    <div class="form-group-title division">
      {{ $t("lang.rms.api.result.warehouse.businessCharactreisitcInfo") }}:
    </div>
    <!-- <el-form-item
      :label="$t('lang.rms.api.result.warehouse.businessFeatureNo')"
      prop="businessCode"
    >
      <el-input
        v-model="modelData.businessCode"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterBusinessNo')"
      />
    </el-form-item> -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.businessFeatureModelName')" prop="name">
      <el-input
        v-model="modelData.name"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterBusinessFeatureModelName')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.supportedTaskTypes')" prop="taskTypes">
      <el-select v-model="modelData.taskTypes" multiple :placeholder="$t('lang.rms.fed.choose')">
        <el-option
          v-for="item in dictionary.ROBOT_TASK_TYPE"
          :key="item.fieldCode"
          :label="item.fieldCode"
          :value="item.fieldValue"
        />
      </el-select>
    </el-form-item>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.adaptableChargingStationType')"
      prop="chargerTypes"
    >
      <el-select v-model="modelData.chargerTypes" multiple :placeholder="$t('lang.rms.fed.choose')">
        <el-option v-for="item in chargerTypeList" :key="item" :label="item" :value="item" />
      </el-select>
    </el-form-item>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.supportedBizTypes')"
      prop="bizTypes"
    >
      <el-select v-model="modelData.bizTypes" :placeholder="$t('lang.rms.fed.choose')">
        <el-option v-for="item in bizTypesArr" :key="item.dictKey" :label="$t(item.dictValue)" :value="item.dictKey" />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.supportedSizeType')" prop="sizeTypes">
      <el-select
        v-model="modelData.sizeTypes"
        multiple
        filterable
        allow-create
        default-first-option
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseSelectOrEnterInput')"
        @change="sizeTypesChange(modelData.sizeTypes)"
      >
        <el-option v-for="item in sizeTypeArr" :key="item" :label="item" :value="item" />
      </el-select>
      <div v-if="sizeTypeParamTip" class="error-tip">{{ sizeTypeParamTip }}</div>
    </el-form-item>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.supportedContainerTypes')"
      prop="containerTypes"
    >
      <el-select
        v-model="modelData.containerTypes"
        multiple
        :placeholder="$t('lang.rms.fed.choose')"
      >
        <el-option
          v-for="(item, index) in containerList"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import { mapState } from "vuex";

export default {
  name: "ModelCreateForm",
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    slopProps: {
      type: Object,
      default() {
        return {};
      },
    },
    editProp: {
      type: Boolean,
      default() {
        return false;
      },
    },
    sizeTypeArr: {
      type: Array,
      default: () => [],
    },
    bizTypesArr: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    // 这里存放数据
    return {
      sizeTypeParamTip: null,
      modelData: { ...this.slopProps },
      containerList: [], // [{ label: "容器1", value: 1 }],
      chargerTypeList: [], // ["类型1", "类型2"],
    };
  },
  // 监听属性 类似于data概念
  computed: {
    ...mapState(["dictionary"]),
    rules() {
      return {
        name: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.businessNotEmpty"),
            trigger: "blur",
          },
        ],
        taskTypes: [
          {
            required: true,
            message: this.$t("lang.rms.web.robot.pleaseSelectSupportedTaskType"),
            trigger: "blur",
          },
        ],
        bizTypes: [
          {
            required: true,
            message: this.$t("lang.rms.fed.canNotBeEmpty"),
            trigger: "blur",
          },
        ],
        chargerTypes: [
          {
            required: true,
            message: this.$t(
              "lang.rms.api.result.warehouse.pleaseSelectAdaptableChargingStationType",
            ),
            trigger: "blur",
          },
        ],
        // containerTypes: [
        //   {
        //     required: true,
        //     message: this.$t("lang.rms.api.result.warehouse.pleaseSelectSupportedContainerType"),
        //     trigger: "blur",
        //   },
        // ],
      };
    },
  },
  watch: {
    slopProps(newObj) {
      this.modelData = { ...newObj };
    },
    'modelData.sizeTypes'(val) {
      if (val) this.sizeTypesChange(val)
    },
  },
  mounted() {
    this.getContainerList();
    this.getChargerTypeList();
  },
  methods: {
    // 获取支持容器类型
    getContainerList() {
      $req
        .post("/athena/containerModel/findContainerModelByPage", { currentPage: 1, pageSize: 1000 })
        .then(res => {
          if (res.code !== 0) return;
          let containerList = {};
          const list = res?.data?.recordList || [];
          list.forEach(item => {
            const modelType = item.modelType;
            if (containerList[modelType]) return;
            containerList[modelType] = { label: modelType, value: modelType };
          });
          this.containerList = Object.values(containerList);
        });
    },
    getChargerTypeList() {
      $req.get("/athena/charger/chargerType", {}).then(res => {
        // 型号、任务类型等下拉框元素接口
        if (res.code === 0) {
          this.chargerTypeList = res.data;
        }
      });
    },
    getFormValues() {
      const $form = this.$refs["createForm"];
      return $form.validate();
    },
    resetFormValues() {
      this.$refs["createForm"].resetFields();
    },
    sizeTypesChange(data) {
      if (data.length === 0) {
        this.sizeTypeParamTip = null
        return
      }
      const reg = /^(?!\d)[a-zA-Z0-9]*$/
      if (data) {
        data.forEach((item) => {
          if (reg.test(item)) {
            this.sizeTypeParamTip = null
          } else {
            this.sizeTypeParamTip = this.$t('lang.venus.web.check.canNotStartWithNum')
          }
        })
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.creat-form .el-input,
.creat-form .el-select {
  width: 100%;
}
.form-group-title {
  font-size: 16px;
  font-weight: bold;
  margin: 3px auto;
  text-align: left;
}
.division {
  margin-bottom: 20px;
}
.error-tip {
  position: absolute;
  top: 100%;
  color: red;
  font-size: 12px;
  line-height: 1;
  margin-top: 2px;
}
</style>
