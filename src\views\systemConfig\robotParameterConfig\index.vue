<template>
  <div class="app-container">
    <paramEditDialog ref="editDialog" @save="save" @updateMainTale="getTableList" />
    <robotParamRecordsDialog
      v-if="dialogRobotParamRecordsVisible"
      :dialog-robot-param-records-visible.sync="dialogRobotParamRecordsVisible"
      :item-data="itemData"
      @save="save"
      @update:dialogRobotParamRecordsVisible="getTableList()"
    />
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    </div>
    <!-- 列表信息 -->
    <el-table :data="tableData" style="width: 100%">
      <el-table-column
        :label="$t('lang.rms.fed.lineNumber')"
        width="50"
        align="center"
        :formatter="formatIndex"
      />
      <!--参数名-->
      <el-table-column prop="paramCode" :label="$t('lang.rms.fed.robotParamCode')" align="center" />
      <!--适用机器人型号-->
      <el-table-column
        prop="robotType"
        :label="$t('lang.rms.fed.robotType')"
        width="120"
        :show-overflow-tooltip="true"
        align="center"
      />

      <el-table-column
        prop="paramValueType"
        :label="$t('lang.rms.fed.robotParamValueType')"
        width="120"
        :show-overflow-tooltip="true"
        align="center"
      >
        <template slot-scope="scope">
          {{ $t(paramValueTypeList[scope.row.paramValueType]) }}
        </template>
      </el-table-column>
      <!-- 参数值限制 -->
      <el-table-column
        prop="paramLimit"
        :label="$t('lang.rms.fed.robotParamValueLimit')"
        width="120"
        :show-overflow-tooltip="true"
        align="center"
      />
      <!-- 参数单位 -->
      <el-table-column prop="unit" :label="$t('lang.rms.fed.robotParamUnit')" align="center" />
      <!--描述-->
      <el-table-column prop="descr" :label="$t('lang.rms.fed.describe')" align="center" />
      <el-table-column v-if="!isRoleGuest" :label="$t('lang.rms.fed.operation')" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="itemClick(scope.row, true)">
            {{ $t("lang.rms.fed.edit") }}
          </el-button>
          <!--            <el-button-->
          <!--              type="text"-->
          <!--              size="small"-->
          <!--              @click="itemRobotRecord(scope.row)"-->
          <!--            >-->
          <!--              {{ $t("lang.rms.fed.robotParamRecord") }}-->
          <!--            </el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <geek-pagination
        :current-page="page.currentPage"
        :page-size="page.pageSize"
        :total-page="taskCount"
        @currentPageChange="currentPageChange"
        @pageSizeChange="pageSizeChange"
      />
    </div>
  </div>
</template>
<script>
import paramEditDialog from "./model/dialogParamEdit";
import robotParamRecordsDialog from "./model/dialogRobotParamRecords";
export default {
  name: "RobotParamConfig",
  components: {
    paramEditDialog,
    robotParamRecordsDialog,
  },
  data() {
    return {
      // 搜索内容
      searchData: {
        descriptionChKey: "",
        paramCodeKey: "",
        robotTypeKey: "",
      },
      form: {
        descriptionChKey: "",
        paramCodeKey: "",
        robotTypeKey: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          paramCodeKey: {
            label: "lang.rms.fed.robotParamCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          robotTypeKey: {
            label: "lang.rms.fed.robotType",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          descriptionChKey: {
            label: "lang.rms.fed.describe",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
        },
        rules: [],
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      // 设置table的数据
      tableData: [],
      // 当前数据总数
      taskCount: 0,
      page: {
        pageSize: 10,
        currentPage: 1,
      },

      dialogParamEditVisible: false,
      dialogRobotParamRecordsVisible: false,
      itemData: {},
      // paramValueTypeList: {
      //   0: this.$t('lang.rms.fed.robotParamValueTypeDigit'),
      //   1: this.$t('lang.rms.fed.robotParamValueTypeBool'),
      //   2: this.$t('lang.rms.fed.robotParamValueTypeString'),
      //   3: this.$t('lang.rms.fed.robotParamValueTypeJson')
      // },
      paramValueTypeList: {
        0: "lang.rms.fed.robotParamValueTypeDigit",
        1: "lang.rms.fed.robotParamValueTypeBool",
        2: "lang.rms.fed.robotParamValueTypeString",
        3: "lang.rms.fed.robotParamValueTypeJson",
      },
      isEdit: false,
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  created() {
    this.getTableList();
  },
  methods: {
    /* 格式化内容 */
    formatterTags(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue.join(",");
      } else {
        return cellValue;
      }
    },

    formatImmediate(row, column, cellValue, index) {
      const { immediateList } = this;
      for (let index = immediateList.length - 1; index >= 0; index -= 1) {
        if (immediateList[index].key === cellValue + "") {
          return immediateList[index].value;
        }
      }
      return cellValue;
    },

    formatIndex(row, column, cellValue, index) {
      return ((this.page.pagecurrent - 1) * this.page.pageSize || 0) + index + 1;
    },

    /* 翻页 */
    pageSizeChange(data) {
      this.page.pageSize = data;
      this.getTableList();
    },
    // 跳页
    currentPageChange(currentPage) {
      this.page.currentPage = currentPage;
      this.getTableList();
    },

    // 编辑
    itemClick(row) {
      this.$refs.editDialog.open(row);
    },
    // 编辑
    itemEdit(row) {
      this.$refs.editDialog.open(row);
    },
    // 机器人参数配置
    itemRobotRecord(data) {
      this.itemData = data;
      this.dialogRobotParamRecordsVisible = true;
    },

    // 重置搜索参数
    resetSearchData() {
      for (const key in this.searchData) {
        if (Object.prototype.hasOwnProperty.call(this.searchData, key)) {
          this.searchData[key] = "";
        }
      }
      this.onSearch();
    },

    onQuery(val) {
      this.page.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.page.currentPage = 1;
      this.form = Object.assign({}, val);
      // this.getTableList();
    },
    getTableList() {
      const { form } = this;
      const { descriptionChKey, paramCodeKey, robotTypeKey } = form;
      const data = { language: localStorage.getItem("curRMSLanguage") || "zh_cn" };
      if (descriptionChKey) data.descriptionChKey = descriptionChKey;
      if (paramCodeKey) data.paramCodeKey = paramCodeKey;
      if (robotTypeKey) data.robotTypeKey = robotTypeKey;
      const pageData = "?pageSize=" + this.page.pageSize + "&currentPage=" + this.page.currentPage;

      $req.post("/athena/robot/paramConfig/pageList" + pageData, data).then((data = {}) => {
        const { currentPage = 0, pageSize = 0, pageCount = 0, recordList = [] } = data.data || {};
        this.tableData = recordList.map(item => {
          const descr = this.$t(item.descrI18nCode);
          return { ...item, descr };
        });
        // id
        this.page.pageSize = pageSize;
        this.page.currentPage = currentPage;
        this.taskCount = pageCount;
      });
    },
    // 保存
    // save(data) {
    //   $req.post('/athena/robot/paramConfig/saveParam', data).then(json => {
    //     if (json.code === 0) {
    //       this.$message.success(this.$t(json.msg))
    //       this.getTableList()
    //     }
    //   })
    // }

    // 保存
    save(data) {
      $req.post("/athena/robot/paramConfig/saveParam", data).then(res => {
        if (res.code === 0) this.getTableList();
      });
    },
  },
};
</script>

<style lang="less" scoped>
.app-container {
  background-color: #fff;
  padding: 12px;
}

.form-content {
  padding-bottom: 10px;
  border-bottom: 5px solid #eee;
}
.pagination {
  text-align: right;
}
</style>

<style>
.el-table .cell.el-tooltip {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  max-width: 120px;
}
</style>
<style scoped>
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mt-20 {
  margin-top: 20px;
}
.w_100x {
  width: 100%;
}
.btnwarp {
  padding: 43px 0 0;
}
.floor {
  height: 50px;
}

.floor > .page {
  float: left;
  line-height: 50px;
}

.floor > .jump {
  float: right;
  line-height: 50px;
}
</style>
