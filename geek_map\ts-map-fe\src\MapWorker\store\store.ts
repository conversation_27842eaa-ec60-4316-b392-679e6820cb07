/* ! <AUTHOR> at 2022/08/25 */
import DataResolve from "./resolve-data";

type deadLockRobots = {
  [propName: code]: { code: code; taskId: any; errorCodes: Array<any> };
};
class DataStore {
  private currentFloorIds: { [propName: floorId]: boolean } = {};
  private systemWarning: MWorker.systemWarning = "normal";
  private mapConfig: MWorker.mapConfig = {
    systemState: "",
    stopAreaCount: 0,
    runningAreaCount: 0,
    taskRunning: false,
    systemRunning: false,
    gatherRunning: false,
    sleepRobotsRunning: false,
    awakenRobotsRunning: false,
    shutdownRunning: false,
    shelfTurningRunning: false,
    shelfReturnRunning: false,
    scanRunning: false,
    inSpeedLimitCount: 0,
  };
  private cells: { [propName: code]: cellData } = {}; // 底层地图单元格
  private qrCodes: { [propName: code]: code } = {}; // 底层地图单元格qrCode对应cellCode
  private hostCellCodes: { [propName: code]: code } = {}; // 底层地图单元格hostCellCode对应cellCode
  private shelves: { [propName: code]: shelfData } = {};
  private chargers: { [propName: code]: chargerData } = {};
  private stations: { [propName: code]: stationData } = {};
  private robots: { [propName: code]: robotData } = {};
  private deadLockRobots: deadLockRobots = {};
  private devices: { [propName: code]: deviceData } = {};
  private racks: { [propName: code]: rackData } = {};
  private realtimeObstacles: Array<realtimeObstacleData> = []; // 外部障碍
  private resolve: DataResolve = new DataResolve();

  querySingleData(params: { layer: string; code: code }) {
    const { layer, code } = params;
    let queryData = null;
    switch (layer) {
      case "robot":
        queryData = this.robots[code];
        break;
      case "shelf":
      case "poppick":
        queryData = this.shelves[code];
        break;
      case "cell":
        queryData = this.cells[code];
        if (!queryData) {
          let cellCode = this.qrCodes[code] || this.hostCellCodes[code];
          queryData = this.cells[cellCode];
        }
        break;
      case "rack":
        queryData = this.racks[code];
        break;
      case "charger":
        queryData = this.chargers[code];
        break;
      case "device":
        queryData = this.devices[code];
        break;
      case "station":
        queryData = this.stations[code];
        break;
    }
    if (!queryData) {
      const { warn } = console;
      warn(layer, code, "data 找不到");
    }
    return queryData;
  }

  updateDisplay(body: any, cb: MWorker.wsCallback) {
    const _this = this,
      resolve = _this.resolve,
      floorIds = _this.currentFloorIds;

    let mapDisplay: displays = {};
    let isShelfUpdate = false;

    const displayCells = body.displayCells || []; // 单元格 是个数组！！！;
    if (displayCells.length) {
      const { cells } = resolve.updateCells(displayCells, _this.cells);
      mapDisplay.cells = cells;
    }

    const delShelves: Array<number | string> = body.delShelves || []; // 被删除的货架，shelf | ppp
    if (delShelves.length) {
      delShelves.forEach((code: number | string) => {
        if (_this.shelves[code]) delete _this.shelves[code];
      });
      isShelfUpdate = true;
    }

    const displayShelves = body.displayShelves; // shelves {key,value}
    if (displayShelves) {
      let item, floorId;
      for (let key in displayShelves) {
        item = displayShelves[key];
        floorId = item?.location?.z;
        if (!floorId || !floorIds[floorId]) {
          const warn = console.warn;
          warn("displayShelves 找不到位置或楼层：", item.shelfCode);
          continue;
        }
        _this.shelves[key] = item;
        isShelfUpdate = true;
      }
    }

    const displayRobots = body.displayRobots; // robot {key,value}
    if (displayRobots) {
      const { robots, isOnloadShelf, isDeadLocks, deadCodes } = resolve.updateRobots(
        floorIds,
        displayRobots,
        _this.robots,
        _this.deadLockRobots,
        _this.shelves,
      );
      if (isOnloadShelf) isShelfUpdate = true;
      if (isDeadLocks) {
        cb &&
          cb("wsDeadLockRobots", {
            deadLockCodes: deadCodes,
            deadLockRobots: _this.deadLockRobots,
          });
      }
      mapDisplay.robots = robots;
    }

    if (isShelfUpdate) {
      const { shelves } = resolve.updateShelves(_this.shelves);
      mapDisplay.shelves = shelves;
    }

    const displayRacks = body.displayRacks; // racks {key,value}
    if (displayRacks) {
      const { racks } = resolve.updateRacks(displayRacks, _this.racks);
      mapDisplay.racks = racks;
    }

    const displayWorkStations = body.displayWorkStations; // 工作站 {key,value}; 包含普通工作站 和ppp工作站，stationParks为工作站停靠点(停靠点toto)
    if (displayWorkStations) {
      const { stations } = resolve.updateStations(floorIds, displayWorkStations, this.stations);
      mapDisplay.stations = stations;
    }

    const displayChargers = body.displayChargers; // 充电站 {key,value};
    if (displayChargers) {
      const { chargers } = resolve.updateChargers(floorIds, displayChargers, this.chargers);
      mapDisplay.chargers = chargers;
    }

    const displayDevices = body.displayDevices; // 设备 {key,value};
    if (displayDevices) {
      const { devices, dmpDevices, systemWarning } = resolve.updateDevices(
        floorIds,
        displayDevices,
        _this.devices,
        _this.systemWarning,
      );
      mapDisplay.devices = devices;
      mapDisplay.dmpDevices = dmpDevices;
      if (systemWarning) this.systemWarning = systemWarning;
    }

    const displayKnockAreas = body.displayKnockAreas; // 机器人相撞区域
    if (displayKnockAreas) {
      const { knockAreas } = resolve.resolveKnockAreas(floorIds, displayKnockAreas);
      mapDisplay.knockAreas = knockAreas;
    }

    this.realtimeObstacles = []; // 外部障碍物先给空，重新赋值
    const displayRealtimeObstacles = body.displayRealtimeObstacles || []; // 外部障碍物
    if (displayRealtimeObstacles.length) {
      const { realtimeObstacles } = resolve.resolveRealtimeObstacles(
        floorIds,
        displayRealtimeObstacles,
        this.realtimeObstacles,
      );
      mapDisplay.realtimeObstacles = realtimeObstacles;
    }

    const displayLogicAreas = body.displayLogicAreas || []; // 这里的逻辑区域 仅做业务数据用，没有地图数据呦
    if (displayLogicAreas.length) {
      cb && cb("wsLogicAreasDisplay", displayLogicAreas);
    }

    const displaySpeedLimitAreas = body.displaySpeedLimitAreas || []; // 这里的限速区域 仅做业务数据用，没有地图数据呦
    if (displaySpeedLimitAreas.length) {
      cb && cb("wsSpeedLimitAreasDisplay", displaySpeedLimitAreas);
    }

    cb && cb("wsUpdateDisplay", mapDisplay);
  }

  installDisplay(isInitFinish: boolean, body: any, cb: MWorker.wsCallback) {
    const _this = this;
    const resolve = _this.resolve;
    const floorIds = _this.currentFloorIds;

    let mapDisplay: displays = { isInitFinish };

    const displayShelves = body.displayShelves || {}; // shelves {key,value}
    if (displayShelves) {
      const { shelves } = resolve.resolveShelves(floorIds, displayShelves, _this.shelves);
      mapDisplay.shelves = shelves;
    }

    const displayRacks = body.displayRacks; // racks {key,value}
    if (displayRacks) {
      const { racks } = resolve.resolveRacks(floorIds, displayRacks, _this.racks, _this.cells);
      mapDisplay.racks = racks;
    }

    const displayRobots = body.displayRobots; // robot {key,value}
    if (displayRobots) {
      const { robots } = resolve.formatRobots(floorIds, displayRobots, _this.robots, _this.shelves);
      mapDisplay.robots = robots;
    }

    const displayWorkStations = body.displayWorkStations; // 工作站 {key,value}; 包含普通工作站 和ppp工作站，stationParks为工作站停靠点
    if (displayWorkStations) {
      const { stations } = resolve.resolveStations(floorIds, displayWorkStations, _this.stations);
      mapDisplay.stations = stations;
    }

    const displayChargers = body.displayChargers; // 充电站 {key,value};
    if (displayChargers) {
      const { chargers } = resolve.resolveChargers(floorIds, displayChargers, _this.chargers);
      mapDisplay.chargers = chargers;
    }

    const displayDevices = body.displayDevices; // 设备 {key,value};
    if (displayDevices) {
      const { devices, dmpDevices, systemWarning } = resolve.resolveDevices(
        floorIds,
        displayDevices,
        _this.devices,
        _this.systemWarning,
      );
      mapDisplay.devices = devices;
      mapDisplay.dmpDevices = dmpDevices;
      if (systemWarning) this.systemWarning = systemWarning;
    }

    const displayDeviceItems = body.displayDeviceItems; // 自定义设备 {key,value};
    if (displayDeviceItems) {
      //
    }

    const displayKnockAreas = body.displayKnockAreas; // 机器人相撞区域
    if (displayKnockAreas) {
      const { knockAreas } = resolve.resolveKnockAreas(floorIds, displayKnockAreas);
      mapDisplay.knockAreas = knockAreas;
    }

    const displayRealtimeObstacles = body.displayRealtimeObstacles || []; // 外部障碍物
    if (displayRealtimeObstacles.length) {
      const { realtimeObstacles } = resolve.resolveRealtimeObstacles(
        floorIds,
        displayRealtimeObstacles,
        this.realtimeObstacles,
      );
      mapDisplay.realtimeObstacles = realtimeObstacles;
    }

    const displayLogicAreas = body.displayLogicAreas || []; // 这里的逻辑区域 仅做业务数据用，没有地图数据呦
    if (displayLogicAreas.length) {
      cb && cb("wsLogicAreasDisplay", displayLogicAreas);
    }

    const displaySpeedLimitAreas = body.displaySpeedLimitAreas || []; // 这里的限速区域 仅做业务数据用，没有地图数据呦
    if (displaySpeedLimitAreas.length) {
      cb && cb("wsSpeedLimitAreasDisplay", displaySpeedLimitAreas);
    }

    cb && cb("wsInitDisplay", mapDisplay);
  }

  installFloors(map: any) {
    const floorIds = map.floorIds || [];
    const floors = map.floors || {};
    const released = map.released || false;

    const resFloors = this.resolve.resolveFloorsData(floorIds, floors);
    const { floorsData, floorList, currentFloorIds, storeCells, storeQrCodes, storeHostCellCodes } =
      resFloors;

    this.cells = storeCells;
    this.qrCodes = storeQrCodes;
    this.hostCellCodes = storeHostCellCodes;
    this.currentFloorIds = currentFloorIds;

    return { floorList, floorsData, released };
  }

  installConfig(mapConfig: MWorker.mapConfig) {
    mapConfig = mapConfig || {};
    if (mapConfig["systemState"] === "STOP" || mapConfig["systemState"] === "FIRESTOP ") {
      this.systemWarning = "red";
    }
    for (let key in this.mapConfig) {
      this.mapConfig[key] = mapConfig[key];
    }
  }

  updateMapConfig(mapConfig: MWorker.mapConfig) {
    mapConfig = mapConfig || {};
    if (mapConfig.systemState === "STOP" || mapConfig.systemState === "FIRESTOP ") {
      this.systemWarning = "red";
    } else {
      this.systemWarning = "normal";
    }

    let isUpdate = false;
    let diffConfig: MWorker.mapConfig = {};
    for (let key in this.mapConfig) {
      let oldVal = this.mapConfig[key];
      let newVal = mapConfig[key];
      if (newVal !== oldVal) {
        this.mapConfig[key] = newVal;
        diffConfig[key] = newVal;
        isUpdate = true;
      }
    }
    if (isUpdate) return diffConfig;
    else return null;
  }

  getSystemWarning() {
    return this.systemWarning;
  }

  getMapConfig() {
    return this.mapConfig;
  }

  uninstall() {
    this.currentFloorIds = {};
    this.systemWarning = "normal";
    this.mapConfig = {
      systemState: "",
      stopAreaCount: 0,
      runningAreaCount: 0,
      taskRunning: false,
      systemRunning: false,
      gatherRunning: false,
      sleepRobotsRunning: false,
      awakenRobotsRunning: false,
      shutdownRunning: false,
      shelfTurningRunning: false,
      shelfReturnRunning: false,
      scanRunning: false,
      inSpeedLimitCount: 0,
    };

    this.cells = {}; // 底层地图单元格
    this.qrCodes = {};
    this.shelves = {};
    this.chargers = {};
    this.stations = {};
    this.robots = {};
    this.deadLockRobots = {};
    this.devices = {};
    this.racks = {};

    this.realtimeObstacles = []; // 外部障碍
  }

  destroy() {
    this.resolve = null;
    this.mapConfig = null;
    this.currentFloorIds = null;
    this.systemWarning = null;
    this.mapConfig = null;
    this.cells = null; // 底层地图单元格
    this.qrCodes = null;
    this.shelves = null;
    this.chargers = null;
    this.stations = null;
    this.robots = null;
    this.devices = null;
    this.racks = null;
    this.deadLockRobots = null;
    this.realtimeObstacles = null; // 外部障碍
  }
}
export default DataStore;
