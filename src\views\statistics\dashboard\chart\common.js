let dashRequestTemporaryStorage = [];

/**
* 请求是否读缓存, 为了减少请求次数
* @param {*} url 
* @param {*} params 
* @returns 
*/
export async function requestCache(url, params) {
  // 清除10秒外的请求缓存
  const curTime = + new Date;
  dashRequestTemporaryStorage = dashRequestTemporaryStorage.filter(item => curTime - item.time < 10000);

  const paramsTxt = JSON.stringify(params);
  const filterItem = dashRequestTemporaryStorage.find(item => {
    return url === item.url && paramsTxt === item.params;
  });

  if (filterItem) {
    return await new Promise((resolve) => {
      if (filterItem.wait) {
        filterItem.waitHandelList.push(resolve);
      } else {
        resolve(filterItem.data);
      }
    });
  }

  const tsData = { url, params: paramsTxt, data: null, time: (+ new Date), wait: true, waitHandelList: [] }
  dashRequestTemporaryStorage.push(tsData);

  // 先搜索
  const result = await $req.post(url, params, { intercept: false });
  // 缓存
  tsData.wait = false;
  tsData.data = result;
  tsData.waitHandelList.forEach(item => item(result));
  return result;
}

export const parseEchartOption = (options) => {
  // 如果options.series中数据大于30条, 增加dataZoom
  if (options.series && options.series.length) {
    const seriesLen = options.series[0].data.length;
    if (seriesLen > 30) {
      options.dataZoom = [
        {
          type: "slider",
          xAxisIndex: 0,
          filterMode: "none",
        },
        {
          type: "inside",
          xAxisIndex: 0,
          filterMode: "none",
        },
      ];
    }
  }

  // 如果没有tooltip, 增加tooltip
  if (!options.tooltip) {
    options.tooltip = {
      trigger: 'axis',
      confine: true,
      extraCssText: 'overflow-y:auto;max-height:500px;pointer-events:auto !important;z-index: 2',
    }
  }

  return options;
}

export default class Chart {
  constructor(type, option) {
    this.type = type;
    this.x = option.x;
    this.y = option.y;
    this.width = option.width;
    this.height = option.height;
    this.intervalTimer = option.intervalTimer || 5000;
    this.isFilterParams = false;
    this.filterList = [];
  }

  // 请求前对filterList的检查
  async requestChartData(params = {}) {
    const { filterList, isFilterParams } = this;
    const requestData = {};
    if (isFilterParams) {
      (filterList || []).forEach(filterKey => {
        const value = params[filterKey];
        if (!value) return;
  
        switch (filterKey) {
          case 'date':
            requestData[filterKey] = $utils.Tools.formatDate(value, "yyyy-MM-dd");
            break;
          default:
            requestData[filterKey] = value;
            break;
        }
      })
    } else {
      return params;
    }
    
    return requestData;
  }

  // 请求及请求的处理
  async request(params) {
    return {};
  }

  // 地图数据处理
  async handler(params) {
    return {};
  }
}