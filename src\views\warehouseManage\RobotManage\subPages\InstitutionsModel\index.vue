<template>
  <div class="div_content">
    <SearchWrap>
      <SearchForm @onsubmit="search" />
    </SearchWrap>
    <div style="text-align: right">
      <CreateDialog
        :prop-show="showCreatDialog"
        :edit-prop="editCreatDialog"
        :button-text="$t('lang.rms.api.result.warehouse.robotOntologyModel')"
        :title-text="$t(creatDialogTitle)"
        @createconfirm="createConfirm"
        @createcancel="createCancel"
        @showTrueFalse="showTrueFalse"
      >
        <CreatForm ref="createForm" :slop-props="slopProps" :edit-prop="editCreatDialog" />
      </CreateDialog>
    </div>
    <el-table :loading="loading" :data="recordList" :style="{ width: '100%' }">
      <!-- <el-table-column
        prop="chassisCode"
        :label="$t('lang.rms.api.result.warehouse.bodyNo')"
        min-width="120"
      /> -->
      <el-table-column
        prop="name"
        :label="$t('lang.rms.api.result.warehouse.ontologyName')"
        min-width="120"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.series')"
        prop="series"
        min-width="120"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.lengthWidthHeight')"
        prop="length"
        min-width="120"
      >
        <template slot-scope="scope">
          <span>{{ `${scope.row.length}/${scope.row.width}/${scope.row.height}` }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('lang.rms.fed.rotationDiameter') + '(mm)'"
        prop="diameter"
        min-width="120"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.bodyWeight')"
        prop="weight"
        min-width="120"
      />
      <!-- <el-table-column
        :label="$t('lang.rms.api.result.warehouse.xDirection')"
        prop="headOffsetRatio"
        min-width="120"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.yDirection')"
        prop="tailOffsetRatio"
        min-width="120"
      /> -->
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.navigationMode')"
        prop="navigationModes"
        min-width="240"
      >
        <template slot-scope="scope">
          <el-tag
            v-for="item in scope.row.navigationModes"
            :key="item"
            type="info"
            style="margin-right: 10px; margin-bottom: 2px"
            >{{ navigationModesMap[+item] }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.maxDrivingSpeed')"
        prop="maxVelocity"
        min-width="120"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.maxTuringSpeed')"
        prop="maxAngularVelocity"
        min-width="120"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.maxAcceleration')"
        prop="maxAcceleration"
        min-width="120"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.moveMode')"
        prop="movingModes"
        min-width="140"
      >
        <template slot-scope="scope">
          <el-tag
            v-for="item in scope.row.movingModes"
            :key="item"
            type="info"
            style="margin-right: 10px; margin-bottom: 2px"
            >{{ item }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.maxDistanceError')"
        prop="maxPositionError"
        min-width="160"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.batteryType')"
        prop="batteryType"
        min-width="120"
      >
        <template slot-scope="scope">
          <span>{{ $t(batteryTypeMap[+scope.row.batteryType]) }}</span>
        </template>
      </el-table-column>
      <!-- 电池最大毫安时 -->
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.batteryMaxMah')"
        prop="maxBatteryCapacity"
        min-width="120"
      />
      <!-- 工作耗电量 -->
      <!-- <el-table-column
        :label="$t('lang.rms.api.result.warehouse.workingPowerConsumption')"
        prop="workingTimePerPercent"
        min-width="120"
      /> -->
      <!-- 空闲耗电量 -->
      <!-- <el-table-column
        :label="$t('lang.rms.api.result.warehouse.idlePowerConsumption')"
        prop="idleTimePerPercent"
        min-width="120"
      /> -->
      <!-- 休眠耗电量 -->
      <!-- <el-table-column
        :label="$t('lang.rms.api.result.warehouse.sleepPowerConsumption')"
        prop="sleepingTimePerPercent"
        min-width="120"
      /> -->
      <el-table-column
        fixed="right"
        :label="$t('lang.rms.fed.textOperation')"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="checkListItemInfo(scope.row)">
            {{ $t("lang.rms.fed.buttonView") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="copyListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.web.map.version.copy") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="editListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.fed.buttonEdit") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="deleteListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.fed.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="text-align: right; margin-top: 30px">
      <el-pagination
        background
        layout="total, prev, pager, next, sizes, jumper"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="paginationParams.total"
        :page-size="paginationParams.pageSize"
        :current-page="paginationParams.currentPage"
        @current-change="paginationChange"
        @size-change="paginationPageChange"
      />
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import SearchWrap from "../../Components/SearchWrap";
import CreateDialog from "../../Components/CreateDialog";
import CreatForm from "./Components/CreatForm";
import SearchForm from "./Components/SearchForm";
import robotManageRequest from "@/api/robotManage";

export default {
  name: "InstitutionsModel",
  components: {
    CreatForm,
    SearchWrap,
    CreateDialog,
    SearchForm,
  },
  data() {
    return {
      batteryTypeMap: [
        "lang.rms.fed.battery.lto",
        "lang.rms.fed.battery.lfpo",
        "lang.rms.api.result.warehouse.LithiumTernary",
      ],
      navigationModesMap: ["SLAM", "QR"],
      slopProps: {},
      showCreatDialog: false,
      editCreatDialog: true,
      creatDialogTitle: "lang.rms.api.result.warehouse.createRobotChassisModel",
      copyDataOpen: false,
      loading: false,
      recordList: [],
      paginationParams: { pageSize: 20, currentPage: 1, total: 0 },
      searchFormData: {},
      viewDialogClose: false,
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  watch: {},
  methods: {
    search(searchFormData) {
      const { paginationParams } = this;
      if (searchFormData.isReset) {
        paginationParams.currentPage = 1;
        delete searchFormData.isRest;
      }
      this.searchFormData = searchFormData;

      this.reqTableList(searchFormData, paginationParams);
    },
    reqTableList(searchFormData, paginationParams) {
      this.loading = true;
      const params = {
        ...searchFormData,
        ...paginationParams,
      };
      robotManageRequest.ontologyModelSearch(params).then(({ data }) => {
        const { pageSize, currentPage, recordList, recordCount } = data;
        this.recordList = recordList;
        this.paginationParams = {
          pageSize,
          currentPage,
          total: recordCount,
        };
        this.loading = false;
      });
    },
    reReqTableList() {
      this.paginationParams.currentPage = 1;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationChange(currentPage) {
      this.paginationParams.currentPage = currentPage;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationPageChange(pageSize) {
      this.paginationParams.pageSize = pageSize;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    createConfirm() {
      if (this.viewDialogClose) {
        this.createCancel();
        return false;
      }
      if (this.editCreatDialog) {
        this.showCreatDialog = true;
        const $createForm = this.$refs["createForm"];
        $createForm.getFormValues().then(val => {
          if (val) {
            const { modelData } = $createForm;
            const paramsObj = { ...modelData, localEnvelope: JSON.stringify(modelData.localCover) };
            if (!this.slopProps.id || this.copyDataOpen) {
              delete paramsObj.id;
            }
            if (Object.prototype.toString.call(paramsObj.file) === "[object String]") {
              delete paramsObj.file;
            }
            // delete paramsObj.chassisImage
            delete paramsObj.localCover;

            const formData = new FormData();
            for (const key in paramsObj) {
              formData.append(key, paramsObj[key] ?? "");
            }
            robotManageRequest.createRobotModel(formData).then(({ code }) => {
              if (+code === 0) {
                console.log("添加/修改成功！");
              }
              this.createCancel();
              this.reReqTableList();
              this.confirmNext();
            });
          }
        });
      }
    },
    showTrueFalse() {
      this.slopProps = {
        maxPositionError: 150
      };
    },
    createCancel() {
      this.$refs["createForm"].resetFormValues();
      this.slopProps = {};
      this.editCreatDialog = true;
      this.showCreatDialog = false;
      this.copyDataOpen = false;
      this.viewDialogClose = false;
      this.creatDialogTitle = "lang.rms.api.result.warehouse.createRobotChassisModel";
    },
    openDialogInSomeType(edit, data) {
      const {
        id,
        name,
        // chassisCode,
        series,
        chassisImage,
        length,
        width,
        height,
        diameter,
        weight,
        headOffsetRatio,
        tailOffsetRatio,
        localCover,
        navigationModes,
        maxVelocity,
        maxAngularVelocity,
        maxAcceleration,
        movingModes,
        maxPositionError,
        controlAbilities,
        maxObstacleDistance,
        batteryType,
        maxBatteryCapacity,
        workingTimePerPercent,
        idleTimePerPercent,
        sleepingTimePerPercent,
      } = data;
      this.slopProps = {
        id,
        name,
        // chassisCode,
        series,
        chassisImage,
        length,
        width,
        height,
        diameter,
        weight,
        headOffsetRatio,
        tailOffsetRatio,
        localCover,
        navigationModes,
        maxVelocity,
        maxAngularVelocity,
        maxAcceleration,
        movingModes,
        maxPositionError,
        controlAbilities,
        maxObstacleDistance,
        batteryType,
        maxBatteryCapacity,
        workingTimePerPercent,
        idleTimePerPercent,
        sleepingTimePerPercent,
      };
      this.slopProps.batteryType = String(this.slopProps.batteryType || "2");
      this.editCreatDialog = edit;
      this.showCreatDialog = true;
      this.creatDialogTitle = edit
        ? "lang.rms.api.result.warehouse.EditRobotModel"
        : "lang.rms.api.result.warehouse.viewRobotModel";
    },
    // 查看
    checkListItemInfo(robotData) {
      robotManageRequest.getRobotRobotModelDetailInfo({}, { id: robotData.id }).then(({ data }) => {
        this.viewDialogClose = true;
        this.openDialogInSomeType(false, data);
      });
    },
    // 复制
    copyListItemInfo(robotData) {
      robotManageRequest.getRobotRobotModelDetailInfo({}, { id: robotData.id }).then(({ data }) => {
        this.copyDataOpen = true;
        this.openDialogInSomeType(true, data);
      });
    },
    // 编辑
    editListItemInfo(robotData) {
      robotManageRequest.getRobotRobotModelDetailInfo({}, { id: robotData.id }).then(({ data }) => {
        this.openDialogInSomeType(true, data);
      });
    },
    // 删除
    deleteListItemInfo(robotData) {
      this.$confirm(
        this.$t("lang.rms.api.result.warehouse.willDeleteToContinue"),
        this.$t("lang.rms.fed.prompt"),
        {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.rms.fed.cancel"),
          type: "warning",
        },
      )
        .then(() => {
          robotManageRequest.deleteRobotItem({}, { id: robotData.id }).then(({ code }) => {
            if (+code === 0) {
              this.$message({
                type: "success",
                message: this.$t("lang.venus.web.common.successfullyDeleted"),
              });
            }
            this.reReqTableList();
          });
        })
        .catch(() => null);
    },
    async confirmNext() {
      try {
        await this.$confirm(
          this.$t("lang.rms.fed.gotoSomePage", [
            this.$t("lang.rms.api.result.warehouse.mechanismModel"),
          ]),
        );
        this.$emit("goNextPage");
      } catch (e) {}
    },
  },
};
</script>
<style lang="scss" scoped>
.div_content {
  // height: calc( 100vh - 160px );
  // overflow: hidden;
}
.align-bottom {
  vertical-align: bottom;
}
</style>
