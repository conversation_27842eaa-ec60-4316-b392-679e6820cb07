<template>
  <div>
    <!-- 主表弹框 -->
    <m-dialog :visible="visible" :before-save="beforeSave" @closed="closed" @save="save">
      <!-- 标题 -->
      <template #title>
        <span class="f16">{{ $t("lang.rms.fed.edit") }}</span>
      </template>
      <!-- 编辑 -->
      <div class="ui-containerBox">
        <m-form
          ref="myForm"
          :form-data="formData"
          label-position="right"
          :extend-config="extendFormConfig"
          :label-width="150"
        />
      </div>
    </m-dialog>
  </div>
</template>

<script>
import { getEditFormData } from "../data";

export default {
  props: {
    visible: Boolean,
    initRow: Object,
  },
  data() {
    return {
      extendFormConfig: {
        isNeedBtn: false,
      },
    };
  },
  computed: {
    formData() {
      return getEditFormData(this, this.initRow);
    },
  },
  methods: {
    // 保存前
    beforeSave() {
      return new Promise((resolve, reject) => {
        this.$refs.myForm
          .getValidateFormModel()
          .then(formData => {
            const params = {
              logicId: String(this.initRow.logicId),
              ...formData,
            };

            $req
              .post("/athena/area/updateById ", params)
              .then(res => {
                // 工作站查询
                const { code } = res;
                if (code) {
                  reject();
                  return;
                }
                resolve();
              })
              .catch(() => reject());
          })
          .catch(() => reject());
      });
    },
    // 保存成功关闭弹框
    save() {
      this.$emit("saveSuccess");
    },
    // 关闭
    closed() {
      this.$emit("update:visible", false);
    },
  },
};
</script>
<style lang="less">
.ui-containerBox .el-input--prefix .el-input__inner {
  padding-left: 15px;
}
.p12 {
  padding: 0 12px;
}
</style>
