class UserManager {
  constructor() {
    this.editStatusUserList = [];
    this.createStatusUserList = [];
    this.name = "";
    this.descr = "";
  }
  // 静态方法
  static getInstance() {
    if (!this.instance) {
      this.instance = new UserManager();
    }
    return this.instance;
  }
  // 存
  saveUser(user, type) {
    if (type === "create") {
      if (!this.createStatusUserList.includes(user)) {
        this.createStatusUserList.push(user);
      }
    } else if (type === "edit") {
      if (!this.editStatusUserList.includes(user)) {
        this.editStatusUserList.push(user);
      }
    }
  }
  saveUserInfo(userInfo) {
    (this.name = userInfo.name), (this.descr = userInfo.descr);
  }
  // 取
  getUserList(type) {
    if (type === "create") {
      return this.createStatusUserList;
    } else if (type === "edit") {
      return this.editStatusUserList;
    }
  }

  getEditStatusUserInfo() {
    return {
      name: this.name,
      descr: this.descr,
    };
  }

  // 清
  clear(type) {
    if (type === "create") {
      this.createStatusUserList.length = 0;
    } else {
      this.editStatusUserList.length = 0;
    }
  }
}

export default UserManager.getInstance();
