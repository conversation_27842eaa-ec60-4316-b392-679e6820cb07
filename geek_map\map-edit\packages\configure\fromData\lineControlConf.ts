// 线段 - 控制

import { NodeAttrEditConf } from "@packages/type/editUiType";
import { useI18n } from "@packages/hook/useI18n";
import { LINE_CONTROL_SUPPORTSIDEWAY_DICT } from "@packages/configure/dict/supportSideWayDict";
export const LINE_CONTROL_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  const { t } = useI18n();
  return {
    name: "line-control",
    tabTitle: "lang.rms.fed.control",
    formItem: [
      {
        label: "lang.rms.fed.speedLimit",
        describe: `${t("lang.rms.fed.robotSegmentMaxSpeedLimit")}m/s`,
        isTitle: true,
      },
      {
        prop: "unloadMaxSpeed",
        label: "lang.rms.fed.noLoad",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
      },
      {
        prop: "loadMaxSpeed",
        label: "lang.rms.fed.load",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
      },
      {
        label: "lang.rms.fed.obsAvoWperToVehicleHead",
        describe: `${t("lang.rms.fed.robotSegmentObstacleWidth")}/mm`,
        isTitle: true,
      },
      {
        prop: "unloadObstacleRangeAround",
        label: "lang.rms.fed.noLoad",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
      },
      {
        prop: "loadObstacleRangeAround",
        label: "lang.rms.fed.load",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
      },
      {
        label: "lang.rms.fed.obsAvoHheadingDirection",
        describe: `${t("lang.rms.fed.robotSegmentObstacleWidth")}/mm`,
        isTitle: true,
      },
      {
        prop: "unloadObstacleRange",
        label: "lang.rms.fed.noLoad",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
      },
      {
        prop: "loadObstacleRange",
        label: "lang.rms.fed.load",
        component: "elInputNumber",
        precision: 1,
        min: -100000,
        max: 100000,
        step: 1,
      },
      // 是否横移
      {
        label: "lang.rms.fed.whetherEnableTraverse",
        describe: `lang.rms.fed.whetherEnableTraverseMsg`,
        isTitle: true,
      },
      {
        prop: "supportSideWay",
        label: "",
        component: "elRadioGroup",
        data: LINE_CONTROL_SUPPORTSIDEWAY_DICT || []
      }, 
    ],
  };
};
