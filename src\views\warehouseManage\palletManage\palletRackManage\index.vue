<template>
  <component
    :is="currentCom"
    :mode="mode"
    :row-detail.sync="row"
    @updateCom="updateCom"
    @update:row-detail="rowDetail = $event"
  />
</template>
<script>
import PalletRackManageList from "./components/list.vue";
import PalletRackManageDetail from "./components/detail.vue";
export default {
  components: {
    PalletRackManageList,
    PalletRackManageDetail,
  },
  data() {
    return {
      currentCom: "PalletRackManageList",
      mode: "",
      row: {},
    };
  },
  methods: {
    updateCom({ currentCom, mode, row = {} }) {
      this.currentCom = currentCom;
      this.mode = mode;
      this.row = row;

      console.log(this.currentCom, this.mode, this.row);
    },
  },
};
</script>
