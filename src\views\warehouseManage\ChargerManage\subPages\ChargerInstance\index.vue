<template>
  <div class="charger-main">
    <SearchWrap>
      <SearchForm @onsubmit="onSubmit" />
    </SearchWrap>
    <div>
      <div class="detail-dialog">
        <el-dialog
          :title="$t('lang.rms.web.charger.telemetryPacket')"
          :visible.sync="dialogVisible"
          :show-close="true"
        >
          <p v-if="currentCharger.telemetryPacket">{{ currentCharger.telemetryPacket }}</p>
          <p v-else class="t-c">{{ $t("lang.rms.fed.notExistData") }}</p>
        </el-dialog>
      </div>
    </div>
    <div style="text-align: right; width: 90%">
      <el-button type="primary" @click="changeStatus(1)">{{ $t("lang.rms.fed.enable") }}</el-button>
      <el-button type="primary" @click="changeStatus(0)">
        {{ $t("lang.rms.fed.stopStatus") }}
      </el-button>
      <el-button type="primary" :disabled="restartRobotDisabled" @click="restartCharger()">
        {{ $t("lang.rms.fed.restartRobot") }}
      </el-button>
    </div>
    <el-table
      ref="selectChargers"
      v-loading="loading"
      :data="recordList"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="40" />
      <el-table-column prop="chargerId" :label="$t('lang.rms.web.charger.chargerId')" width="80" />
      <el-table-column prop="hostCode" :label="$t('lang.rms.web.charger.hostCode')" />
      <el-table-column prop="manageStatusI18n" :label="$t('lang.rms.web.charger.status')">
        <template slot-scope="scope">
          {{ $t(scope.row.manageStatusI18n) }}
        </template>
      </el-table-column>
      <el-table-column prop="workStatusDesc" :label="$t('lang.rms.web.charger.workStatus')">
        <template slot-scope="scope">
          <span v-if="scope.row.workStatus == 2">
            {{ scope.row.robotId + $t(scope.row.workStatusDesc) }}
          </span>
          <span v-if="scope.row.workStatus != 2">{{ $t(scope.row.workStatusDesc) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="connectionStatusI18n" :label="$t('lang.rms.fed.connectState')">
        <template slot-scope="scope">
          {{ $t(scope.row.connectionStatusI18n ? scope.row.connectionStatusI18n : "-") }}
        </template>
      </el-table-column>
      <el-table-column prop="errorStatusI18n" :label="$t('lang.rms.fed.errorState')">
        <template slot-scope="scope">
          {{ $t(scope.row.errorStatusI18n ? scope.row.errorStatusI18n : "-") }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="mapName" :label="$t('lang.rms.web.station.inMap')" /> -->
      <!-- <el-table-column prop="floorId" :label="$t('lang.rms.web.station.location')" width="120px">
        <template slot-scope="scope">
          <span v-if="scope.row.floorId">
            {{ scope.row.floorId + $t("lang.rms.web.edit.floor") + "," + scope.row.locationXY }}
          </span>
        </template>
      </el-table-column> -->
      <el-table-column prop="cellCode" :label="$t('lang.rms.web.station.cellCode')" />
      <el-table-column prop="interactiveModelDesc" :label="$t('lang.rms.web.charger.interactive')">
        <template slot-scope="scope">
          {{ $t(scope.row.interactiveModelDesc) }}
        </template>
      </el-table-column>
      <el-table-column prop="type" :label="$t('lang.rms.web.charger.type')" />
      <!-- 机器人id -->
      <el-table-column prop="robotId" :label="$t('lang.rms.fed.chargerRobotID')" />
      <!-- 预分配机器人 -->
      <el-table-column prop="preAllocatedRobotId" :label="$t('lang.rms.fed.preAllocatedRobotId')" />
      <!-- <el-table-column prop="protocol" :label="$t('lang.rms.web.charger.protocol')" /> -->
      <!-- <el-table-column prop="angle" :label="$t('lang.rms.fed.angle')">
        <template slot-scope="scope">{{ scope.row.angle }}°</template>
      </el-table-column> -->
      <!-- <el-table-column :label="$t('lang.rms.web.charger.telemetryPacket')" width="220">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleShowDetail(scope.row)">
            {{ $t("lang.rms.fed.textDetails") }}
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <div style="text-align: right; margin-top: 30px">
      <el-pagination
        background
        layout="total,prev, pager, next, sizes, jumper"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="paginationParams.pageSize"
        :total="paginationParams.total"
        @current-change="paginationChange"
        @size-change="sizeChange"
      />
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import SearchWrap from "../../Components/SearchWrap";
import SearchForm from "./Components/SearchForm";
import chargerManageRequest from "@/api/chargerManage";

export default {
  components: {
    SearchWrap,
    SearchForm,
  },
  data() {
    return {
      demo: "123",
      ruleForm: {
        user: "",
        region: "",
      },
      rules: {
        user: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
          { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" },
        ],
        region: [{ required: true, message: "请选择活动区域", trigger: "change" }],
      },
      loading: false,
      recordList: [{}],
      dialogVisible: false,
      currentCharger: {},
      paginationParams: { pageSize: 20, currentPage: 1, total: 0 },
      searchFormData: {},
      restartRobotDisabled: false,
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  created: function () {
    this.reReqTableList();
  },
  // 方法集合
  methods: {
    handleSelectionChange(val) {
      // manageStatus  1启用  0 停用
      const stop = val.filter(item => item.manageStatus === 0);
      this.restartRobotDisabled = stop.length > 0;
    },
    onSubmit(searchFormData) {
      const { paginationParams } = this;
      this.searchFormData = searchFormData;

      this.reqTableList(searchFormData, paginationParams);
    },
    reqTableList(searchFormData, paginationParams) {
      this.loading = true;
      chargerManageRequest
        .getChargerPageList(Object.assign(searchFormData, paginationParams))
        .then(({ data }) => {
          const { pageSize, currentPage, recordList, recordCount } = data;
          this.recordList = recordList;
          this.paginationParams = {
            pageSize,
            currentPage,
            total: recordCount,
          };
          this.loading = false;
        });
    },
    reReqTableList() {
      this.paginationParams.currentPage = 1;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    sizeChange(currentSize) {
      this.paginationParams.pageSize = currentSize;
      this.paginationParams.currentPage = 1;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationChange(currentPage) {
      this.paginationParams.currentPage = currentPage;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleShowDetail(row) {
      this.currentCharger = row;
      this.dialogVisible = true;
    },
    enableChargers(chargerIds) {
      chargerManageRequest.enableCharger(chargerIds).then(data => {
        this.$message({ showClose: false, message: this.$t(data.msg), type: "success" });
        this.reReqTableList();
      });
    },
    disableChargers(chargerIds) {
      chargerManageRequest.disableCharger(chargerIds).then(data => {
        this.$message({ showClose: false, message: this.$t(data.msg), type: "success" });
        this.reReqTableList();
      });
    },
    changeStatus(status) {
      const chargerIds = [];
      const selectArr = this.$refs.selectChargers.selection;
      selectArr.forEach(item => {
        chargerIds.push(item.chargerId);
        // if (item.manageStatus !== status) {
        //   chargerIds.push(item.chargerId)
        // }
      });
      // for (const index in this.$refs.selectChargers.selection) {
      //   const item = this.$refs.selectChargers.selection[index]
      //   if (item.manageStatus !== status) {
      //     chargerIds.push(item.chargerId)
      //   }
      // }
      if (chargerIds.length < 1) {
        this.$message({
          showClose: false,
          message: this.$t("lang.rms.fed.chooseCharger"),
          type: "warning",
        });
        return;
      }

      if (status === 1) {
        this.enableChargers(chargerIds);
      } else {
        this.disableChargers(chargerIds);
      }
    },
    restartCharger() {
      const chargerIds = [];
      const selectArr = this.$refs.selectChargers.selection;
      selectArr.forEach(item => {
        chargerIds.push(item.chargerId);
      });
      if (chargerIds.length < 1) {
        this.$message({
          showClose: false,
          message: this.$t("lang.rms.fed.chooseCharger"),
          type: "warning",
        });
        return;
      }

      chargerManageRequest.restartCharger(chargerIds).then(data => {
        this.$message({ showClose: false, message: this.$t(data.msg), type: "success" });
        this.reReqTableList();
      });
    },
  },
  // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
.charger-main {
  width: 100%;
}
.align-bottom {
  vertical-align: bottom;
}
.t-c {
  text-align: center;
}
:deep(.el-table .el-table__row .el-table__cell div) {
  word-break: break-all;
}
</style>
