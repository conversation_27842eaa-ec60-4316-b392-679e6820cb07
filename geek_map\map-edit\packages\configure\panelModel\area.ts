import { ToolPanelType } from "@packages/type/editUiType";
import {
  AREA_TRAFFIC_LIGHT,
  AREA_STOP,
  AREA_ROBOT,
  AREA_SHELF,
  AREA_TASK_CONTROL,
  AREA_NO_STAY,
  AREA_BLOCK,
  AREA_SINGLE_LANE,
  AREA_TRAFFIC_CONTROL,
  AREA_SORTING_AREA,
  AREA_RESTRICT_BIG_ARC_AREA, 
  AREA_CUSTOM_AREA
} from "@packages/configure/dict/nodeType";

// 添加区域
export const ADD_TRAFFIC_LIGHT_AREA: ToolPanelType = {
  option: {
    icon: "map-font-baojing",
    name: "addTrafficLightArea",
    describe: "lang.rms.fed.area.trafficLights",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_TRAFFIC_LIGHT,
  },
};
// 区域设置-系统急停区域
export const ADD_STOP_AREA: ToolPanelType = {
  option: {
    icon: "map-font-stop",
    name: "addStop<PERSON>rea",
    describe: "lang.rms.fed.area.systemStop",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_STOP,
  },
};

// 区域 - 交通管制区域
export const ADD_TRAFFIC_CONTROL_AREA: ToolPanelType = {
  option: {
    icon: "map-font-jiaotongguanzhi-copy",
    name: "addTrafficControl",
    describe: "lang.rms.fed.search.type.logic.area.trafficControl",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_TRAFFIC_CONTROL,
  },
};

// 区域设置 - 机器人调度;
export const ADD_ROBOT_AREA: ToolPanelType = {
  option: {
    icon: "map-font-robot",
    name: "addRobotArea",
    describe: "lang.rms.map.edit.area.guidance.robotArea",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_ROBOT,
  },
};

// 区域设置 - 货架区域;
export const ADD_SHELF_AREA: ToolPanelType = {
  option: {
    icon: "map-font-shinshophuojia",
    name: "addShelfArea",
    describe: "lang.rms.map.edit.shelfArea",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_SHELF,
  },
};

// 区域设置 - 任务控制区域;
export const ADD_TASK_CONTROL_AREA: ToolPanelType = {
  option: {
    icon: "map-font-task-limit",
    name: "addTaskControlArea",
    describe: "lang.rms.fed.area.controlTask",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_TASK_CONTROL,
  },
};

// 区域设置 - 禁止停留区域;
export const ADD_NO_STAY_AREA: ToolPanelType = {
  option: {
    icon: "map-font-no-stay",
    name: "addNoStayArea",
    describe: "lang.rms.fed.search.type.logic.area.noStay",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_NO_STAY,
  },
};

// 区域设置 - 分拣区;
export const ADD_SORTING_AREA: ToolPanelType = {
  option: {
    icon: "map-font-ercifenjian",
    name: "addSortingArea",
    describe: "分拣区",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_SORTING_AREA,
  },
};

// 区域设置 - 地图区块;
export const ADD_BLOCK_AREA: ToolPanelType = {
  option: {
    icon: "map-font-quyu",
    name: "addBlockArea",
    describe: "lang.rms.map.edit.mapBlock",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_BLOCK,
  },
};

// 区域设置 - 单行道独木桥
export const ADD_ONEWAY_STREET: ToolPanelType = {
  option: {
    icon: "map-font-chedaozhanyong",
    name: "addOneWayStreet",
    describe: "lang.rms.fed.area.singlaneAndBridge",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: AREA_SINGLE_LANE,
  },
};

// 区域设置 - 自定义蒙层
export const ADD_CUSTOM: ToolPanelType = {
  option: {
    icon: "map-font-mengban",
    name: "addCustom",
    describe: "lang.rms.fed.area.customArea",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: AREA_CUSTOM_AREA,
  },
};

// 区域设置 - 限制弧线转弯区域
export const ADD_RESTRICT_BIG_ARC_AREA: ToolPanelType = {
  option: {
    icon: "map-font-mengban",
    name: "addRestrictBigArcArea",
    describe: "限制弧线转弯区域",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: AREA_RESTRICT_BIG_ARC_AREA,
  },
};
