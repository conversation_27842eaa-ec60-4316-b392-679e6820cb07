<template>
  <!-- 角色列表 -->
  <geek-main-structure class="old-role-list">
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    </div>
    <div class="table-content">
      <div class="handle-btn">
        <el-button
          v-if="checkPermission('AuthAddRolePermission')"
          type="primary"
          size="small"
          @click="onCreate"
        >
          {{ $t("lang.rms.fed.add") }}
        </el-button>
      </div>
      <customize-table :table-config="tableConfig">
        <template #type="{ column }">
          <el-table-column v-bind="column" :label="$t(column.label)">
            <template slot-scope="scope">
              <span>{{ scope.row.type | typeFilter(i18) }}</span>
            </template>
          </el-table-column>
        </template>
        <template #system="{ column }">
          <el-table-column v-bind="column" :label="$t(column.label)">
            <template #default>
              <span>RMS</span>
            </template>
          </el-table-column>
        </template>
        <template #status="{ column }">
          <el-table-column v-bind="column" :label="$t(column.label)">
            <template slot-scope="scope">
              <span>{{ scope.row.status | statusFilter(i18) }}</span>
            </template>
          </el-table-column>
        </template>
        <template #createTime="{ column }">
          <el-table-column v-bind="column" :label="$t(column.label)">
            <template slot-scope="scope">
              <span>{{ setTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
        </template>
        <template #updateTime="{ column }">
          <el-table-column v-bind="column" :label="$t(column.label)">
            <template slot-scope="scope">
              <span>{{ setTime(scope.row.updateTime) }}</span>
            </template>
          </el-table-column>
        </template>
        <template #operations="{ column }">
          <el-table-column v-bind="column" :label="$t(column.label)">
            <template slot-scope="scope">
              <!-- <div v-if="scope.row.roleId !== 1"> -->
              <el-button
                v-if="checkPermission('AuthEditRolePermission')"
                type="text"
                size="small"
                @click="handleEditRole(scope.row)"
              >
                {{ $t("lang.rms.fed.buttonEdit") }}
              </el-button>
              <el-button
                v-if="checkPermission('AuthDelRole')"
                type="text"
                size="small"
                :disabled="[1, 2].includes(scope.row.roleId)"
                @click="handleDeleteRole(scope.row)"
              >
                {{ $t("lang.rms.fed.buttonDelete") }}
              </el-button>
              <el-button
                v-if="checkPermission('AuthEnableRole')"
                type="text"
                size="small"
                :disabled="[1, 2].includes(scope.row.roleId)"
                @click="handleEnableRole(scope.row)"
              >
                <p>{{ scope.row.status | statusButtonFilter(i18) }}</p>
              </el-button>
              <!-- </div> -->
            </template>
          </el-table-column>
        </template>
      </customize-table>
      <div style="text-align: right">
        <el-pagination
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          :total="page.total"
          :page-sizes="[10, 25, 50, 100]"
          layout="sizes, prev, pager, next"
          background
          class="geek-pagination"
          @size-change="pageSizeChange"
          @current-change="currentPageChange"
        />
      </div>
    </div>
  </geek-main-structure>
</template>

<script>
import { mapMutations } from "vuex";
import CustomizeTable from "../../../components/customize-table";
export default {
  components: {
    CustomizeTable,
  },
  filters: {
    typeFilter: function (value, that) {
      let typeText = "";
      if (value === 1) {
        typeText = that("lang.rms.fed.page");
      }
      return typeText;
    },
    statusFilter: function (value, that) {
      let statusText = "";
      if (value === 0) {
        statusText = that("lang.rms.fed.textProhibit");
      } else {
        statusText = that("lang.rms.fed.textEnable");
      }
      return statusText;
    },
    statusButtonFilter: function (value, that) {
      let buttonStatus = "";
      if (value === 1) {
        buttonStatus = that("lang.rms.fed.textProhibit");
      } else {
        buttonStatus = that("lang.rms.fed.textEnable");
      }
      return buttonStatus;
    },
  },
  data() {
    return {
      params: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      recordCount: 0,
      formConfig: {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          userNameAsFuzz: {
            label: "lang.rms.fed.inputRoleName",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          descr: {
            label: "lang.rms.fed.inputRoleRoleDescription",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          phoneAsFuzz: {
            label: "lang.rms.fed.listPermissionType",
            default: "",
            tag: "input",
            disabled: true,
            placeholder: "lang.rms.fed.pagePermission",
          },
          status: {
            label: "lang.rms.fed.listState",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "1",
                label: "lang.rms.fed.textEnable",
              },
              {
                value: "0",
                label: "lang.rms.fed.buttonProhibit",
              },
            ],
          },
        },
        rules: [],
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableConfig: {
        attrs: {
          // height: '100%'
        },
        isPagination: false,
        columns: [
          {
            label: "lang.rms.fed.listSerialNumber",
            "show-overflow-tooltip": true,
            // align: 'center',
            prop: "roleId",
          },
          {
            label: "lang.rms.fed.inputRoleName",
            "show-overflow-tooltip": true,
            // align: 'center',
            prop: "name",
          },
          {
            label: "lang.rms.fed.inputRoleRoleDescription",
            "show-overflow-tooltip": true,
            // align: 'center',
            prop: "descr",
          },
          {
            label: "lang.rms.fed.inputPermissionType",
            "show-overflow-tooltip": true,
            // align: 'center',
            slot: "type",
            key: "type",
          },
          {
            label: "lang.rms.fed.system",
            "show-overflow-tooltip": true,
            // align: 'center',
            slot: "system",
            key: "system",
          },
          {
            label: "lang.rms.fed.inputState",
            "show-overflow-tooltip": true,
            // align: 'center',
            slot: "status",
            key: "status",
          },
          {
            label: "lang.rms.fed.listCreationTime",
            "show-overflow-tooltip": true,
            // align: 'center',
            slot: "createTime",
            key: "createTime",
          },
          {
            label: "lang.rms.fed.listEditingTime",
            "show-overflow-tooltip": true,
            // align: 'center',
            slot: "updateTime",
            key: "updateTime",
          },
          {
            label: "lang.rms.fed.listEditor",
            "show-overflow-tooltip": true,
            // align: 'center',
            prop: "editer",
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "140px",
            // align: 'center',
            slot: "operations",
          },
        ],
        data: [],
        pageSize: 10,
        total: 0,
      },
    };
  },
  created() {
    this.params = {
      pageSize: this.page.pageSize,
      currentPage: this.page.currentPage,
    };
    this.getRoleList(this.params);
  },
  methods: {
    ...mapMutations("roleList", ["setPageShow", "setHandleType", "setUserInfo"]),
    // 格式化时间
    setTime(value) {
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss");
    },
    i18(title) {
      const hasKey = this.$t(title);
      const translatedTitle = this.$t(title);
      if (hasKey) {
        return translatedTitle;
      }
      return title;
    },
    // 列表接口请求
    getRoleList(params) {
      $req
        .get("/athena/api/coreresource/auth/role/pageQuery/v1", {
          ...params,
          params: true,
        })
        .then(res => {
          const data = res.data;
          this.page.currentPage = data.currentPage || 1;
          this.tableConfig.data = data.recordList;
          this.page.total = data.recordCount;
          this.tableConfig.total = data.recordCount;
        });
    },
    onCreate() {
      this.setUserInfo({});
      this.setPageShow(true);
      this.setHandleType("create");
    },
    // 编辑
    handleEditRole(userInfo) {
      this.setUserInfo(userInfo);
      // 切换显示页面
      this.setPageShow(true);
      // 设置操作状态
      this.setHandleType("edit");
    },
    // 删除
    handleDeleteRole(userInfo) {
      this.$geekConfirm(this.$t("lang.venus.web.common.isDeleted")).then(() => {
        $req
          .post("/athena/api/coreresource/auth/role/delRole/v1", {
            roleId: userInfo.roleId,
          })
          .then(res => {
            if (res.code === 0) {
              this.getRoleList(this.params);
            }
          });
      });
    },
    handleEnableRole(userInfo) {
      // 0表示禁用，1表示启用
      if (userInfo.status === 1) {
        $req
          .post("/athena/api/coreresource/auth/role/disableRole/v1", {
            roleId: userInfo.roleId,
            name: userInfo.name,
          })
          .then(res => {
            if (res.code === 0) {
              this.getRoleList(this.params);
            }
          });
      } else {
        $req
          .post("/athena/api/coreresource/auth/role/enableRole/v1", {
            roleId: userInfo.roleId,
            name: userInfo.name,
          })
          .then(res => {
            if (res.code === 0) {
              this.getRoleList(this.params);
            }
          });
      }
    },
    // 分页
    currentPageChange(page) {
      this.page.currentPage = page;
      this.params = Object.assign(this.params, this.page);
      this.getRoleList(this.params);
    },
    // 改变每页显示条数
    pageSizeChange(size) {
      this.page.pageSize = size;
      this.params = Object.assign(this.params, this.page);
      this.getRoleList(this.params);
    },
    onQuery(val) {
      this.page.currentPage = 1;
      this.params = Object.assign(val, this.page);
      this.params.createTime = this.params.createTime && this.params.createTime.join("-");
      this.getRoleList(this.params);
    },
    onReset(val) {
      this.page.currentPage = 1;
      this.params = Object.assign({}, val, this.page);
      this.getRoleList(this.params);
    },
  },
};
</script>

<style lang="less">
.old-role-list {
  .table-content {
    padding-top: 10px;
  }
  .handle-btn {
    padding-bottom: 20px;
    text-align: right;

    .el-button {
      width: 120px;
    }
  }
}
</style>
