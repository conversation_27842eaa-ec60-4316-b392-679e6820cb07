import DICT from "./dict";
import { useAppStore } from "@packages/store/app";
import { useAttrStore } from "@packages/store/attr";
import { useI18n } from "@packages/hook/useI18n";
import {TASK_TYPE_DICT} from "@packages/configure/dict/taskType";
export const funcOptions = (formData: { [k: string]: any }): any => {
  const { t } = useI18n();
  const appStore = useAppStore();
  const attrStore = useAttrStore();

  /**
   * 由于存在选点需求, 所以这里只能先去请求数据了
   */
   attrStore.getFindAdjacentByCellCodeDictByOnly({
    cellCode: <string>attrStore.curNodeCellCodeByIndex,
    floorId: <number>appStore.floorId,
    mapId: <number>appStore.mapId,
  });

  attrStore.getAllRobotTypesByOnly();

  const associatedCellCode =
    attrStore.findAdjacentByCellCodeDictMap[<string>attrStore.curNodeCellCodeByIndex] || [];

  return {
    // 机器人调整方式
    ADJUST_CELL: {
      funcA: {
        label: "lang.rms.fed.function.adjustMethod",
        component: "elSelect",
        data: DICT.ROBOT_ADJUSTMENT_CONTSHELF,
        get(formData: any) {
          return Number(formData.funcA);
        },
      },
    },
    BACKUP_CELL: {
      // 强制后退
      funcA: {
        label: "lang.rms.fed.function.forceBack",
        component: "elSelect",
        data: DICT.WHETHER_DICT,
      },
      funcC: {
        component: "PointSelectV2",
        loading: !associatedCellCode,
        options: associatedCellCode || [],
        isMultiplePointDisable: true,
        multiple: true,
        get(formData: any) {
          return formData.funcC ? JSON.parse(formData.funcC) : [];
        },
        set(value: string[], formData: any) {
          formData.funcC = value ? JSON.stringify(value) : "";
        },
        // event: {
        //   visibleChange(visible: boolean) {
        //     if (visible) {
        //       attrStore.getFindAdjacentByCellCodeDictByOnly({
        //         cellCode: <string>attrStore.curNodeCellCodeByIndex,
        //         floorId: <number>appStore.floorId,
        //         mapId: <number>appStore.mapId,
        //       });
        //     }
        //   },
        // },
      },
    },
    // 避让功能
    AVOID_CELL: {
      funcC: {
        component: "PointSelectV2",
        options: associatedCellCode || [],
        loading: !associatedCellCode,
        isMultiplePointDisable: true,
        filterable: true,
        multiple: true,
        get(formData: any) {
          return formData.funcC ? JSON.parse(formData.funcC) : [];
        },
        set(value: string[], formData: any) {
          formData.funcC = value ? JSON.stringify(value) : "";
        },
        // event: {
        //   visibleChange(visible: boolean) {
        //     if (visible) {
        //       attrStore.getFindAdjacentByCellCodeDictByOnly({
        //         cellCode: <string>attrStore.curNodeCellCodeByIndex,
        //         floorId: <number>appStore.floorId,
        //         mapId: <number>appStore.mapId,
        //       });
        //     }
        //   },
        // },
      },
      funcD: {
        component: "PointSelectV2",
        options: attrStore.getCellData() || [],
        // loading: !associatedCellCode,
        allowCreate: true,
        filterable: true,
        multiple: true,
        isMultiplePointDisable: true,
        get(formData: any) {
          return formData.funcD ? JSON.parse(formData.funcD) : [];
        },
        set(value: string[], formData: any) {
          formData.funcD = value ? JSON.stringify(value) : "";
        },
        appendAttrsFn(value: string, data: any) {
          return {
            disabled: !data.funcC,
          };
        },
      },
    },
    /**
     * 等待功能
     */
    WAIT_CELL: {
      // 支持的指令类型
      funcD: {
        component: "elSelect",
        data: attrStore.robotInstructionDict || [],
        loading: !attrStore.robotInstructionDict,
        get(formData: any) {
          return JSON.parse(formData.funcD || "[]")[0];
        },
        set(value: string[], formData: any) {
          formData.funcD = JSON.stringify([value]);
        },
        event: {
          visibleChange(visible: boolean) {
            if (visible) {
              attrStore.getRobotInstructionByOlny();
            }
          },
        },
      },
      // 支持的任务类型
      funcE: { component: "elSelect", data: DICT.TASK_TYPE_DICT },
      // 等待功能 - 触发条件
      funcF: {
        component: "elSelect",
        data: DICT.UNLOAD_STATUS_DICT,
        multiple: true,
        get(formData: any) {
          return formData.funcF ? JSON.parse(formData.funcF) : [];
        },
        set(value: string[], formData: any) {
          formData.funcF = value ? JSON.stringify(value) : "";
        },
      },
      // 等待功能 - 回调方式
      funcB: { component: "elSelect", data: DICT.FORCE_BACKWARD_DICT },
      funcC: {
        component: "elSelect",
        data: associatedCellCode || [],
        loading: !associatedCellCode,
        filterable: true,
        multiple: true,
        get(formData: any) {
          return formData.funcC ? JSON.parse(formData.funcC) : [];
        },
        set(value: string[], formData: any) {
          formData.funcC = value ? JSON.stringify(value) : "";
        },
        event: {
          visibleChange(visible: boolean) {
            if (visible) {
              attrStore.getFindAdjacentByCellCodeDictByOnly({
                cellCode: <string>attrStore.curNodeCellCodeByIndex,
                floorId: <number>appStore.floorId,
                mapId: <number>appStore.mapId,
              });
            }
          },
        },
      },
    },
    /**
     * 鸣笛条件
     */
    BEEP_CELL: {
      funcA: { component: "elSelect", data: DICT.WHISTLE_DICT },
    },
    LIMIT_SHELF_ANGLE_CELL: {
      funcC: {
        describe: "lang.rms.api.result.edit.map.trafficCapacity",
        appendSlotName: "funcRackAngle",
        set(value: string, allData: { [k: string]: any }) {
          allData.funcD = value;
        },
      },
    },
    // 不可停留功能
    NOT_STOP_CELL: {
      // 阻塞点
      funcC: {
        component: "PointSelectV2",
        describe: "lang.rms.fed.function.stayBlockingCell.desc",
        isMultiplePointDisable: true,
        options: associatedCellCode || [],
        loading: !associatedCellCode,
        multiple: true,
        get(formData: any) {
          return formData.funcC ? JSON.parse(formData.funcC) : [];
        },
        set(value: string[], formData: any) {
          formData.funcC = value ? JSON.stringify(value) : "";
        },
        // event: {
        //   visibleChange(visible: boolean) {
        //     if (visible) {
        //       attrStore.getFindAdjacentByCellCodeDictByOnly({
        //         cellCode: <string>attrStore.curNodeCellCodeByIndex,
        //         floorId: <number>appStore.floorId,
        //         mapId: <number>appStore.mapId,
        //       });
        //     }
        //   },
        // },
      },
    },
    // 排队点
    QUEUE_CELL: {
      // 等待点
      funcC: {
        component: "PointSelectV2",
        filterable: true,
        multiple: true,
        isMultiplePointDisable: true,
        get(formData: any) {
          return formData.funcC ? JSON.parse(formData.funcC) : [];
        },
        set(value: string[], formData: any) {
          formData.funcC = value ? JSON.stringify(value) : "";
        },
        options: attrStore.storedCellCodes.map(item => ({
          label: item.cellCode,
          value: item.cellCode,
          nodeId: item.id,
        })),
      },
    },

    // 限制终点朝向
    LIMIT_STOP_ANGLE_CELL: {
      funcA: {
        component: "elSelect",
        data: DICT.FUNC_DIRECTION_DICT,
        get(formData: any) {
          return Number(formData.funcA);
        },
      }
    },
    // 消防功能
    FIRE_PASS: {
      describe: "lang.rms.api.result.edit.map.fireFightingTip",
    },
    // 不可抵达功能
    BAD_CELL: {
      describe: "lang.rms.api.result.edit.map.unreachablePoint",
    },

    // 跟随功能
    FOLLOW_CELL: {
      describe: "lang.rms.api.result.edit.map.followFunctionTip",
    },
    // 限制转面功能
    LIMITTED_TURN_CELL: {
      describe: "lang.rms.api.result.edit.map.restrictedTurnTip",
    },
    //转面点控制
    TURN_CONTROL_CELL: {
      describe: "该功能可以控制机器人进行转面",
    },
    // 禁止转弯功能
    NOT_ALLOW_TURN_CELL: {
      describe: "lang.rms.api.result.edit.map.noturningTip",
    },
    // 回收功能
    RECYCLEID: {
      describe: "lang.rms.api.result.edit.map.recyclingFunctionTip",
      // 支持的机器人类型
      funcC: {
        component: "elSelect",
        data: attrStore.robotTypeDict || [],
        loading: !attrStore.robotTypeDict,
        filterable: true,
        multiple: true,
        get(formData: any) {
          return formData.funcC ? JSON.parse(formData.funcC) : [];
        },
        set(value: string[], formData: any) {
          formData.funcC = value ? JSON.stringify(value) : "";
        }
      },
    },

    // 重启功能
    RESTART_CELL: {
      funcA: { component: "elSelect", data: DICT.ANGLE_DICT },
    },
    // 途经功能
    VIA_CELL: {
      funcD: {
        component: "PointSelectV2",
        options: associatedCellCode || [],
        get(formData: any) {
          return formData.funcD ? JSON.parse(formData.funcD) : [];
        },
        set(value: string[], formData: any) {
          if (!value.length) {
            formData.funcC = ''
          }
          formData.funcD = value ? JSON.stringify(value) : "";
        },
        isMultiplePointDisable: true,
        allowCreate: true,
        filterable: true,
        multiple: true,
        // allEnableSelect: true
      },
      funcC: {
        component: "elSelect",
        // data: DICT.VIA_CELL_DIRECTION_DICT,
        appendAttrsFn(value: string, formData: { [k: string]: any }) {
          if (formData.funcD && formData.funcD !== '[]') {
            return { data: DICT.VIA_CELL_DIRECTION_DICT }
          } else {
            const bothListItem = DICT.VIA_CELL_DIRECTION_DICT.find((item: any) => {
              return item.value === DICT.VIA_DIRECTION_DICT_BOTH
            });

            return { data: [bothListItem] }
          }
        }
      },
      funcB: {
        component: "elSelect",
        data: DICT.VIA_CELL_BOTH_DICT,
      }
    },

    // 停靠功能
    REST_CELL: {
      // 停靠车型
      funcC: {
        component: "elSelect",
        data: attrStore.robotTypeDict || [],
        loading: !attrStore.robotTypeDict,
        filterable: true,
        multiple: true,
        get(formData: any) {
          return formData.funcC ? JSON.parse(formData.funcC) : [];
        },
        set(value: string[], formData: any) {
          formData.funcC = value ? JSON.stringify(value) : "";
        },
      },
    },

    TURN_CELL: {
      funcA: { component: "elSelect", data: DICT.FACE_TURNING_CONTROL_DICT },
    },
    LIGHT_CELL: {
      funcA: {
        component: "elSelect",
        data: DICT.LIGHT_MODEL_DICT,
        describe: "lang.rms.api.result.edit.map.robotLightupModeTip",
      },
      funcB: {
        component: "elSelect",
        data: DICT.LIGHT_COLOR_DICT,
        describe: "lang.rms.api.result.edit.map.robotLightupColorTip",
      },
    },
    // 趴窝功能
    WAIT_FOR_TASK_CELL: {
      describe: [
        t("lang.rms.api.result.warehouse.lieDown.series"),
        t("lang.rms.api.result.warehouse.lieDown.range"),
        t("lang.rms.api.result.warehouse.lieDown.multi"),
        t("lang.rms.api.result.warehouse.lieDown.point"),
      ].join("\r\n"),
      // 趴窝类型
      funcA: { component: "elSelect", data: DICT.BROKEN_DOWN_DICT },
      // 等待时间
      funcB: {
        suffixName: "s",
      },
      // 单元格节点
      funcD: {
        component: "PointSelectV2",
        loading: !associatedCellCode,
        options: associatedCellCode || [],
        isMultiplePointDisable: true,
        filterable: true,
        // event: {
        //   visibleChange(visible: boolean) {
        //     if (visible) {
        //       attrStore.getFindAdjacentByCellCodeDictByOnly({
        //         cellCode: <string>attrStore.curNodeCellCodeByIndex,
        //         floorId: <number>appStore.floorId,
        //         mapId: <number>appStore.mapId,
        //       });
        //     }
        //   },
        // },
      },
      // 作用范围
      funcE: {
        suffixName: "m",
        appendAttrsFn(value: string, data: { [k: string]: any }) {
          return {
            condition: data.funcA === DICT.BROKEN_DOWN_DICT_SERIES,
          };
        },
      },
    },

    // 货架归还转面
    SHELF_RETURN_SIDE_CELL: {
      // getShelfModelListByOnly
      funcB: { component: "elSelect", data: DICT.WHETHER_DICT },
      funcA: {
        component: "elSelect",
        data: attrStore.shelfModelDict || [],
        loading: !attrStore.shelfModelDict,
        event: {
          visibleChange(visible: boolean) {
            if (visible) {
              attrStore.getShelfModelListByOnly();
            }
          },
        },
      },
      funcC: { component: "elSelect", data: DICT.STATION_DIRECTIONBYWARE_DICT },
      funcD: {
        component: "elSelect",
        multiple: true,
        data: DICT.SHELF_SURFACE_DICT,
        get(formData: any) {
          return JSON.parse(formData.funcD || "[]");
        },
        set(value: string[], formData: any) {
          formData.funcD = JSON.stringify(value);
        },
      },
    },

    // 分拣区入口
    SORTING_ENTRY_CELL: {
      describe: '分拣场景三段式调度的分拣入口点',
      funcA: {
        describe: '入口点的排队数量',
      },
      funcB: {
        describe: '去入口点的优先级',
      },
      // 进入方向
      funcC: {
        component: "elSelect",
        data: DICT.STATION_DIRECTIONBYWARE_DICT,
      },
      // funcC: {
      //   component: "elSelect",
      //   data: attrStore.getAreaInfoByType('SORTING_AREA') || [],
      // },
      areaId: {
        component: "elSelect",
        data: attrStore.getAreaInfoByType || [],
      }
    },

    DOCKING_CELL: {
      funcA: {
        label: "lang.rms.fed.function.dockingAngle",
        index: 1,
      },
      funcB: {
        label: "lang.rms.fed.function.dockingType",
        index: 2,
      },
      funcC: {
        label: "lang.rms.fed.function.dockingX",
        index: 3,
      },
      funcD: {
        label: "lang.rms.fed.function.dockingY",
        index: 4,
      },
    },
  };
};
