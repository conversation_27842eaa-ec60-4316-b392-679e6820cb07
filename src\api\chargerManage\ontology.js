/*
 * @Author: dingt<PERSON> (<EMAIL>)
 * @Date: 2022-01-15 17:38:44
 * @Description: 充电站相关接口
 */
import request from '../request';

// 充电站id-搜索功能
export function getChargerId(params = {}) {
  return request({ url: '/athena/charger/chargerId', method: 'get', params })
}

// 充电站类型-搜索功能
export function getChargerType(params = {}) {
  return request({ url: '/athena/charger/chargerType', method: 'get', params })
}

// 外部系统编码-搜索功能
export function getHostCode(params = {}) {
  return request({ url: '/athena/charger/hostCode', method: 'get', params })
}

// 充电站协议-下拉框
export function getProtocol(params = {}) {
  return request({ url: '/athena/charger/protocol', method: 'get', params })
}

// 交互模式-下拉框
export function getInteractiveModel(params = {}) {
  return request({ url: '/athena/charger/interactiveModel', method: 'get', params })
}

// 获取地图信息
export function getMapInfo(data = {}, params = {}) {
  return request({ url: '/athena/charger/map', method: 'get', data, params })
}

// 充电站分页列表
export function getChargerPageList(params = {}) {
  return request({ url: '/athena/charger/pageList', method: 'get', params })
}

// 启用
export function enableCharger(data = {}, params = {}) {
  return request({ url: '/athena/charger/enable', method: 'post', data, params })
}

// 停用
export function disableCharger(data = {}, params = {}) {
  return request({ url: '/athena/charger/disable', method: 'post', data, params })
}

// 重启
export function restartCharger(data = {}, params = {}) {
  return request({ url: '/athena/charger/restart', method: 'post', data, params })
}
