<template>
  <geek-main-structure>
    <el-tabs v-model="activeName" class="tab-class">
      <!-- 容器类别管理 -->
      <el-tab-pane
       v-if="tabNamePerssion['categoryManage']"
        :label="$t('lang.rms.web.container.containerCategoryManagement')"
        name="categoryManage"
      >
        <categoryManage />
      </el-tab-pane>
      <!-- 货架模型 -->
      <el-tab-pane
         v-if="tabNamePerssion['containerModel']"
        :label="$t('lang.rms.web.container.containerModelManagement')"
        name="containerModel"
        style="position: relative"
      >
        <containerModel />
      </el-tab-pane>
      <!-- 异形货架 -->
      <el-tab-pane
        v-if="tabNamePerssion['SpecialShapedShelfModel']"
        :label="$t('lang.rms.fed.shapedShelfModel')"
        name="SpecialShapedShelfModel"
        style="position: relative"
      >
        <SpecialShapedShelfModel />
      </el-tab-pane>
    </el-tabs>
  </geek-main-structure>
</template>

<script>
import categoryManage from "./categoryManage";
import containerModel from "./containerModel";
import SpecialShapedShelfModel from "./SpecialShapedShelfModel";
import { mapMutations } from "vuex";

export default {
  components: {
    categoryManage,
    containerModel,
    SpecialShapedShelfModel,
  },
  data() {
    return {
      tabNamePerssion: {
        categoryManage: this.getTabPermission("TabShelfCategoryPage", "containerModelManage"),
        containerModel: this.getTabPermission("TabModelPage", "containerModelManage"),
        SpecialShapedShelfModel:this.getTabPermission("TabShapedModelPage", "containerModelManage") 
      },
      defaultActive: "categoryManage",

    };
  },
  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
  },
  mounted() {
    this.defaultActive = $utils.Tools.getRouteQueryTabName(
      this.defaultActive,
      this.tabNamePerssion,
    );
    this.resetModelView();
  },
  watch: {
    activeName() {
      this.resetModelView();
    },
  },
  methods: {
    ...mapMutations("containerModal", ["resetModelView"]),
  },

};
</script>
<style lang="less" scoped>
.tab-class {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-tab-pane) {
  position: relative;
  height: 100%;
  overflow:scroll;
}

:deep(.el-tabs__content) {
  flex: 1;
}
.tab-class {
  height: 100%;
}
</style>
