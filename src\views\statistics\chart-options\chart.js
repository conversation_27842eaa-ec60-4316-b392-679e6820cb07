export default class {
  constructor(options) {
    this.width = options.width || 16;
    this.height = options.height || 10;
    this.x = options.x || 0;
    this.y = options.y || 0;

    this.chart = {
      type: options.chart.type,
      request: options.chart.request,
      dataHandler: options.chart.dataHandler || {
        handler: "",
        params: {},
      }
    };

    this.defEchartsOptions = options.defEchartsOptions || {
      title: {},
      xAxis: {
        type: 'category',
        data: []
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: [],
          type: options.chart.type
        }
      ]
    };
  }
}