<template>
  <el-container class="outer-wrap">
    <div class="inner-wrap">
      <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane :label="$t('lang.mb.home.stationManage')" name="first1">
          <StationInstance />
        </el-tab-pane>
        <el-tab-pane :label="$t('lang.rms.api.result.warehouse.workstationModel')" name="first2">
          <StationModal />
        </el-tab-pane>
      </el-tabs> -->
      <StationInstance />
    </div>
  </el-container>
</template>

<script>
import StationInstance from "./subPages/StationInstance";
// import StationModal from "./subPages/StationModal";

export default {
  components: {
    StationInstance,
    // StationModal,
  },
  data() {
    // 这里存放数据
    return {
      activeName: "first1",
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    handleClick(tab) {
      console.log(tab);
    },
  },
};
</script>
<style lang="scss" scoped>
.outer-wrap {
  background: white !important;
  height: auto;
}
.inner-wrap {
  width: 100%;
  box-sizing: border-box;
  margin-right: 25px;
  margin-left: 25px;
}
</style>
