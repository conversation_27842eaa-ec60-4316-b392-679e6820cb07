<template>
  <div class="shelf">
    <!-- 货架 -->
    <div class="shelfBox" ref="shelfBoxRef">
      <span class="shelfContent">{{ $t("lang.rms.fed.containerCenter") }}</span>
      <div v-if="auxiliary" class="auxiliary-w">
        <span class="auxiliaryContent_l">-{{ shelfData.width / 2 }}mm</span>
        <span class="auxiliaryContent_r">+{{ shelfData.width / 2 }}mm</span>
      </div>
      <div v-if="auxiliary" class="auxiliary-h">
        <span class="auxiliaryContent_t">+{{ shelfData.length / 2 }}mm</span>
        <span class="auxiliaryContent_b">-{{ shelfData.length / 2 }}mm</span>
      </div>

      <div 
        class="leftTopFoot foot" 
        v-for="(item, index) in legList" 
        :key="index" 
        :style="{
          top: item.top,
          left: item.left
        }"
      >
        <div v-if="auxiliary" class="auxiliary-foot-w">
          <span class="auxiliaryContent">{{ item.width }}mm</span>
        </div>
        <div v-if="auxiliary" class="auxiliary-foot-h">
          <span class="auxiliaryContent">{{ item.length }}mm</span>
        </div>

        <div class="auxiliary-foot-rcY" :style="item.guideY">
          <span class="auxiliaryContent">{{ item.rcY }}mm</span>
        </div>
        <div class="auxiliary-foot-rcX" :style="item.guideX">
          <span class="auxiliaryContent">{{ item.rcX }}mm</span>
        </div>
      </div>

      <div class="F">
        {{ $t("lang.rms.fed.faceF") }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // 辅助线
    auxiliary: {
      type: Boolean,
      default() {
        return false;
      },
    },
    shelfData: {
      type: Object,
      default() {
        return {};
      }, 
    },
  },
  data() {
    return {
      shelfBoxRef: null
    }
  },
  mounted() {
    this.shelfBoxRef = this.$refs.shelfBoxRef
  },
  computed: {
    legList() {
      const { shelfData, shelfBoxRef } = this;
      if (!shelfBoxRef) return [];

      const { offsetHeight, offsetWidth } = shelfBoxRef;
      const { width, length } = shelfData;
      return (shelfData.extendJson.legs || []).map(item => {
        const top = 50 + (item.rcY) / width * 100;
        const left = 50+ (item.rcX) / length * 100;
        const guideX = {};
        const guideY = {};

        if (shelfBoxRef) {
          guideX.top = "50%";
          guideY.left = "50%";

          if (left <= 50) {
            guideX.left = '50%';
            guideX.width = `${(50 - left) * (offsetWidth / 100)}px`;
          } else {
            guideX.right = '50%';
            guideX.width = `${(left - 50) * (offsetWidth / 100)}px`;
          }

          if (top <= 50) {
            guideY.bottom = '50%';
            guideY.height = `${(50 - top) * (offsetHeight / 100)}px`;
            guideY.lineHeight = guideY.height;
          } else {
            guideY.top = '50%';
            guideY.height = `${(top - 50) * (offsetHeight / 100)}px`;
            guideY.lineHeight = guideY.height;
          }
        }

        return {
          ...item,
          top: `${100 - top}%`,
          left: `${left}%`,
          guideX,
          guideY
        }
      });
    }
  },
};
</script>

<style scoped lang="less">
.F {
  position: absolute;
  top: calc(100% + 3px);
  text-align: center;
  font-weight: 600;
  width: 100%;
  font-size: 18px;
}
.shelf {
  width: 100%;
  height: 100%;
  position: relative;

  .shelfContent {
    top: 50%;
    left: 50%;
    position: absolute;
    transform: translate(-50%, -50%);
    padding-top: 25px;

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #616060;
      width: 10px;
      height: 10px;
      border-radius: 50%;
    }
  }

  .shelfBox {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 3px solid #9e9e9e;
    border-radius: 2px;

    .auxiliary-w {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 100%;
      border-top: 3px dotted #ccc;
      text-align: center;
      line-height: 20px;
      
      .auxiliaryContent_l, .auxiliaryContent_r {
        position: absolute;
        top: 50%;
        width: 100px;
      }

      .auxiliaryContent_l {
        left: 0;  
      }

      .auxiliaryContent_r {
        right: 0;
      }
    }

    .auxiliary-h {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      height: 100%;
      border-right: 3px dotted #ccc;
      .auxiliaryContent_t, .auxiliaryContent_b {
        transform: translateX(-50%);
        position: absolute;
        text-align: center;
        left: 50%;
        width: 100px;
      }

      .auxiliaryContent_t {
        top: 0;  
      }

      .auxiliaryContent_b {
        bottom: 0;
      }
    }

    .auxiliary-foot-rcX, .auxiliary-foot-rcY {
      position: absolute;
      .auxiliaryContent {
        width: 100%;
        display: block;
        text-align: center;
      }
    }

    .auxiliary-foot-rcY {
      border-left: 2px dashed #ccc;
    }
    
    .auxiliary-foot-rcX {
      border-top: 2px dashed #ccc;
    }
  }

  .foot {
    border: 2px solid #9e9e9e;
    width: 60px;
    height: 60px;
    position: absolute;
    border-radius: 5px;
    transform: translate(-30px, -30px);

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #616060;
      width: 10px;
      height: 10px;
      border-radius: 50%;
    }

    &.leftTopFoot {
      top: 10px;
      left: 10px;
    }

    .auxiliary-foot-w {
      position: absolute;
      height: 100%;
      left: 100%;
      width: 10px;
      border-right: 3px dotted #9e9e9e;

      .auxiliaryContent {
        position: absolute;
        top: 50%;
        transform: translateX(-50%);
        left: 65px;
        line-height: 0;
        width: 100px;
      }
    }

    .auxiliary-foot-h {
      position: absolute;
      width: 100%;
      top: 100%;
      height: 10px;
      border-bottom: 3px dotted #9e9e9e;

      .auxiliaryContent {
        text-align: center;
        line-height: 35px;
      }
    }
  }
}
</style>
