<template>
  <div class="content">
    <el-tree ref="tree" :data="data" show-checkbox node-key="permissionId" :props="defaultProps" />
  </div>
</template>
<script>
// import * as api from "@/api/authManage";
import { mapState, mapMutations } from "vuex";
import UserManager from "./store";
// import { transMsg } from "@/utils/dict";
export default {
  data() {
    return {
      data: [],
      userId: [],
      permissionId: [],
      status: 1,
      submitData: {},
      defaultProps: {
        children: "children",
        label: function (a) {
          let text;
          if (a.code.indexOf("auth") !== -1) {
            text = $utils.Tools.transMsgLang(a.code);
          } else {
            text = a.code;
          }
          return text;
        },
      },
    };
  },
  computed: {
    ...mapState("roleList", ["handleType", "userInfo"]),
  },
  mounted() {
    this.injectPagePermisson();
  },
  activated() {
    // EventBus.$on("setRolesStatus", data => {
    //   // 设置是否启用角色权限
    //   this.status = data;
    // });
    // this.injectPagePermisson()
  },
  beforeDestroy() {
    // EventBus.$off("setRolesStatus")
  },
  methods: {
    ...mapMutations("roleList", ["setPageShow"]),
    injectPagePermisson() {
      // 编辑状态
      if (this.handleType === "edit") {
        $req
          .get("/athena/api/coreresource/auth/permission/listWebPerm/v1", {
            roleId: this.userInfo.roleId,
            subsysId: 1,
          })
          .then(res => {
            if (res.code === 0) {
              this.data = res.data.sybSystemPermList[1];
              let checkedList = res.data.sybSystemPermList.checkedPermissionIds;
              this.$refs.tree.setCheckedKeys(checkedList);
              for (let key in res.data.userId) {
                UserManager.saveUser(res.data.userId[key], "edit");
              }
            }
          });
      } else {
        // 新建状态
        $req
          .get("/athena/api/coreresource/auth/permission/listPermissionsBySubsysId", {
            subsysId: 1,
          })
          .then(res => {
            if (res.code === 0) {
              this.data = res.data.filter(item => item.code === "pc");
              let checkedList = [];
              this.$refs.tree.setCheckedKeys(checkedList);
            }
          });
      }
    },
    savePagePermisson(inputInfo) {
      this.roleId = this.userInfo.roleId;
      // 获取当前的选择permissionIds
      this.permissionId = this.$refs.tree
        .getCheckedKeys()
        .concat(this.$refs.tree.getHalfCheckedKeys());
      let data = {
        status: inputInfo.permissionStatus,
        name: inputInfo.name,
        descr: inputInfo.descr,
        type: 1,
        subsystem: [1],
        permList: [
          {
            subsysId: 1,
            permissionId: this.permissionId,
          },
        ],
      };
      if (this.handleType === "create") {
        let _data = {
          ...data,
          // ...{ userId: UserManager.getUserList("create") },
        };
        let allowPass = this.checkingInput(_data);
        if (allowPass) {
          $req.post("/athena/api/coreresource/auth/permission/setWebPerm/v1", _data).then(res => {
            if (res.code === 0) {
              // this.$emit("fatherMethod");
              this.setPageShow(false);
              this.$success($utils.Tools.transMsgLang(res.msg));
            }
          });
        }
      } else {
        let _data = {
          ...data,
          ...{
            roleId: Number(this.roleId),
            // userId: UserManager.getUserList("edit"),
          },
        };
        let allowPass = this.checkingInput(_data);
        if (allowPass) {
          let tempData = this.submitData;
          $req
            .post("/athena/api/coreresource/auth/permission/editWebPerm/v1", tempData)
            .then(res => {
              if (res.code === 0) {
                // this.$emit("fatherMethod");
                this.setPageShow(false);
                this.$success($utils.Tools.transMsgLang(res.msg));
              }
            });
        }
      }
    },
    checkingInput(data) {
      // if (data.userId.length === 0) {
      //   this.showWaring("lang.rms.fed.pleaseAssociateUsers");
      //   return false;
      // } else
      if (data.name === "") {
        this.showWaring("lang.rms.fed.pleaseEnterRoleName");
        return false;
      } else if (data.descr === "") {
        this.showWaring("lang.rms.fed.pleaseEnterRoleDesc");
        return false;
      } else if (data.permList[0].permissionId.length === 0) {
        this.showWaring("lang.rms.fed.pleaseAssignPagePermissions");
        return false;
      } else {
        this.submitData = data;
        return true;
      }
    },
    showWaring(text) {
      this.$message({
        message: this.$t(text),
        type: "warning",
      });
    },
    i18(title) {
      const hasKey = this.$t(title);
      const translatedTitle = this.$t(title);
      if (hasKey) {
        return translatedTitle;
      }
      return title;
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  overflow: auto;
  max-height: 300px;
  padding: 0px 20px 20px 0px;
}
</style>
