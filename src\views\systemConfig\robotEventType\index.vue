<template>
  <geek-main-structure class="exception-hand-box">
    <div class="form-content">
      <el-button type="primary" @click="addRobotEventType">
        {{ $t("lang.rms.fed.newlyAdded") }}
      </el-button>
    </div>
    <div class="table-content">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="eventGroupNameI18n" :label="$t('lang.rms.fed.eventGroupName')">
          <!-- <template slot-scope="scope">
            <span>{{ $t(scope.row.eventGroupNameI18n) }}</span>
          </template> -->
        </el-table-column>
        <el-table-column
          prop="eventGroup"
          :label="$t('lang.rms.fed.eventGroupValue')"
          align="center"
        />
        <el-table-column prop="eventTypeNameI18n" :label="$t('lang.rms.fed.eventTypeName')">
          <!-- <template slot-scope="scope">
            <span>{{ $t(scope.row.eventTypeNameI18n) }}</span>
          </template> -->
        </el-table-column>
        <el-table-column
          prop="eventType"
          :label="$t('lang.rms.fed.eventTypeValue')"
          align="center"
        />
        <el-table-column prop="eventDesc" :label="$t('lang.rms.fed.eventDes')" />
        <el-table-column
          prop="enableSaveDatabase"
          :label="$t('lang.rms.fed.enable.saveDatabase')"
          align="center"
        >
          <template slot-scope="scope">
            <span>
              {{ scope.row.enableSaveDatabase ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="enablePublish"
          :label="$t('lang.rms.fed.enable.publish')"
          align="center"
        >
          <template slot-scope="scope">
            <span>
              {{ scope.row.enablePublish ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}
            </span>
          </template>
        </el-table-column>
        <el-table-column v-if="!isRoleGuest" fixed="right" :label="$t('lang.rms.fed.operation')">
          <template slot-scope="scope">
            <el-button type="text" @click="handleEdit(scope.row)">
              {{ $t("lang.rms.fed.edit") }}
            </el-button>
            <el-button type="text" @click="handleDel(scope.row)">
              {{ $t("lang.rms.fed.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <edit-dialog ref="editDialog" @updateTableList="getTableList" />
  </geek-main-structure>
</template>

<script>
import EditDialog from "./components/editDialog";

export default {
  components: { EditDialog },
  data() {
    return {
      tableData: [], // 设置table的数据
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  activated() {
    this.getTableList();
  },
  methods: {
    addRobotEventType() {
      console.log("添加机器人事件类型");
      this.$refs.editDialog.open();
    },
    handleEdit(row) {
      this.$refs.editDialog.open(row);
    },
    handleDel(row) {
      if (!row) return;
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete")).then(() => {
        $req.get("/athena/robot/robotEventType/delete", { id: row.id }).then(res => {
          if (res.code !== 0) return;
          this.getTableList();
          this.$success(this.$t(res.msg));
        });
      });
    },
    getTableList() {
      $req.get("/athena/robot/robotEventType/list").then(res => {
        let result = res.data;
        if (result && $utils.Type.isArray(result)) {
          result.forEach(element => {
            element.eventGroupNameI18n = this.$t(element.eventGroupNameI18n);
            element.eventTypeNameI18n = this.$t(element.eventTypeNameI18n);
          });
          this.tableData = result;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.form-content {
  border-bottom: 5px solid #eee;
  padding-bottom: 10px;
  text-align: right;
}

.table-content {
  padding-top: 15px;

  .btn-opt {
    padding: 3px 5px;
    min-height: 10px;
  }
}
</style>
