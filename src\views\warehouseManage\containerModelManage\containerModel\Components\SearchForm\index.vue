<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2022-01-05 20:04:30
 * @Description:
-->
<template>
  <el-form
    ref="searchForm"
    :inline="true"
    class="demo-form-inline"
    label-position="top"
    :model="searchForm"
  >
    <el-form-item :label="$t('lang.rms.web.container.containerModelId')" prop="id">
      <searchId
        :data-arr="idSearchArr"
        :placeholder-str="$t('lang.rms.web.container.pleaseEnterContainerId')"
        :reset-form-event="resetFormEvent"
        @searchItem="searchIdFun"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.web.container.containerModelAlias')" prop="modelName">
      <searchId
        :data-arr="nameSearchArr"
        :placeholder-str="$t('lang.rms.web.container.pleaseEnterContainerAliasId')"
        :reset-form-event="resetFormEvent"
        @searchItem="searchNameFun"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.web.container.isSupportMove')" prop="move">
      <el-select v-model="searchForm.move" :placeholder="$t('lang.rms.fed.choose')">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="$t(item.label)"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item class="align-bottom">
      <el-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.query") }}</el-button>
      <el-button type="primary" @click="resetForm">{{ $t("lang.rms.fed.reset") }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import { getIdArrSearch, getModelArrSearch } from "@/api/containerManage/index";

import searchId from "../searchInput/searchInput";

export default {
  // import引入的组件需要注入到对象中才能使用
  components: { searchId },
  data() {
    // 这里存放数据
    return {
      searchForm: {
        id: "",
        modelName: "",
        move: "",
      },
      options: [
        { value: "0", label: "lang.rms.web.container.canNotMove" },
        { value: "1", label: "lang.rms.web.container.canMove" },
      ],
      idSearchArr: [],
      nameSearchArr: [],
      resetFormEvent: false,
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  mounted() {
    this.onSubmit();
    this.getIdArr();
    this.getModelArr();
  },
  // 方法集合
  methods: {
    searchIdFun(data) {
      this.searchForm.id = data.value;
    },
    searchNameFun(data) {
      this.searchForm.modelName = data.value;
    },
    inputChange(val = "") {
      this.searchForm.id = val
        .split("")
        .filter(key => !isNaN(key))
        .join("");
    },
    onSubmit() {
      this.$emit("onsubmit", { ...this.searchForm });
    },
    async resetForm() {
      this.resetFormEvent = true;
      this.$refs["searchForm"].resetFields();
      await this.$emit("onsubmit", { ...this.searchForm });
      this.resetFormEvent = false;
    },
    async getIdArr() {
      const { data } = await getIdArrSearch();
      this.idSearchArr = data.map(item => ({ value: item.toString() }));
    },
    async getModelArr() {
      const { data } = await getModelArrSearch();
      this.nameSearchArr = data.map(item => ({ value: item.toString() }));
    },
  },
};
</script>
<style lang="scss" scoped>
.align-bottom {
  vertical-align: bottom;
}
</style>
