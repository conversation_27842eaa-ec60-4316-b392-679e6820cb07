<template>
  <div class="customize-table">
    <div v-if="tableConfig.queryActions" class="query-actions">
      <el-button type="primary" @click="onCreate">新增</el-button>
    </div>
    <div class="query-table">
      <!-- max-height="241" -->
      <el-table
        v-bind="tableConfig.attrs"
        :data="tableConfig.data"
        style="width: 100%"
        @selection-change="onSelect"
      >
        <template v-for="column in tableConfig.columns">
          <slot v-if="column.slot" :name="column.slot" :column="column" :label="$t(column.label)" />
          <el-table-column
            v-else-if="column.operations"
            :key="column.prop"
            v-bind="column"
            :label="$t(column.label)"
            fixed="right"
            align="center"
          >
            <template #default="scope">
              <el-button
                v-for="item in column.operations"
                :key="item.label"
                v-bind="item"
                :icon="item.icon ? item.icon : ''"
                :size="item.mini ? item.mini : 'mini'"
                :type="item.type ? item.text : 'text'"
                @click="operationHandler(item.handler, scope.row)"
              >
                {{ item.label }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column v-else v-bind="column" :key="column.prop" :label="$t(column.label)" />
        </template>
      </el-table>
    </div>
    <div v-if="tableConfig.isPagination" class="query-pagination">
      <el-pagination
        :key="pageNum"
        v-bind="$attrs"
        :layout="tableConfig.pageLayout ? tableConfig.pageLayout : pageLayout"
        :page-size="tableConfig.pageSize ? tableConfig.pageSize : pageSize"
        :page-sizes="tableConfig.pageSizes ? tableConfig.pageSizes : pageSizes"
        :total="tableConfig.total"
        background
        v-on="$listeners"
      />
    </div>
  </div>
</template>

<script>
// import list from '@/mixins/list'
export default {
  name: "CustomizeTable",
  components: {},
  props: {
    tableConfig: {
      required: true,
      type: Object,
      default: () => ({}),
      queryActions: {
        type: Boolean,
        default: false,
      },
      data: {
        required: true,
        type: Array,
        default: () => [],
      },
      columns: {
        required: true,
        type: Array,
        default: () => [],
      },
      isPagination: {
        type: Boolean,
        default: true,
      },
      total: {
        type: Number,
        default: 0,
      },
      pageSize: {
        type: Number,
        default: 10,
      },
    },
  },
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      pageSizes: [10, 25, 50, 100],
      pageLayout: "sizes, prev, pager, next",
    };
  },
  computed: {},
  watch: {},
  methods: {
    operationHandler(handler, row) {
      console.log(row);
      this.$emit(handler, row);
    },
    onCreate() {
      this.$emit("on-create");
    },
    onSelect(val) {
      this.$emit("on-select", val);
    },
  },
};
</script>
<style lang="less">
.customize-table {
  .el-table th > .cell,
  .el-table td > .cell {
    font-size: 12px;
  }
}
.customize-table th.gutter {
  display: table-cell !important;
}
.customize-table .query-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
