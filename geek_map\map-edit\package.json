{"name": "map", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build --modern", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve", "start": "vue-cli-service serve"}, "main": "./packages/src.ts", "dependencies": {"core-js": "^3.8.3", "element-plus": "^2.2.14", "pinia": "^2.0.20", "pixi-dashed-line": "^1.4.2", "pixi-viewport": "^4.34.4", "pixi.js": "^6.4.2", "vue": "^3.2.13", "vue-router": "^4.0.3"}, "devDependencies": {"@element-plus/icons-vue": "^2.0.9", "@pixi/graphics-smooth": "0.0.30", "@timohausmann/quadtree-js": "^1.2.5", "@timohausmann/quadtree-ts": "^2.0.0-beta.1", "@turf/turf": "^6.5.0", "@types/lodash": "^4.14.194", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/compiler-sfc": "^3.2.41", "@vue/eslint-config-typescript": "^9.1.0", "axios": "^0.27.2", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "lodash": "^4.17.21", "sass": "^1.32.7", "sass-loader": "^12.0.0", "typescript": "~4.5.5", "vue-i18n": "^9.2.2", "vue-tour": "^2.0.0"}}