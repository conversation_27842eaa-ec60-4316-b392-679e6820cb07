<template>
  <el-container class="border-box">
    <el-row type="flex" class="row-bg" justify="start" align="middle">
      <el-col :span="24" class="col-flex">
        <slot />
      </el-col>
    </el-row>
  </el-container>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';

export default {
  data() {
    // 这里存放数据
    return {

    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {

  },
}
</script>
<style lang='scss' scoped>
.border-box {
  box-sizing: border-box;
  // border: 1px solid #ebebeb;
}
.col-flex {
  display: flex;
  align-items:center;
}
</style>
