/* ! <AUTHOR> at 2021/01 */
import Cookies from "js-cookie";
import langType from "@lang/_lang-type-data";

export default {
  // 语言国际化相关
  getLocalLang() {
    let lang = localStorage.getItem("curRMSLanguage");
    if (lang) return lang;
    return localStorage.getItem("curLanguage") || "";
  },
  setLocalLang(localLang = "zh_cn") {
    localStorage.setItem("curLanguage", localLang);
    localStorage.setItem("curRMSLanguage", localLang);
  },
  setLocalLangData(localLang, data) {
    localStorage.setItem(`RMSLanguageData${localLang}`, data);
  },
  getLocalLangData(localLang) {
    let conf = localStorage.getItem(`RMSLanguageData${localLang}`);
    if (conf) return JSON.parse(conf);
    return {};
  },
  setI18nMessage($i18n, apiUrl, forceUpdate) {
    const localLang = this.getLocalLang();
    if (JSON.stringify($i18n.messages[localLang]) === "{}" || forceUpdate) {
      $i18n.locale = localLang;
      $i18n.mergeLocaleMessage(localLang, $utils.Data.getLocalLangData(localLang));

      const localLangType = langType[localLang];
      let elementLocaleLang = null;
      let geekElement = {};
      if (localLangType) {
        if (localLangType.getElementLang) elementLocaleLang = langType[localLang].getElementLang();
        if (localLangType.getGeekElementLang)
          geekElement = langType[localLang].getGeekElementLang();
      }
      $req
        .reqLangMsg(apiUrl)
        .then(res => {
          const elementSource = elementLocaleLang ? elementLocaleLang.default : {};
          let langData = Object.assign({}, res.data, elementSource, geekElement.default);

          $i18n.locale = localLang;
          $i18n.mergeLocaleMessage(localLang, langData);
          this.setLocalLangData(localLang, JSON.stringify(langData));
        })
        .catch(e => {
          this.setLocalLang("zh_cn");
        });
    } else if (!$i18n.messages[localLang]) {
      setTimeout(() => {
        this.setI18nMessage($i18n, apiUrl);
      }, 1000);
    } else {
      $i18n.locale = localLang;
    }
  },

  // 权限模式相关
  getRMSPermission() {
    const str = localStorage.getItem("Geek_RMSPermission");
    return str === "true";
  },
  setRMSPermission(flag) {
    localStorage.setItem("Geek_RMSPermission", flag);
  },

  // 后台配置相关
  getRMSConfig() {
    let conf = localStorage.getItem("Geek_RMSConfig");
    if (conf) return JSON.parse(conf);
    else return conf;
  },
  setRMSConfig(str) {
    localStorage.setItem("Geek_RMSConfig", str);
  },

  // token相关
  getToken() {
    const rmsToken = Cookies.get("rmsToken");
    return rmsToken ? rmsToken : localStorage.getItem("Geek_RMSToken");
  },
  setToken(token) {
    Cookies.set("rmsToken", token);
    localStorage.setItem("Geek_RMSToken", token);
  },

  // sidebarStatus
  getSidebarStatus() {
    let status = localStorage.getItem("Geek_RMSSidebarStatus");
    if (status) return JSON.parse(status);
    else return status;
  },
  setSidebarStatus(flag) {
    localStorage.setItem("Geek_RMSSidebarStatus", flag);
  },

  // userInfo相关
  getUserInfo() {
    return localStorage.getItem("Geek_userInfo");
  },
  setUserInfo(userInfo) {
    localStorage.setItem("Geek_userInfo", userInfo);
  },

  // userInfo相关
  getRoleInfo() {
    if (this.getRMSPermission()) {
      let roleInfo = localStorage.getItem("Geek_roleInfo");
      return roleInfo;
    } else {
      let userInfo = localStorage.getItem("Geek_userInfo");
      return userInfo || "guest";
    }
  },
  setRoleInfo(roleInfo) {
    localStorage.setItem("Geek_roleInfo", roleInfo);
  },

  // menuList相关
  getMenuList() {
    let conf = localStorage.getItem("Geek_RMSMenuList");
    if (conf) return JSON.parse(conf);
    else return conf;
  },
  setMenuList(str) {
    localStorage.setItem("Geek_RMSMenuList", str);
  },
  setAuthBtnList(str) {
    localStorage.setItem("Geek_RMSAuthBtnList", str);
  },
  setAuthTabList(str) {
    localStorage.setItem("Geek_RMSAuthTabList", str);
  },

  // 前端自定义配置相关
  getRMSFEDConfig(key) {
    let conf = localStorage.getItem("Geek_RMSFEDConfig");

    if (conf) {
      let confJSON = JSON.parse(conf);
      if (key) {
        return confJSON[key];
      }
      return confJSON;
    } else return conf;
  },
  // obj必须是JSON格式
  setRMSFEDConfig(obj, key) {
    if (!$utils.Type.isObject(obj) && !$utils.Type.isArray(obj)) {
      throw new Error("obj必须是JSON格式||Array格式");
    }
    if (key) {
      let conf = $utils.Data.getRMSFEDConfig();
      if (conf) {
        conf[key] = obj;
        localStorage.setItem("Geek_RMSFEDConfig", JSON.stringify(conf));
      } else {
        conf = {};
        conf[key] = obj;
        localStorage.setItem("Geek_RMSFEDConfig", JSON.stringify(conf));
      }
    } else {
      localStorage.setItem("Geek_RMSFEDConfig", JSON.stringify(obj));
    }
  },

  // logout需要清空的Storage数据
  removeAllStorage() {
    localStorage.removeItem("Geek_staticConfig");
    localStorage.removeItem("Geek_RMSConfig");
    localStorage.removeItem("Geek_RMSMenuList");
    localStorage.removeItem("Geek_userInfo");
    localStorage.removeItem("Geek_RMSToken");
    Cookies.remove("rmsToken");
    $utils._initStatus.config = true;
    $utils._initStatus.menuList = true;
  },

  // login时重新reset storage数据
  loginResetStorage() {
    localStorage.removeItem("Geek_RMSMenuList");
    localStorage.removeItem("Geek_userInfo");
    localStorage.removeItem("Geek_RMSToken");
    Cookies.remove("rmsToken");
    $utils._initStatus.menuList = true;
    // 重新登录时重置一下headerTab
    if (!$app) return;
    const { commit } = $app.$store;
    commit("removeHeaderTabs");
  },
};
