@charset "utf-8";
@import url(./panel-rack-list.less);
@rightWidth: 300px;
.map2d-order-menu-sub {
  .ant-menu {
    padding: 2px 0 6px;
    margin-top: -6px;
  }
  .ant-menu-item {
    line-height: 28px;
    height: 28px;
    margin-bottom: 0 !important;
  }
}
#root .map2d-right-panel {
  position: absolute;
  right: 0;
  top: 38px;
  width: @rightWidth;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  transition-duration: 0.2s;
  box-shadow: 0 3px 5px #d9d6d6;
  background: #fff;
  max-height: calc(100% - 38px);
  &.is-collapse {
    transform: translateX(100%);
  }

  .map2d-control-btn-group {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    padding: 0 10px;
    background: #fff;
    border-radius: 2px;
    box-shadow: 0 3px 5px #d9d6d6;
    > .ant-btn.ant-btn-block {
      margin: 5px 0 0;
      height: 32px;
      text-align: left;
      &:first-child {
        margin-top: 10px;
      }
      &:last-child {
        margin-bottom: 10px;
      }
    }
  }

  .system-status-btn,
  .collapse-btn {
    position: absolute;
    right: @rightWidth;
    height: 32px;
    border-right: 0;
    transition-duration: 0.2s;
    box-shadow: -5px -5px 5px -4px #d9d6d6, -5px 5px 5px -4px #d9d6d6;
    border-radius: 5px 0 0 5px;
  }
  .system-status-btn {
    top: 30px;
    width: 30px;
    transform: translateX(380px);
    &.is-collapse {
      transform: translateX(0);
    }
  }
  .collapse-btn {
    top: 80px;
    width: 18px;
    min-width: 18px;
    &.is-collapse {
      width: 30px;
      top: 100px;
    }
  }

  .map2d-order-group {
    position: relative;
    width: 100%;
    flex: 1;
    overflow: auto;
    box-shadow: -5px 0 5px -5px #d9d6d6;
    margin-top: 5px;
    overflow-x: hidden;
  }

  .map2d-order-menu {
    position: sticky;
    width: 300px !important;
    top: 0;
    left: 0;
    overflow: hidden !important;
    border-bottom: 0;
    z-index: 9;
    .ant-menu-item,
    .ant-menu-submenu {
      position: relative !important;
      flex: 0 0 86px;
      line-height: 38px;
      height: 38px !important;
      text-align: center;
      background: #f5f7fa;
      color: #999;
      border: 1px solid transparent;
      overflow: hidden !important;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 0 5px;
      opacity: 1 !important;
      pointer-events: unset !important;

      &::after {
        border-bottom: 0 !important;
      }
      &:nth-child(4) {
        flex: 0 0 42px;
        padding: 0;
        .ant-menu-title-content {
          display: none;
        }
        .anticon {
          font-size: 16px !important;
          padding-top: 2px;
        }
      }
      &:nth-child(5) {
        z-index: -10;
        height: 0 !important;
        font-size: 0;
        padding: 0;
        margin: 0;
        width: 0 !important;
        order: 99999999 !important;
      }
    }
    .ant-menu-item-selected,
    .ant-menu-submenu-selected {
      background: #fff;
      border: 1px solid #dcdfe6;
      border-bottom: 0 !important;
      color: #1890ff;
    }
  }

  .map2d-order-group-component {
    padding: 10px;
    border-top: 1px solid #ddd;
    overflow-x: hidden;
    .ant-input-affix-wrapper {
      padding: 1px 11px !important;
    }
    .component-btn-group {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding-top: 4px;

      > button {
        width: 48%;
        letter-spacing: 1px;
        overflow: hidden;
        margin-top: 6px;
        white-space: unset;
        height: unset;
      }
    }
    .component-operate-detail {
      width: 100%;
      margin-top: 12px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      .ant-card-body {
        padding: 0;
      }
      .ant-card-actions {
        border: 0;
        > li {
          margin: 5px 0;
        }
      }

      &.ant-tabs {
        border: 1px solid #eee;
        border-bottom: 0;

        .ant-tabs-nav {
          margin: 3px 0 0;
          padding: 0 10px;
          .ant-tabs-tab {
            padding: 6px 0;
          }
        }
      }
    }

    .component-flex-grid {
      width: 100%;
      font-size: 14px;
      line-height: 2;
      margin: 8px 0 5px;
      word-break: break-word;
      border: 1px solid #eee;
      tr {
        line-height: 1.2;
        border-top: 1px solid #eee;

        > td {
          padding: 6px 4px 6px 1px;
          vertical-align: middle;
        }
        &:nth-child(2n) {
          background: #f6f6f6;
        }
        &:first-child {
          border-top: 0;
        }
      }
      .ant-select-selection-item {
        white-space: unset;
      }

      .item-label {
        color: #606266;
        text-align: right;
        font-weight: 700;
        font-size: 13px;
        user-select: text !important;
        width: 90px;
        border-right: 1px solid #eee;
        vertical-align: middle;
      }

      .item-value {
        user-select: text !important;
        padding-left: 5px;
        > input {
          width: 100%;
        }

        > button.submit {
          float: right;
        }
      }
    }
  }

  // 每个面板样式
  .map2d-order-group-component {
    .ant-tabs{width: 100%}
    .cell-radio,
    .charger-radio {
      .g-flex();
      width: 100%;
      margin-top: 8px;
      justify-content: flex-start;
      .ant-radio-button-wrapper {
        flex: 0 0 33.33%;
        display: flex;
        justify-content: center;
        .g-text-overflow();

        &:not(:first-child):before {
          left: 0;
        }
      }
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
        background: #ecf5ff;
      }
    }
    .warehouse-radio {
      padding: 8px 6px 8px;
      display: flex;
      flex-wrap: wrap;
      button {
        margin-right: 8px;
      }
    }

    .charger-error-number {
      padding: 8px 0;
      font-size: 14px;
      text-indent: 3px;

      > label {
        color: rgba(0, 0, 0, 0.65);
      }

      > span {
        color: #ff2328;
        margin-left: 6px;

        &:last-child {
          color: #fe8b04;
        }
      }
    }

    .warning-dialog-list,
    .charger-error-list,
    .warehouse-task-list,
    .zone-list {
      width: 100%;
      .ant-collapse-header {
        padding: 5px 10px;
        .type-error {
          color: #ff2328;
        }
        .type-warning {
          color: #fe8b04;
        }
      }
      .ant-collapse-content-box {
        padding: 0;
      }
    }
    .warehouse-task-list,
    .zone-list {
      width: 100%;
      margin-top: 8px;
      .ant-collapse-header {
        padding: 0 8px;
        .ant-collapse-expand-icon {
          margin-top: 8px;
        }
      }

      .warehouse-task-header,
      .zone-area-header {
        display: flex;
        height: 38px;
        line-height: 38px;
        justify-content: space-between;
        width: 100%;

        > label {
          border-right: 1px solid #eee;
          padding-right: 10px;
          max-width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        > span {
          font-size: 13px;
          padding: 0 5px;
          margin: 0 5px;
          color: #0da0f9;

          &.danger {
            color: #ff2328;
          }
          &.warning{color: #FF8C00;}
          &.fire-stop {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
            width: 75px;
            text-align: center;
            // padding: 0 !important;
            // margin: 0 !important;
          }
          &.stop {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
            width: 45px;
            text-align: center;
            // padding: 0 !important;
            // margin: 0 !important;
          }
          &.speed-limit{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
            width: 90px;
            text-align: center;
            // padding: 0 !important;
            // margin: 0 !important;
          }
        }

        > a:hover {
          color: #0da0f9;
        }
      }
    }
    .zone-number-box {
      width: 100%;
      border-top: 1px solid #eee;
      margin: 10px 0 0;
      padding: 6px 0 0;

      > li {
        display: flex;
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        line-height: 24px;

        &:first-child {
          padding-bottom: 5px;
        }

        > label {
          padding-right: 8px;
        }

        > span {
          color: #0da0f9;
        }

        > div > p {
          display: flex;
          color: #0da0f9;

          &.danger {
            color: #ff2328;
          }
          &.warning {
            color: #fe8b04;
          }

          > label {
            padding-right: 10px;
          }
        }
      }
    }
    .panel-right-title {
      font-size: 14px;
      font-weight: 800;
      margin-bottom: 6px;
    }
  }
}
