import Chart, { requestCache } from "../common";

/**
 * 2.2.1机器人利用率(P40工作中的机器人总数)
 */
export default class RobotTotalCredByP40 extends Chart {
  /**
   * 初始化图表 - 2.2.1机器人利用率(P40工作中的机器人总数)
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('cred', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "P40工作中机器人";
    this.isFilterParams = option.isFilterParams || true;
    this.filterList = option.filterList || ['date', 'cycle'];
  }

  async request(params) {
    const { data } = await requestCache('/athena/stats/query/robot/snapshot', {
      date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
      cycle : "5",
      ...params
    })

    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const totalDataItem = data?.robotSnapshotList || [];
    let number = 0;
    totalDataItem.filter(item => item.haveData).forEach(item => {
      number += item['DEFAULT_P40_WORK'] || item['P40_WORK'] || 0;
    });

    return {
      id: 'P40_WORK',
      title: this.title || '',
      number: number,
      color: "#409EFF",
    }
  }
} 