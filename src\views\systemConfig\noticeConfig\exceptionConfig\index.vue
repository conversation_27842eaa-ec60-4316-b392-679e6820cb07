<template>
  <div class="app-container">
    <editDialog
      v-if="showDialog"
      :show-dialog="showDialog"
      :edit-data="editData"
      @closeDialog="closeDialog()"
      @updataQueueNum="changeQueueNum"
    />
    <el-card>
      <!-- 查询条件 -->
      <el-form label-position="top" label-width="80px" :model="queryInfo">
        <el-row :gutter="30">
          <!-- 查询 -->
          <el-col :span="8">
            <el-form-item :label="$t('lang.rms.fault.exception.code')">
              <el-input
                v-model.trim="queryInfo.systemCode"
                :placeholder="$t('lang.rms.fed.pleaseEnter')"
                class="w_100x"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6" class="btnwarp2">
            <el-button type="primary" @click="onSearch()">{{ $t("lang.rms.fed.query") }}</el-button>
            <el-button type="primary" @click="resetSearchData">
              {{ $t("lang.rms.fed.reset") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="mt-20">
      <!-- 列表信息 -->
      <el-table :data="tableData" style="width: 100%">
        <el-table-column
          prop="systemCode"
          :label="$t('lang.rms.fault.exception.code')"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column :label="$t('lang.rms.web.monitor.exception.info')">
          <template slot-scope="scope">
            <div>{{ $t(scope.row.exception) }}</div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('lang.rms.fed.chargerSolution')">
          <template slot-scope="scope">
            <div>{{ $t(scope.row.solution) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('lang.rms.fed.showEnableOrDisable')" min-width="90px">
          <template slot-scope="scope">
            <div>
              {{
                scope.row.status === 1
                  ? $t("lang.rms.fed.chargerEnable")
                  : $t("lang.venus.common.dict.disable")
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="callbackDisable" :label="$t('lang.rms.fed.callbackEnableOrDisable')" min-width="100px">
          <template slot-scope="scope">
            <div>
              {{
                scope.row.callbackDisable
                  ? $t("lang.rms.fed.chargerEnable")
                  : $t("lang.venus.common.dict.disable")
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="callbackDuration"
          :label="$t('lang.rms.fed.callbackDuration')"
          min-width="120px"
        ></el-table-column>
        <el-table-column
          prop="exceptionDuration"
          :label="$t('lang.rms.fed.exceptionDuration')"
          min-width="120px"
        ></el-table-column>
        <el-table-column
          prop="messageGroup"
          :label="$t('lang.rms.backlog.fault.isBacklog')"
          min-width="100px"
        >
          <template slot-scope="scope">
            <div>
              {{ scope.row.messageGroup === 2 ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('lang.rms.backlog.fault.isEmailNotice')" min-width="90px">
          <template slot-scope="scope">
            <div>{{ scope.row.isSendEmail ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}</div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('lang.rms.backlog.fault.isMaintenance')" min-width="90px">
          <template slot-scope="scope">
            <div>
              {{ scope.row.isMaintenance ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="!isRoleGuest"
          fixed="right"
          :label="$t('lang.rms.fed.chargerOperating')"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="onEdit(scope.row)">
              {{ $t("lang.rms.fed.edit") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="mt-20"
        :current-page="pageData.pagecurrent"
        :page-sizes="[10, 25, 50, 100]"
        :page-size="pageData.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="taskCount"
        @size-change="pageSizeChange"
        @current-change="goPage"
      >
      </el-pagination>
    </el-card>
  </div>
</template>
<script>
import editDialog from "./model/editDialog";

export default {
  name: "FaultConfiguration",
  components: {
    editDialog,
  },
  data() {
    return {
      // 搜索内容
      queryInfo: {
        // 工作站id
        systemCode: "",
      },
      querySearchList: [],
      timeout: null,
      pageData: {
        // 当前页数
        pagecurrent: 1,
        // 每页展示数
        pagesize: 10,
      },
      // 设置table的数据
      tableData: [],
      // 当前数据总数
      taskCount: 0,
      editData: {},
      showDialog: false,
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  created() {
    this.getTableList();
  },
  methods: {
    /* 翻页 */
    pageSizeChange(data) {
      this.pageData.pagecurrent = 1;
      this.pageData.pagesize = data;
      this.getTableList();
    },
    // 跳页
    goPage(pageCurrent) {
      this.pageData.pagecurrent = pageCurrent;
      this.getTableList();
    },

    // 编辑
    onEdit(data) {
      this.showDialog = true;
      this.editData = data;
    },
    // 更新
    closeDialog(bel) {
      this.showDialog = bel;
      // this.getTableList();
    },

    changeQueueNum(editData) {
      const newData = {
        systemCode: editData.systemCode,
        isSendEmail: editData.isSendEmail,
        isMaintenance: editData.isMaintenance,
        messageGroup: editData.messageGroup,
        delaySendEmailInterval: Number(editData.delaySendEmailInterval),
        callbackDuration: Number(editData.callbackDuration),
        exceptionDuration: Number(editData.exceptionDuration),
        status: editData.status,
        callbackDisable: editData.callbackDisable
      };
      $req.post("/athena/fault/message/updateFaultMessage", newData).then(res => {
        const successMsg = this.$t(res.msg);
        if (res.code === 0) {
          this.$message({
            message: successMsg,
            type: "success",
          });
          this.showDialog = false;
          this.getTableList();
        }
      });
    },

    // 重置搜索参数
    resetSearchData() {
      for (const key in this.queryInfo) {
        if (this.queryInfo.hasOwnProperty(key)) {
          this.queryInfo[key] = "";
        }
      }
      this.onSearch();
    },
    // 查询
    onSearch() {
      this.pageData.pagecurrent = 1;
      this.getTableList();
    },
    getTableList() {
      const queryInfo = this.queryInfo;
      queryInfo.currentPage = this.pageData.pagecurrent;
      queryInfo.pageSize = this.pageData.pagesize;
      $req.get("/athena/fault/message/getFaultMessages", queryInfo).then(res => {
        const { currentPage = 1, pageSize = 10, recordCount = 0, recordList = [] } = res.data || {};
        this.tableData = recordList;
        this.pageData.pagesize = pageSize;
        this.pageData.pagecurrent = currentPage;
        this.taskCount = recordCount;
      });
    },
    onSwitch(row) {
      console.log(row);
      updateParsingMode({
        id: row.id,
        status: row.status,
      }).then(() => {});
    },
  },
};
</script>
<style scoped>
.mt-20 {
  margin-top: 20px;
}
.w_100x {
  width: 100%;
}
.btnwarp2 {
  padding: 35px 0 0;
}
.floor {
  height: 50px;
}

.floor > .page {
  float: left;
  line-height: 50px;
}

.floor > .jump {
  float: right;
  line-height: 50px;
}
.el-dropdown-link {
  margin-left: 10px;
}
</style>
