/* ! <AUTHOR> at 2022/08/29 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  RightOutlined,
  LeftOutlined,
  MenuUnfoldOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import { Button, Menu, notification } from "antd";
import { $eventBus } from "../../singleton";

import tabList from "./config/tab-list";
import ControlGroup from "./components/control-group";
import OrderGroup from "./components/order-group";
const defaultTab = tabList[0];
type PropsControlGroup = {
  mapReleased: boolean;
  mapConfig: MWorker.mapConfig;
  logicAreas: Array<any>;
  speedLimitAreas: Array<any>;
};
function RightPanel(props: PropsControlGroup) {
  if (!defaultTab || !defaultTab["key"]) return;
  const { t } = useTranslation();
  const [isCollapse, setIsCollapse] = useState(false);
  const [currentMenu, setCurrentMenu] = useState(defaultTab["key"]);
  const [deadRobots, setDeadRobots] = useState(null);
  // 地图切换图层时操作一下默认
  useEffect(() => {
    $eventBus.on("wsMapFloorChangeRight", data => {
      if (data?.floorChanging) {
        setCurrentMenu(null);
      } else {
        setCurrentMenu(defaultTab["key"]);
      }
    });
    return () => {
      $eventBus.off("wsMapFloorChangeRight");
    };
  }, []);

  // 地图机器人死锁
  useEffect(() => {
    $eventBus.on("wsDeadLockRobotsRight", wsData => {
      const { deadLockCodes, deadLockRobots } = wsData || {};
      let robotCodes: Array<code> = Object.keys(deadLockRobots);
      setDeadRobots({ robotData: deadLockRobots, robotCodes });
      notification.close("deadLockRobots");
      if (!robotCodes.length) return;

      const RMSConfig = _$utils.getRMSConfig();
      if (!RMSConfig || !RMSConfig["enabledShowDeadLockWarning"]) return;
      notification.error({
        key: "deadLockRobots",
        message: t("lang.rms.fed.optionWarning"),
        description: (
          <>
            {(deadLockCodes.includes(12102) || deadLockCodes.includes(12101)) && (
              <div>{t("lang.rms.fed.deadlockResolutionMsg1")}</div>
            )}
            {deadLockCodes.includes(12033) && <div>{t("lang.rms.fed.deadlockResolutionMsg2")}</div>}
            {deadLockCodes.includes(12034) && <div>{t("lang.rms.fed.deadlockResolutionMsg3")}</div>}
          </>
        ),
        onClick: () => {
          setCurrentMenu("OrderGroupDeadlock");
          notification.close("deadLockRobots");
        },
        className: "map2d-dead-robot-notification",
        duration: null,
        placement: "bottomLeft",
      });
    });
    return () => {
      $eventBus.off("wsDeadLockRobotsRight");
    };
  }, []);

  const items: any = tabList.slice(0, 3).map(item => {
    return {
      label: t(item.label),
      key: item.key,
    };
  });
  const menuDrop: any = {
    label: null,
    key: "SubMenu",
    icon: <MenuUnfoldOutlined />,
    popupClassName: "map2d-order-menu-sub",
    children: tabList.slice(3).map(item => {
      return {
        label: t(item.label),
        key: item.key,
        icon: <CheckCircleOutlined />,
      };
    }),
  };

  return (
    <div className={`map2d-right-panel ${isCollapse ? "is-collapse" : ""}`}>
      <ControlGroup isCollapse={isCollapse} mapConfig={props.mapConfig} />
      <div className="map2d-order-group">
        <Menu
          selectedKeys={[currentMenu]}
          mode="horizontal"
          triggerSubMenuAction="click"
          items={[...items, menuDrop]}
          onSelect={({ key }: any) => setCurrentMenu(key)}
          className="map2d-order-menu"
        />
        <OrderGroup
          currentMenu={currentMenu}
          mapReleased={props.mapReleased}
          mapConfig={props.mapConfig}
          logicAreas={props.logicAreas}
          speedLimitAreas={props.speedLimitAreas}
          deadRobots={deadRobots}
        />
      </div>
      <Button
        shape="circle"
        icon={isCollapse ? <LeftOutlined /> : <RightOutlined />}
        className={`collapse-btn ${isCollapse ? "is-collapse" : ""}`}
        onClick={() => setIsCollapse(!isCollapse)}
      />
    </div>
  );
}

export default RightPanel;
