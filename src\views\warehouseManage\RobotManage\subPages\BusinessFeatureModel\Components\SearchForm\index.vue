<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2022-01-05 20:04:30
 * @Description:
-->
<template>
  <el-form
    ref="searchForm"
    :inline="true"
    class="demo-form-inline"
    label-position="top"
    :model="searchForm"
  >
    <!-- <el-form-item
      :label="$t('lang.rms.api.result.warehouse.businessFeatureNo')"
      prop="businessCode"
    >
      <el-input
        v-model.trim="searchForm.businessCode"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterBusinessNo')"
      />
    </el-form-item> -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.businessFeatureName')" prop="name">
      <el-input
        v-model.trim="searchForm.name"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterBusinessFeatureModelName')"
      />
    </el-form-item>
    <el-form-item label="sizeType" prop="sizeTypes">
      <el-select
        v-model="searchForm.sizeTypes"
        multiple
        filterable
        allow-create
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterSelectedQueryCriteria')"
      >
        <el-option v-for="item in sizeTypeArr" :key="item" :value="item">{{ item }}</el-option>
      </el-select>
    </el-form-item>
    <el-form-item class="align-bottom">
      <el-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.query") }}</el-button>
      <el-button type="primary" @click="resetForm">{{ $t("lang.rms.fed.reset") }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    sizeTypeArr: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    // 这里存放数据
    return {
      searchForm: {
        // businessCode: "",
        name: null,
        sizeTypes: "",
      },
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  mounted() {
    this.onSubmit();
  },
  // 方法集合
  methods: {
    onSubmit() {
      this.$emit("onsubmit", { ...this.searchForm });
    },
    resetForm() {
      this.$refs["searchForm"].resetFields();
      this.onSubmit();
    },
  },
};
</script>
<style lang="scss" scoped>
.align-bottom {
  vertical-align: bottom;
}
</style>
