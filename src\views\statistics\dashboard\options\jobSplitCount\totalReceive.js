export default {
  // 地图数据信息
  chart: {
    // 改为饼图
    type: 'bar', // 图表类型

    // 地图数据来源
    request: {
      url: '/athena/stats/query/job/split/count/',  // 请求接口
      filters: ['date', 'cycle', 'showReceive', 'showToStationDone', 'showOrgData'], // 筛选条件
      defFilters: {
        date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
        cycle: "60",
        showReceive: true,
        showToStationDone: true, 
        showOrgData: false
      },
      timer: 5000,  // 轮询时间, 如果是0, 则不轮询
    },

    // 地图数据处理
    dataHandler: {  // 数据处理
      handler: 'getStationReceiveByTotal',
      params: {
        type: 'bar',
        paramsName: 'total_stationReceive',
        title: '工作站接收任务总数'
      },
    },
  }
}