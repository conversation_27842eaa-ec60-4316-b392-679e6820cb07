import CellElement from "../element/Cell";
import Store from "../store/Store";
import Base from "./Base";
class Cell extends Base {
  constructor(props) {
    super(props);
    this.layerName = "CELL";
    //特殊处理，遇到这些单元格的时候，不允许添加空载方向
    this.ignoreCell = ['QUEUE_CELL','STATION_CELL','TURN_CELL']
  }

  //初始化元素
  initElements(data = []) {
    data.forEach(item => {
      const { nodeId } = item;
      const $el = CellElement.add(item);
      $el.layerName = this.layerName;
      this.container.addChild($el);
      this.setProperties(nodeId, $el, item);
    });
  }
  //添加元素
  addElements(data = []) {
    //历史数据
    const historyData = [];
    const addedData = [];
    data.forEach(item => {
      const { nodeId } = item;
      const $el = CellElement.add(item);
      $el.layerName = this.layerName;
      this.container.addChild($el);
      //添加节点到四叉树中进行维护
      // Store.tree.insert($el)
      this.setProperties(nodeId, $el, item);
      addedData.push(this.getProperties(nodeId));
      historyData.push(item);
    });
    //新增后，重新构建四叉树,cell类型节点变动才需要构建四叉树
    Store.tree.reBuild();
    //添加历史记录
    const historyDetail = { action: "add", detail: historyData, layerName: this.layerName };
    return { historyDetail, emitData: addedData };
  }
  //更新元素,现在的更新是增量更新，有时需要覆盖属性
  updateElements(data = [], isCoverProperties = false) {
    console.log("update", data);
    //历史数据
    const historyData = [];
    const updateData = [];
    data.forEach(item => {
      const { nodeId,cellType } = item;
      //单元格的特殊处理，如果包含ignoreCell中的单元格，置空loadDirs,unloadDirs
      if(this.ignoreCell.includes(cellType)){
        item.loadDirs = null
        item.unloadDirs = null
      }
      //旧数据存储
      const { properties: oldProperties } = this.getProperties(nodeId);
      const { cellCode: oldCellCode, mapEditItemId: oldMapEditItemId } = { ...oldProperties };
      //设备与cell关联id
      const oldRelatedId = oldCellCode || oldMapEditItemId;
      historyData.push({ ...oldProperties });
      //更新新数据
      const $el = this.id2$el.get(nodeId);
      this.setProperties(nodeId, $el, item, isCoverProperties);
      //获取新数据
      const updateInfo = this.getProperties(nodeId);
      const { properties: newProperties } = updateInfo;
      const { cellCode: newCellCode, mapEditItemId: newMapEditItemId } = newProperties;
      const newRelatedId = newCellCode || newMapEditItemId;
      CellElement.update($el, newProperties);
      //当cellCode发生变更时，更新设备与cellCode的关系
      Store.cellCode2Device.update(oldRelatedId, newRelatedId);
      updateData.push({ ...updateInfo });
    });
    //更新后，重新构建四叉树,cell类型节点变动才需要构建四叉树
    Store.tree.reBuild();
    //添加历史记录
    const historyDetail = { action: "update", detail: historyData, layerName: this.layerName };
    return { historyDetail, emitData: updateData };
  }
  //删除元素
  deleteElements(ids = []) {
    //历史数据
    const historyData = [];
    const deleteData = [];
    ids.forEach(id => {
      const { $el, properties } = this.getProperties(id);
      historyData.push({ ...properties });
      deleteData.push({ $el: null, properties: { ...properties } });
      this.id2$el.delete(id);
      this.container.removeChild($el);
    });
    const historyDetail = { action: "delete", detail: historyData, layerName: this.layerName };
    //删除完成后，重新构建四叉树,cell类型节点变动才需要构建四叉树
    Store.tree.reBuild();
    return { historyDetail, emitData: deleteData };
  }
  //更改dir显示类型
  showDirByType() {
    const $con = this.container;
    $con.children.forEach(child => {
      const properties = this.$el2properties.get(child);
      CellElement.renderDir(child, properties);
    });
  }
  //显示图层
  showLayer(cellType = null) {
    this.container.visible = true;
    this.container.children.forEach(child => {
      if (cellType) {
        child.visible = child.cellType === cellType;
      } else {
        child.visible = true;
      }
    });
  }
  hideLayer(cellType = null) {
    if (!cellType) return (this.container.visible = false);
    this.container.children.forEach(child => {
      child.visible = !(child.cellType === cellType);
    });
  }
  //通过cellCode反向查询nodeId
  getNodeIds(cellCodes = []) {
    const nodeIds = [];
    if (!cellCodes.length) return nodeIds;
    const id2$elArr = [...this.id2$el];
    id2$elArr.forEach(arr => {
      const [nodeId, $el] = arr;
      const { cellCode } = $el;
      if (cellCodes.includes(cellCode)) {
        nodeIds.push(nodeId);
      }
    });
    return nodeIds;
  }
  //是否可以被点击
  triggerLayer(isTrigger) {
    this.container.interactiveChildren = isTrigger;
  }
}
export default Cell;
