// 全局点位编辑
export const cellEditConfFn = () => {
  return [
    {
      prop: "intervalGap",
      label: "步进距离",
      component: "elInputNumber",
      describe: "步进单位/m",
      step: 0.1,
      precision: 3,
      max: 10000,
      min: 0.001,
    },
    {
      prop: "intervalAngle",
      label: "步进角度",
      component: "elInputNumber",
      describe: "步进单位/m",
      step: 0.1,
      precision: 3,
      max: 360,
      min: 0.001,
    },
    {
      prop: "moveX",
      label: "X轴偏移",
      component: "elInputNumber",
      describe: "偏移距离/m",
      precision: 3,
      max: 1000000,
      min: -1000000,
      appendAttrsFn(value: number, dataAll: { [k: string]: any }) {
        return {
          step: dataAll.intervalGap || 0.1,
        };
      },
    },
    {
      prop: "moveY",
      label: "Y轴偏移",
      component: "elInputNumber",
      describe: "偏移距离/m",
      precision: 3,
      max: 1000000,
      min: -1000000,
      appendAttrsFn(value: number, dataAll: { [k: string]: any }) {
        return {
          step: dataAll.intervalGap || 0.1,
        };
      },
    },
    {
      prop: "angle",
      label: "角度偏移",
      component: "elInputNumber",
      describe: "偏移距离/m",
      precision: 3,
      max: 360,
      min: -360,
      appendAttrsFn(value: number, dataAll: { [k: string]: any }) {
        return {
          step: dataAll.intervalAngle || 0.1,
        };
      },
    },
  ];
};
