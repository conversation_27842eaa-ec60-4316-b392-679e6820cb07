import { NodeAttrEditConf } from "@packages/type/editUiType";
import DICT from "@packages/configure/dict";

// 模型
export const MODEL_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  const { PALLET_RACK_CELL, SHELF_CELL } = DICT;
  const curNodeDataByIndex = attrStore.curNodeDataByIndex;
  const isShelf = curNodeDataByIndex.cellType === SHELF_CELL;
  const isHolder = curNodeDataByIndex.cellType === PALLET_RACK_CELL;
  let holderData: any[] = [];
  if (isShelf) { holderData = attrStore.shelfModelList }
  if (isHolder) { holderData = attrStore.holderModelList }

  attrStore.getShelfModelListByOnly()
  attrStore.getHolderModelListByOnly();

  return {
    name: "model",
    tabTitle: "lang.rms.fed.supportModel",
    formItem: [
      // 启用禁用
      {
        prop: "isShelfHolder",
        label: "lang.rms.fed.enable",
        component: "elSwitch",
        labelWidth: "70px",
        get(fromData: {[k: string]: any }) {
          return !!fromData.shelfHolder;
        },
        set(value: boolean, fromData: {[k: string]: any }) {
          if (value) {
            fromData.shelfHolder = {};
          } else {
            fromData.shelfHolder = null;
          }
        },
      },
      // 模型名称
      {
        prop: "holderTypeId",
        label: "lang.rms.fed.groundSupportModelAlias",
        component: "elSelect",
        labelWidth: "70px",
        loading: !holderData,
        data: (holderData || []).map((item: any) => ({ label: item.modelName, value: item.id })),
        // data: [],
        get(fromData: {[k: string]: any }) {
          return (fromData.shelfHolder || {}).holderTypeId;
        },
        set(value: string, fromData: {[k: string]: any }) {
          fromData.shelfHolder.holderTypeId = value;
        },
        event: {
          visibleChange(visible: boolean) {
            if (visible) {
              if (isShelf) {
                attrStore.getShelfModelListByOnly();
              } else if (isHolder) {
                attrStore.getHolderModelListByOnly();
              }
            }
          },
        },
        appendAttrsFn(value: string, data: any) {
          return { disabled: !data.shelfHolder };
        },
      },
      // 角度
      {
        prop: "holderAngle",
        label: "lang.rms.fed.angle",
        component: "elInputNumber",
        labelWidth: "70px",
        min: -180,
        max: 180,
        step: 90,
        get(fromData: {[k: string]: any }) {
          return (fromData.shelfHolder || {}).holderAngle;
        },
        set(value: string, fromData: {[k: string]: any }) {
          fromData.shelfHolder.holderAngle = value;
        },
        appendAttrsFn(value: number, data: any) {
          return { disabled: !data.shelfHolder };
        },
      },
      // 取货架行为模式
      {
        prop: "fetchBehavior",
        label: "lang.rms.fed.pickUpShelvesBehaviorPattern",
        component: "elSelect",
        data: DICT.GROUND_S_ADJUSTMENT_CONTSHELF || [],
        get(fromData: {[k: string]: any }) {
          const fetchBehavior = (fromData.shelfHolder || {}).fetchBehavior
          if(fetchBehavior !== undefined){
            return fetchBehavior
          }else{
            return ''
          }
          // return String((fromData.shelfHolder || {}).fetchBehavior || '');
        },
        set(value: string, fromData: {[k: string]: any }) {
          fromData.shelfHolder.fetchBehavior = value;
        },
        appendAttrsFn(value: string, data: any) {
          return { disabled: !data.shelfHolder };
        },
      },
      // 送货架行为模式
      {
        prop: "deliverBehavior",
        label: "lang.rms.fed.deliveryShelfBehaviorPattern",
        component: "elSelect",
        data: DICT.GROUND_S_ADJUSTMENT || [],
        get(fromData: {[k: string]: any }) {
          const deliverBehavior = (fromData.shelfHolder || {}).deliverBehavior
          if(deliverBehavior !== undefined){
            return deliverBehavior
          }else{
            return ''
          }
          // return String((fromData.shelfHolder || {}).deliverBehavior|| '');
        },
        set(value: string, fromData: {[k: string]: any }) {
          fromData.shelfHolder.deliverBehavior = value;
        },
        appendAttrsFn(value: string, data: any) {
          return { disabled: !data.shelfHolder };
        },
      },
      // 还货架行为模式
      {
        prop: "returnBehavior",
        label: "lang.rms.fed.goReturnBehaviorPattern",
        component: "elSelect",
        data: DICT.GROUND_S_ADJUSTMENT || [],
        get(fromData: {[k: string]: any }) {
          const returnBehavior = (fromData.shelfHolder || {}).returnBehavior
          if(returnBehavior !== undefined){
            return returnBehavior
          }else{
            return ''
          }
          // return String((fromData.shelfHolder || {}).returnBehavior || '');
        },
        set(value: string, fromData: {[k: string]: any }) {
          fromData.shelfHolder.returnBehavior = value;
        },
        appendAttrsFn(value: string, data: any) {
          return { disabled: !data.shelfHolder };
        },
      },
    ],
  };
};
