<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2021-12-27 12:13:50
 * @Description:
-->
<template>
  <geek-main-structure class="parameterConfig">
    <el-tabs v-model="activeName" class="tab-class">
      <!-- 机器人实例 -->
      <el-tab-pane
        v-if="tabNamePerssion['first5']"
        :label="$t('lang.rms.api.result.warehouse.robotExample')"
        name="first5"
      >
        <RobotInstance />
      </el-tab-pane>
      <!-- 机器人型号 -->
      <el-tab-pane
        v-if="tabNamePerssion['first4']"
        :label="$t('lang.rms.api.result.warehouse.robotModel')"
        name="first4"
      >
        <RobotModal />
      </el-tab-pane>
      <!-- 业务特征模型 -->
      <el-tab-pane
        v-if="tabNamePerssion['first3']"
        :label="$t('lang.rms.api.result.warehouse.businessModel')"
        name="first3"
      >
        <BusinessFeatureModel @goNextPage="handleGoNextPage('first4')" />
      </el-tab-pane>
      <!-- 机构组件模型 -->
      <el-tab-pane
        v-if="tabNamePerssion['first2']"
        :label="$t('lang.rms.api.result.warehouse.mechanismComponentModel')"
        name="first2"
      >
        <MechanismComponentModel @goNextPage="handleGoNextPage('first3')" />
      </el-tab-pane>
      <!-- 机构模型 -->
      <el-tab-pane
        v-if="tabNamePerssion['first1']"
        :label="$t('lang.rms.api.result.warehouse.mechanismModel')"
        name="first1"
      >
        <MechanismModel @goNextPage="handleGoNextPage('first2')" />
      </el-tab-pane>
      <!-- 本体模型 -->
      <el-tab-pane
        v-if="getTabPermission('TabOntologyPage', 'robotManage')"
        :label="$t('lang.rms.api.result.warehouse.ontologyModel')"
        name="first8"
      >
        <InstitutionsModel @goNextPage="handleGoNextPage('first1')" />
      </el-tab-pane>
    </el-tabs>
  </geek-main-structure>
</template>

<script>
import InstitutionsModel from "./subPages/InstitutionsModel";
import RobotInstance from "./subPages/RobotInstance";
import RobotModal from "./subPages/RobotModal";
import BusinessFeatureModel from "./subPages/BusinessFeatureModel";
import MechanismComponentModel from "./subPages/MechanismComponentModel";
import MechanismModel from "./subPages/MechanismModel";

export default {
  components: {
    InstitutionsModel,
    RobotInstance,
    RobotModal,
    BusinessFeatureModel,
    MechanismComponentModel,
    MechanismModel,
  },
  data() {
    // 这里存放数据
    return {
      tabNamePerssion: {
        first5: this.getTabPermission("TabInstancePage", "robotManage"),
        first4: this.getTabPermission("TabModelPage", "robotManage"),
        first3: this.getTabPermission("TabBussinessPage", "robotManage"),
        first2: this.getTabPermission("TabComponentManage", "robotManage"),
        first1: this.getTabPermission("TabMechanismPage", "robotManage"),
        first8: this.getTabPermission("TabOntologyPage", "robotManage"),
      },
      defaultActive: "first5",
    };
  },
  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
  },
  mounted() {
    this.getDictionary();

    this.defaultActive = $utils.Tools.getRouteQueryTabName(this.defaultActive, this.tabNamePerssion);
  },
  // 方法集合
  methods: {
    getDictionary() {
      const { commit } = $app.$store;
      const types = [
        "ROBOT_SERIES", // 系列
        "NAVIGATION_MODE", // 导航方式
        "ROBOT_BEHAVIOR", // 行为能力
        "ROBOT_TASK_TYPE", // 任务类型
        "ROBOT_BIZ", // 可执行的业务
        "ROBOT_BATTERY_TYPE", // 电池类型
        "ROBOT_MOTION_MODE", // 移动方式
        "ROBOT_ACTION", // 动作能力
        "ROBOT_VERSION", // 协议版本
      ];
      $req.post("/athena/dict/query", { types: types }).then(res => {
        // 型号、任务类型等下拉框元素接口
        if (res.code === 0) {
          commit("setDictionary", res.data || []);
        }
      });
    },
    handleGoNextPage(activeName) {
      if (this.tabNamePerssion[activeName]) {
        this.defaultActive = activeName;
      } else {
        this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.outer-wrap {
  background: white !important;
  height: auto;
  :deep(.el-table .el-table__row .el-table__cell div) {
    word-break: break-all !important;
  }
}
.inner-wrap {
  width: 100%;
  box-sizing: border-box;
  padding-right: 25px;
  padding-left: 25px;
  padding-bottom: 20px;
}
</style>
