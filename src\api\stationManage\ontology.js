/*
 * @Author: dingt<PERSON> (<EMAIL>)
 * @Date: 2022-01-15 17:38:44
 * @Description: 工作站相关接口
 */
import request from '../request';

// 工作站查询接口
export function getStationPageList(params = {}) {
  return request({ url: '/athena/station/stationPageList', method: 'get', params })
}

// 工作站模型列表
export function getStationModalPageList(params = {}) {
  return request({ url: '/athena/station/stationModelPageList	', method: 'get', params })
}

// 获取工作站类型
export function getStationType(data = {}, params = {}) {
  return request({ url: '/athena/station/stationType', method: 'get', data, params })
}

// 获取地图信息
export function getMapInfo(data = {}, params = {}) {
  return request({ url: '/athena/station/map', method: 'get', data, params })
}

// 启用
export function enableStation(data = {}, params = {}) {
  return request({ url: '/athena/station/enable', method: 'post', data, params })
}

// 停用
export function disableStation(data = {}, params = {}) {
  return request({ url: '/athena/station/disable', method: 'post', data, params })
}

// 更新工位最大排队数量
export function updateMaxRobotQueueSize(data = {}, params = {}) {
  return request({ url: '/athena/station/updateMaxRobotQueueSize', method: 'post', data, params })
}
