import Chart, { requestCache } from "../common";

/**
 * 3.2 机器人充电汇总
 */
export default class RobotChargingTable extends Chart {
  /**
   * 初始化图表 - 3.2 机器人充电无电量统计
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('table', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "机器人充电无电量统计";
    this.isFilterParams = option.isFilterParams || true;
    this.filterList = option.filterList || ['startTime', 'endTime', 'statType', 'mapId', 'codes', 'currentPage', 'pageSize'];
  }

  async request(params) {
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stat/charger/detailByRobotId', {
      startTime: new Date().setHours(0, 0, 0, 0),
      endTime: new Date().setHours(23, 59, 59, 0),
      currentPage: 1,
      pageSize: 20,
      ...params
    })
    
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const dataItem = [];
    Object.values(data).forEach(item => {
      dataItem.push(...item)
    })
    
    let tableCloumns = [
      { label: '机器人编号', prop: 'robotId' },
      { label: '充电站编号', prop: 'chargerId' },
      { label: '充电开始时间', prop: 'chargeStartTime', type: 'time' },
      { label: '充电结束时间', prop: 'chargeEndTime', type: 'time' },
      { label: '充电时长(s)', prop: 'chargeDuration' },
      { label: '起始电量(%)', prop: 'chargeStartPower' },
      { label: '结束电量(%)', prop: 'chargeEndPower' },
    ];

    return {
      title: this.title,
      tableCloumns,
      tableData: dataItem,
    }
  }
}