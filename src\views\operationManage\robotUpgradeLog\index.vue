<template>
  <!-- 机器人升级日志 -->
  <geek-main-structure class="robot-upgrade-log">
    <div class="form-content">
      <geek-customize-form
        :form-config="formConfig"
        @on-query="onQuery"
        @on-reset="onReset"
      />
    </div>
    <div class="table-content">
      <el-table :data="robotStatus" style="width: 100%">
        <el-table-column type="index" width="50" />
        <el-table-column prop="robotId" :label="$t('lang.rms.fed.robot') + 'ID'" />
        <el-table-column prop="version" :label="$t('lang.rms.fed.edition')" />
        <el-table-column prop="status" :label="$t('lang.rms.fed.state')">
          <template slot-scope="scope">{{ $t(statusList[scope.row.status]) }}</template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('lang.rms.fed.upgradeTime')">
          <template slot-scope="scope">
            {{ setTime(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: right;">
        <geek-pagination
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          :total-page="recordCount"
          @currentPageChange="currentPageChange"
          @pageSizeChange="pageSizeChange"
        />
      </div>
    </div>
  </geek-main-structure>
</template>

<script>
export default {
  name: 'RobotUpgradeLog',
  components: {},
  data() {
    return {
      params: {},
      page: {
        pageSize: 10,
        currentPage: 1
      },
      recordCount: 0,
      formConfig: {
        attrs: {
          labelWidth: '80px',
          inline: true
        },
        configs: {
          robotId: {
            label: this.$t('lang.rms.fed.robot') + 'ID',
            default: '',
            tag: 'input',
            placeholder: this.$t('lang.rms.fed.pleaseEnterTheRobotID')
          },
          status: {
            label: this.$t('lang.rms.fed.state'),
            tag: 'select',
            default: '',
            placeholder: '',
            options: [
              {
                value: 0,
                label: this.$t('lang.rms.fed.upgradeIsNowBeingPrepared')
              },
              {
                value: 1,
                label: this.$t('lang.rms.fed.underUpgrade')
              },
              {
                value: 2,
                label: this.$t('lang.rms.fed.upgradeIsDone')
              },
              {
                value: 3,
                label: this.$t('lang.rms.fed.upgradesAnomaly')
              }
            ]
          },
          version: {
            label: this.$t('lang.rms.fed.softwareOfRobot') + 'ID',
            default: '',
            tag: 'input',
            placeholder: this.$t('lang.rms.fed.pleaseEnter')
          },
          createTime: {
            label: this.$t('lang.rms.fed.updateDate'),
            default: '',
            valueFormat: 'yyyy-MM-dd',
            type: 'daterange',
            tag: 'date-picker',
            'range-separator': '-',
            'start-placeholder': this.$t('lang.rms.fed.startDate'),
            'end-placeholder': this.$t('lang.rms.fed.endData')
          }
        },
        rules: [],
        operations: [
          {
            label: this.$t('lang.rms.fed.query'),
            handler: 'on-query',
            type: 'primary'
          },
          {
            label: this.$t('lang.rms.fed.reset'),
            handler: 'on-reset',
            type: 'default'
          }
        ]
      },
      statusList: {
        0: this.$t('lang.rms.fed.upgradeIsNowBeingPrepared'),
        1: this.$t('lang.rms.fed.underUpgrade'),
        2: this.$t('lang.rms.fed.upgradeIsDone'),
        3: this.$t('lang.rms.fed.upgradesAnomaly')
      },
      robotStatus: [],
      diseaseList: []
    }
  },
  activated() {
    this.params = {
      pageSize: 10,
      currentPage: 1
    }
    this.getSoftwareList(this.params)
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.formConfig = {
          attrs: {
            labelWidth: '80px',
            inline: true
          },
          configs: {
            robotId: {
              label: this.$t('lang.rms.fed.robot') + 'ID',
              default: '',
              tag: 'input',
              placeholder: this.$t('lang.rms.fed.pleaseEnterTheRobotID')
            },
            status: {
              label: this.$t('lang.rms.fed.state'),
              tag: 'select',
              default: '',
              placeholder: '',
              options: [
                {
                  value: 0,
                  label: this.$t('lang.rms.fed.upgradeIsNowBeingPrepared')
                },
                {
                  value: 1,
                  label: this.$t('lang.rms.fed.underUpgrade')
                },
                {
                  value: 2,
                  label: this.$t('lang.rms.fed.upgradeIsDone')
                },
                {
                  value: 3,
                  label: this.$t('lang.rms.fed.upgradesAnomaly')
                }
              ]
            },
            version: {
              label: this.$t('lang.rms.fed.softwareOfRobot') + 'ID',
              default: '',
              tag: 'input',
              placeholder: this.$t('lang.rms.fed.pleaseEnter')
            },
            createTime: {
              label: this.$t('lang.rms.fed.updateDate'),
              default: '',
              valueFormat: 'yyyy-MM-dd',
              type: 'daterange',
              tag: 'date-picker',
              'range-separator': '-',
              'start-placeholder': this.$t('lang.rms.fed.startDate'),
              'end-placeholder': this.$t('lang.rms.fed.endData')
            }
          },
          rules: [],
          operations: [
            {
              label: this.$t('lang.rms.fed.query'),
              handler: 'on-query',
              type: 'primary'
            },
            {
              label: this.$t('lang.rms.fed.reset'),
              handler: 'on-reset',
              type: 'default'
            }
          ]
        }
      }, 200)
    })
  },
  methods: {
    // 格式化时间
    setTime(value) {
      return $utils.Tools.formatDate(value, 'yyyy-MM-dd hh:mm:ss')
    },
    // 列表接口请求
    getSoftwareList(params) {
      $req.get('/athena/robot/software/center', {
        ...params,
        params: true
      }).then(res => {
        const data = res.data
        this.robotStatus = data.recordList
        this.page.currentPage = data.currentPage || 1
        this.recordCount = data.pageCount
      })
    },
    // 分页
    currentPageChange(page) {
      this.page.currentPage = page
      this.params = Object.assign(this.params, this.page)
      this.getSoftwareList(this.params)
    },
    // 改变每页显示条数
    pageSizeChange(size) {
      this.page.pageSize = size
      this.params = Object.assign(this.params, this.page)
      this.getSoftwareList(this.params)
    },
    // 机器人id模糊搜索
    // fetchDate() {
    //     $req.get("/athena/robotStatus/queryRobotIds", {
    //         info: this.form.robotId,
    //         robotStates: [],
    //         params: true
    //     }).then(res => {
    //         this.diseaseList = [];
    //         if (res.code === 0) {
    //             for (const i of res.data) {
    //                 const item = {
    //                     value: String(i)
    //                 };
    //                 this.diseaseList.push(item);
    //             }
    //         }
    //     });
    // },
    onQuery(val) {
      this.page.currentPage = 1
      const { createTime, ...parmas } = val

      this.params = Object.assign(parmas, this.page)

      this.params.from = createTime[0]
      this.params.to = createTime[1]
      this.getSoftwareList(this.params)
    },
    onReset(val) {
      this.page.currentPage = 1
      this.params = Object.assign({}, val, this.page)
      this.getSoftwareList(this.params)
    }
  }
}
</script>

<style lang="less" scoped>
.form-content {
  .form-flex-wrap {
    display: flex;
    flex-wrap: wrap;
  }
  .el-form-item {
    margin-right: 8px;
  }
  .btn-content {
    display: flex;
    align-items: flex-end;
    margin-bottom: 18px;
  }
}
</style>
