<template>
  <el-dialog
    :title="$t('lang.rms.web.station.queueNumber')"
    :visible.sync="dialogVisible"
    :append-to-body="true"
    :before-close="close"
    :close-on-click-modal="false"
    width="50%"
  >
    <div class="total-number">
      <span class="total-number-title">{{
        $t("lang.rms.web.station.unionStationQueueNumber")
      }}</span
      >:
      <span class="total-number-num">
        {{ maxRobotQueueSize }}
      </span>
    </div>
    <div class="park-queue-title">{{ $t("lang.rms.web.station.parkQueueNumber") }}</div>

    <customize-table :table-config="tableConfig">
      <template #maxQueueNumber="{ column }">
        <el-table-column v-bind="column">
          <template #default="{ row }">
            <el-input-number
              v-model="row.maxQueueSize"
              :min="0"
              :max="99"
              size="small"
              controls-position="right"
              @change="handleChange"
            />
          </template>
        </el-table-column>
      </template>
      <template #isWorkingShow="{ column }">
        <el-table-column v-bind="column">
          <template #default="{ row }">
            {{ row.isWorking ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}
          </template>
        </el-table-column>
      </template>
    </customize-table>

    <span slot="footer" class="dialog-footer">
      <el-button @click="close"> {{ $t("lang.rms.fed.cancel") }}</el-button>
      <el-button type="primary" @click="save">{{ $t("lang.rms.fed.save") }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import CustomizeTable from "../../../../../components/customize-table";

export default {
  name: "RobotEventTypeEditDialog",
  components: {
    CustomizeTable,
  },
  data() {
    return {
      dialogVisible: false,
      maxRobotQueueSize: 0,
      pageType: "add",
      rowData: null,
      stationPoints: [],
    };
  },
  computed: {
    tableConfig() {
      return {
        attrs: {
          // height: '100%'
        },
        isPagination: false,
        columns: [
          {
            label: this.$t("lang.rms.web.station.parkId"),
            prop: "parkId",
            width: "100px",
          },
          {
            label: this.$t("lang.rms.fed.cellCode"),
            "show-overflow-tooltip": true,
            prop: "cellCode",
          },
          {
            label: this.$t("lang.venus.web.common.isEnable"),
            slot: "isWorkingShow",
          },
          {
            label: this.$t("lang.rms.fed.maxQueueNumber"),
            "show-overflow-tooltip": true,
            slot: "maxQueueNumber",
          },
        ],
        data: this.stationPoints,
      };
    },
  },
  methods: {
    open(data) {
      this.rowData = JSON.parse(JSON.stringify(data));
      this.stationPoints = this.rowData.stationPoints || []; //JSON.parse(JSON.stringify(data.codeList));
      this.maxRobotQueueSize = data.maxRobotQueueSize;
      this.dialogVisible = true;
    },

    close() {
      this.dialogVisible = false;
    },

    handleChange(value) {
      console.log(value, this.stationPoints);
      const arr = JSON.parse(JSON.stringify(this.stationPoints));
      let count = 0;
      for (let i = 0; i < arr.length; i++) {
        const element = arr[i];
        count += element.maxQueueSize;
      }
      this.maxRobotQueueSize = count;
    },
    // 保存
    save() {
      const arr = JSON.parse(JSON.stringify(this.stationPoints));
      let reqParkList = [];
      for (let i = 0; i < arr.length; i++) {
        const element = arr[i];
        const reqEle = {
          parkId: element.parkId,
          maxRobotQueueSize: element.maxQueueSize,
        };
        reqParkList.push(reqEle);
      }
      const data = {
        mapId: this.rowData.mapId,
        stationId: this.rowData.stationId,
        maxRobotQueueSize: this.maxRobotQueueSize,
        stationPointRobotQueueSizes: reqParkList,
      };

      console.log(data);

      $req.post("/athena/station/updateMaxRobotQueueSize", data).then(res => {
        const { code } = res || {};
        if (code === 0) {
          this.$message({
            showClose: false,
            message: this.$t("lang.rms.api.result.ok"),
            type: "success",
          });
          this.dialogVisible = false;
          this.$emit("updateMainList");
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-form-item {
  width: 22%;
}

/deep/ .el-form-item__label {
  padding-bottom: 0;
  font-size: 13px;
  font-weight: 800;
}

/deep/ .el-dialog__body {
  border-top: none;
}

.total-number {
  font-size: 14px;
  padding-left: 10px;
  border-bottom: 1px solid #eee;

  padding-bottom: 15px;
  .total-number-title {
    padding-right: 5px;
    display: inline-block;
  }
  .total-number-num {
    padding-left: 20px;
    display: inline;
  }
}
.park-queue-title {
  padding-left: 10px;
  font-size: 14px;
  border-bottom: 1px solid #eee;
  height: 40px;
  line-height: 40px;
}
</style>
