<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2022-01-05 20:04:30
 * @Description:
-->
<template>
  <el-form
    ref="searchForm"
    :inline="true"
    class="demo-form-inline"
    label-position="top"
    :model="searchForm"
  >
    <el-form-item :label="$t('lang.rms.api.result.warehouse.ontologyName')" prop="name">
      <el-input
        v-model.trim="searchForm.name"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterOntologyName')"
      />
    </el-form-item>
    <!-- <el-form-item :label="$t('lang.rms.api.result.warehouse.bodyNo')" prop="chassisCode">
      <el-input
        v-model.trim="searchForm.code"
        :placeholder="$t('lang.rms.api.result.warehouse.bodyNo')"
      />
    </el-form-item> -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.series')" prop="series">
      <el-select
        v-model="searchForm.series"
        :placeholder="$t('lang.rms.api.result.warehouse.series')"
      >
        <el-option
          v-for="item in (dictionary || {}).ROBOT_SERIES || []"
          :key="item.fieldCode"
          :label="item.fieldCode"
          :value="item.fieldValue"
        />
      </el-select>
    </el-form-item>
    <!-- <el-form-item
      :label="$t('lang.rms.api.result.warehouse.navigationMode')"
      prop="navigationModes"
    >
      <el-select
        v-model="searchForm.navigationModes"
        multiple
        :placeholder="$t('lang.rms.api.result.warehouse.navigationMode')"
      >
        <el-option :label="$t('lang.rms.fed.navigation.slam')" value="0" />
        <el-option :label="$t('lang.rms.fed.navigation.qr')" value="1" />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.batteryType')" prop="batteryType">
      <el-select
        v-model="searchForm.batteryType"
        :placeholder="$t('lang.rms.api.result.warehouse.batteryType')"
      >
        <el-option :label="$t('lang.rms.api.result.warehouse.all')" value="all" />
        <el-option :label="$t('lang.rms.fed.battery.lto')" value="0" />
        <el-option :label="$t('lang.rms.fed.battery.lfpo')" value="1" />
        <el-option :label="$t('lang.rms.api.result.warehouse.LithiumTernary')" value="2" />
      </el-select>
    </el-form-item> -->
    <el-form-item class="align-bottom">
      <el-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.query") }}</el-button>
      <el-button type="primary" @click="resetForm">{{ $t("lang.rms.fed.reset") }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import { mapState } from "vuex";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    // 这里存放数据
    return {
      searchForm: {
        name: "",
        // chassisCode: "",
        series: "",
      },
    };
  },
  // 监听属性 类似于data概念
  computed: {
    ...mapState(["dictionary"]),
  },
  // 监控data中的数据变化
  watch: {},
  mounted() {
    this.onSubmit();
  },
  // 方法集合
  methods: {
    onSubmit(reset) {
      const subData = Object.assign({}, this.searchForm);
      this.$emit("onsubmit", { ...subData, isReset: reset === true });
    },
    resetForm() {
      this.$refs["searchForm"].resetFields();
      this.onSubmit(true);
    },
  },
};
</script>
<style lang="scss" scoped>
.align-bottom {
  vertical-align: bottom;
}
</style>
