<template>
  <geek-main-structure class="software-control">
    <div class="btnwarp">
      <el-button
        v-if="checkPermission('RobotSoftwareManagerAdd', 'natural')"
        type="primary"
        @click="dialogFormVisible = true"
      >
        {{ $t("lang.rms.fed.newlyAdded") }}
      </el-button>
      <el-button
        v-if="checkPermission('RobotSoftwareManagerDelete', 'natural')"
        type="danger"
        @click="handleDeleteRobotControl"
      >
        {{ $t("lang.rms.fed.delete") }}
      </el-button>
    </div>
    <el-table
      :data="robotStatus"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
      />
      <el-table-column
        prop="version"
        :label="$t('lang.rms.fed.edition')"
      />
      <el-table-column
        prop="type"
        :formatter="statusformatter"
        :label="$t('lang.rms.fed.type')"
      />
      <el-table-column
        prop="oldName"
        :label="$t('lang.rms.fed.fileName')"
      />
      <el-table-column
        min-width="400px"
        prop="path"
        :label="$t('lang.rms.fed.storagePath')"
      />
      <el-table-column
        prop="createTime"
        min-width="110px"
        :formatter="timeformatter"
        :label="$t('lang.rms.fed.creationTime')"
      />
      <el-table-column
        prop="updateTime"
        min-width="110px"
        :formatter="timeformatter"
        :label="$t('lang.rms.fed.updateTime')"
      />
      <el-table-column
        fixed="right"
        :label="$t('lang.rms.fed.operation')"
        width="180"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handleEdit(scope.$index, scope.row)"
          >
            {{ $t('lang.rms.fed.edit') }}
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="handleActive(scope.$index, scope.row)"
          >
            {{ $t('lang.rms.fed.application') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="page.currentPage"
      :page-sizes="pageSizes"
      :page-size="page.pageSize"
      layout="sizes, prev, pager, next"
      :total="recordCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <el-dialog
      :title="$t('lang.rms.fed.addSoftware')"
      :visible.sync="dialogFormVisible"
      width="640px"
      :before-close="handleClose"
    >
      <el-form
        ref="addForm"
        :model="addForm"
        label-width="80px"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.edition')" prop="version">
              <!-- :placeholder="x1" -->
              <el-input v-model="addForm.version" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.type')" prop="type">
              <el-select v-model="addForm.type">
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('lang.rms.fed.describe')" prop="descr">
              <!-- :placeholder="x2" -->
              <el-input v-model="addForm.descr" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-upload
        ref="upload"
        class="upload"
        action="/athena/robot/software/upload"
        :limit="1"
        :file-list="fileList"
        :on-success="handleUploadSuccess"
        :auto-upload="false"
      >
        <el-button
          slot="trigger"
          size="small"
          type="primary"
        >
          {{ $t("lang.rms.fed.chooseAFile") }}
        </el-button>
        <el-button
          style="margin-left: 10px"
          size="small"
          type="success"
          @click="submitUpload"
        >
          {{ $t("lang.rms.fed.upload") }}
        </el-button>
        <div slot="tip" class="el-upload__tip">{{ $t("lang.rms.fed.uploadOneFile") }}</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="clearAddForm">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button
          type="primary"
          :disabled="addForm.version == '' || !uploadInfo.newName"
          @click="handleSubmitRobotControl"
        >
          {{ $t("lang.rms.fed.confirm") }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="
        $t('lang.rms.fed.chargerId') + ',' + $t('lang.rms.fed.multipleNumbersAreSeparatedByComma')
      "
      :visible.sync="dialogRobotVisible"
    >
      <el-form :model="formRobot">
        <el-form-item>
          <el-input v-model="formRobot.ids" autocomplete="off" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogRobotVisible = false">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button type="primary" @click="handleSubmitActive">
          {{ $t("lang.rms.fed.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </geek-main-structure>
</template>

<script>
export default {
  name: 'SoftwareControl',
  data() {
    return {
      formRobot: {
        softwareId: '',
        ids: undefined
      },
      addForm: {
        version: '',
        type: 0,
        descr: ''
      },
      isEdit: false,
      fileList: [],
      dialogFormVisible: false,
      dialogRobotVisible: false,
      page: {
        pageSize: 10,
        currentPage: 1
      },
      tableSelection: [],
      uploadInfo: {},
      recordCount: 0,
      pageSizes: [10, 20, 50, 100],
      statusList: [
        {
          value: 0,
          label: this.$t('lang.rms.fed.masterControl')
        }
      ],
      robotStatus: [],
      robotHeaderList: [
        {
          prop: 'version',
          label: this.$t('lang.rms.fed.edition')
        },
        {
          prop: 'type',
          label: this.$t('lang.rms.fed.type')
        },
        {
          prop: 'oldName',
          label: this.$t('lang.rms.fed.fileName')
        },
        {
          prop: 'path',
          label: this.$t('lang.rms.fed.storagePath')
        },
        {
          prop: 'createTime',
          label: this.$t('lang.rms.fed.creationTime')
        },
        {
          prop: 'updateTime',
          label: this.$t('lang.rms.fed.updateTime')
        }
      ]
    }
  },
  mounted() {
    this.handleSubmit()
  },
  methods: {
    handleEdit(index, data) {
      this.dialogFormVisible = true
      this.isEdit = true
      this.addForm = {
        version: data.version || '',
        type: data.type || 0,
        descr: data.descr || ''
      }
      this.uploadInfo = data
      this.fileList = [
        {
          name: data.newName || '',
          url: data.path
        }
      ]
    },
    handleActive(index, data) {
      this.dialogRobotVisible = true
      this.formRobot.softwareId = data.id
    },
    handleSubmitActive() {
      if (!this.formRobot.ids) {
        this.$message.error(this.$t('lang.rms.fed.pleaseEnterChargerId'))
        return false
      }
      this.dialogRobotVisible = false
      $req
        .post('/athena/robot/software/active', {
          softwareId: this.formRobot.softwareId,
          chargeIds: this.formRobot.ids.split(',')
        })
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection.map(item => {
        return item.id
      })
    },
    handleDeleteRobotControl() {
      if (this.tableSelection.length > 0) {
        $req.post('/athena/robot/software/delete', this.tableSelection).then(res => {
          this.handleSubmit()
        })
      } else {
        this.$message.info(this.$t('lang.rms.fed.pleaseSelectOperateVersion'))
      }
    },
    handleSubmitRobotControl() {
      const self = this
      // this.$store.dispatch('setLoading', true)
      this.uploadInfo.category = 1
      const data = Object.assign({}, this.uploadInfo, this.addForm)
      if (this.isEdit) {
        $req.post('/athena/robot/software/update', data).then(res => {
          self.handleSubmit()
          self.clearAddForm()
          // self.$store.dispatch('setLoading', false)
          self.isEdit = false
        })
      } else {
        $req.post('/athena/robot/software/add', data).then(res => {
          self.handleSubmit()
          self.clearAddForm()
          // self.$store.dispatch('setLoading', false)
        })
      }
    },
    clearAddForm() {
      this.dialogFormVisible = false
      this.$refs.upload.clearFiles()
      this.addForm = {
        version: '',
        type: 0,
        descr: ''
      }

      this.uploadInfo = {}
      this.$refs['addForm'].resetFields()
    },
    submitUpload() {
      this.$refs.upload.submit()
    },
    handleUploadSuccess(res) {
      this.uploadInfo = res.data
    },
    statusformatter(row, column) {
      return this.statusList[row[column['property']]].label
    },
    timeformatter(row, column) {
      return new Date(row[column['property']])?.toLocaleString()
    },
    handleSizeChange(size) {
      this.page.pageSize = size
      this.handleSubmit()
    },
    handleCurrentChange(page) {
      this.page.currentPage = page
      this.handleSubmit()
    },
    handleSubmit() {
      const params = Object.assign({}, this.page)
      params.category = 1
      $req.get('/athena/robot/software/findAll', params).then(res => {
        const data = res.data
        this.page.currentPage = data.currentPage || 1
        this.recordCount = data.recordCount
        this.robotStatus = res.data.recordList
      })
    },
    handleClose() {
      this.clearAddForm()
    }
  }
}
</script>

<style scoped>
.el-select {
  width: 100%;
}
.btnwarp {
  padding: 0 0 30px;
}
.el-pagination {
  text-align: right;
  padding: 20px 0 0;
}
.upload {
  width: 300px;
}
</style>
