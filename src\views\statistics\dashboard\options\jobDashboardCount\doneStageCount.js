export default {
  x: 12,
  y: 0,
  width: 12,
  height: 5,

  // 地图数据信息
  chart: {
    type: 'cred', // 图表类型

    // 地图数据来源
    request: {
      url: '/athena/stats/query/job/dashboard/count/',  // 请求接口
      filters: ['date', 'showCount', 'showCountGroup'], // 筛选条件
      defFilters: {  // 默认筛选条件
        date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
        showCount: true,
        showCountGroup: true
      },
      timer: 5000,  // 轮询时间, 如果是0, 则不轮询
    },
    // 地图数据处理
    dataHandler: {  // 数据处理
      handler: 'getCountMap',
      params: {
        type: 'cred',
        paramsName: 'doneStageCount',
        title: '上游下发的任务'
      },
    },
  }
}