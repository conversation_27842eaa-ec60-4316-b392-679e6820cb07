<template>
  <!-- 用户列表 -->
  <geek-main-structure class="user-list">
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    </div>
    <div class="table-content">
      <div class="handle-btn">
        <el-button type="primary" size="small" @click="onCreate">
          {{ $t("lang.rms.fed.add") }}
        </el-button>
      </div>
      <customize-table :table-config="tableConfig">
        <template #status="{ column }">
          <el-table-column v-bind="column" :label="$t(column.label)">
            <template slot-scope="scope">
              <span>{{ scope.row.status | statusFilter(i18) }}</span>
            </template>
          </el-table-column>
        </template>
        <template #createTime="{ column }">
          <el-table-column v-bind="column" :label="$t(column.label)">
            <template slot-scope="scope">
              <span>{{ setTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
        </template>
        <template #operations="{ column }">
          <el-table-column v-bind="column" :label="$t(column.label)">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="openEditPop(scope.row)">
                {{ $t("lang.rms.fed.buttonEdit") }}
              </el-button>
              <el-popover placement="top" trigger="click" popper-class="change-pwd" width="260">
                <div>
                  <el-row class="rowSpace">
                    <el-col :span="8" class="vText">{{ $t("lang.rms.fed.newPassword") }}:</el-col>
                    <el-col :span="16" class="pass-col">
                      <!-- <el-input v-model="newPassword" type="password" class="customInput" /> -->
                      <input
                        v-model="newPassword"
                        :type="passwordType"
                        :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
                        autocomplete="on"
                        class="pass-input"
                      />
                      <span class="pwd-eye" :class="{ show: passwordEyeShow }" @click="changePwdShow" />
                    </el-col>
                  </el-row>
                  <el-row class="cSpace">
                    <el-button size="mini" @click="resetPassWord">
                      {{ $t("lang.rms.fed.reset") }}
                    </el-button>
                    <el-button type="primary" size="mini" @click="changePassWord(scope.row)">
                      {{ $t("lang.rms.fed.save") }}
                    </el-button>
                  </el-row>
                </div>
                <el-button slot="reference" type="text" @click="popoverVisible = !popoverVisible">
                  {{ $t("lang.rms.fed.changePassword") }}
                </el-button>
              </el-popover>
              <el-button
                v-if="checkPermission('AuthQueryUser')"
                type="text"
                size="small"
                :disabled="[1].includes(scope.row.userId)"
                @click="onOffHandler(scope.row)"
              >
                <p>{{ scope.row.status | statusButtonFilter(i18) }}</p>
              </el-button>
            </template>
          </el-table-column>
        </template>
      </customize-table>
      <div style="text-align: right">
        <el-pagination
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          :total="page.total"
          :page-sizes="[10, 25, 50, 100]"
          layout="sizes, prev, pager, next"
          background
          class="geek-pagination"
          @size-change="pageSizeChange"
          @current-change="currentPageChange"
        />
      </div>
    </div>
    <create-user-dialog v-if="createUserFlag" @close="onClose" />
    <edit-user-dialog v-if="editUserFlag" ref="editUser" @close="onClose" />
  </geek-main-structure>
</template>

<script>
import md5 from "js-md5";
import CustomizeTable from "../../components/customize-table";
import CreateUserDialog from "./components/dialog/create-user-dialog";
import EditUserDialog from "./components/dialog/edit-user-dialog";
export default {
  name: "UserList",
  components: {
    CustomizeTable,
    CreateUserDialog,
    EditUserDialog,
  },
  filters: {
    statusFilter: function (value, that) {
      let statusText = "";
      if (value === 0) {
        statusText = that("lang.rms.fed.textProhibit");
      } else {
        statusText = that("lang.rms.fed.textEnable");
      }
      return statusText;
    },
    statusButtonFilter: function (value, that) {
      let buttonStatus = "";
      if (value === 1) {
        buttonStatus = that("lang.rms.fed.textProhibit");
      } else {
        buttonStatus = that("lang.rms.fed.textEnable");
      }
      return buttonStatus;
    },
  },
  data() {
    return {
      passwordType: "password",
      passwordEyeShow: false,
      params: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      newPassword: "",
      // recordCount: 0,
      popoverVisible: false,
      createUserFlag: false,
      editUserFlag: false,
      list: [],
      formConfig: {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          userNameAsFuzz: {
            label: "lang.rms.fed.userName",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          realNameAsFuzz: {
            label: "lang.rms.fed.inputFullName",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          phoneAsFuzz: {
            label: "lang.rms.fed.inputTelephone",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          status: {
            label: "lang.rms.fed.inputState",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "1",
                label: "lang.rms.fed.textEnable",
              },
              {
                value: "0",
                label: "lang.rms.fed.buttonProhibit",
              },
            ],
          },
        },
        rules: [],
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableConfig: {
        attrs: {
          // height: '100%'
        },
        isPagination: false,
        columns: [
          {
            label: "lang.rms.fed.number",
            width: "60px",
            "show-overflow-tooltip": true,
            // align: 'center',
            prop: "userId",
          },
          {
            label: "lang.rms.fed.inputUserName",
            "show-overflow-tooltip": true,
            // align: 'center',
            prop: "userName",
          },
          {
            label: "lang.rms.fed.inputFullName",
            "show-overflow-tooltip": true,
            // align: 'center',
            prop: "realName",
          },
          {
            label: "lang.rms.fed.inputTelephone",
            "show-overflow-tooltip": true,
            // align: 'center',
            prop: "phone",
          },
          {
            label: "lang.rms.fed.inputState",
            "show-overflow-tooltip": true,
            // align: 'center',
            slot: "status",
            key: "status",
          },
          {
            label: "lang.rms.fed.role",
            "show-overflow-tooltip": true,
            // align: 'center',
            prop: "roleNames",
          },
          {
            label: "lang.rms.fed.listFounder",
            "show-overflow-tooltip": true,
            // align: 'center',
            prop: "creator",
          },
          {
            label: "lang.rms.fed.listCreationTime",
            "show-overflow-tooltip": true,
            // align: 'center',
            slot: "createTime",
            key: "createTime",
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "140px",
            // align: 'center',
            slot: "operations",
          },
        ],
        data: [],
        pageSize: 10,
        total: 0,
      },
    };
  },
  created() {
    this.params = {
      pageSize: this.page.pageSize,
      currentPage: this.page.currentPage,
    };
    this.getUserList(this.params);
  },
  methods: {
    // 格式化时间
    setTime(value) {
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss");
    },
    i18(title) {
      const hasKey = this.$t(title);
      const translatedTitle = this.$t(title);
      if (hasKey) {
        return translatedTitle;
      }
      return title;
    },
    // 列表接口请求
    getUserList(params) {
      console.log(params);
      $req
        .get("/athena/api/coreresource/auth/user/pageQuery/v1", {
          ...params,
          params: true,
        })
        .then(res => {
          const data = res.data;
          this.page.currentPage = data.currentPage || 1;
          this.tableConfig.data = data.recordList;
          // this.totalPage = data.recordCount //小艾同学为什么这么写那
          this.page.total = data.recordCount;
          this.tableConfig.total = data.recordCount;
        });
    },
    // 新增
    onCreate() {
      this.createUserFlag = true;
    },
    onClose(type) {
      this.createUserFlag = false;
      this.editUserFlag = false;
      if (type === "success") {
        this.getUserList(this.params);
      }
    },
    // 分页
    currentPageChange(page) {
      this.page.currentPage = page;
      this.params = Object.assign(this.params, this.page);
      this.getUserList(this.params);
    },
    // 改变每页显示条数
    pageSizeChange(size) {
      this.page.pageSize = size;
      this.params = Object.assign(this.params, this.page);
      this.getUserList(this.params);
    },
    onQuery(val) {
      this.page.currentPage = 1;
      this.params = Object.assign(val, this.page);
      this.params.createTime = this.params.createTime && this.params.createTime.join("-");
      this.getUserList(this.params);
    },
    onReset(val) {
      this.page.currentPage = 1;
      this.params = Object.assign({}, val, this.page);
      this.getUserList(this.params);
    },
    openEditPop(userInfo) {
      this.editUserFlag = true;
      this.$nextTick(() => {
        this.$refs.editUser.setUserInfo(userInfo);
      });
    },
    onOffHandler(userInfo) {
      // 传入当前状态，请求相反状态
      // Status为用户状态，0表示禁用，1表示启用
      if (userInfo.status === 0) {
        $req
          .post("/athena/api/coreresource/auth/user/enableUser/v1", {
            userId: userInfo.userId,
            status: 1,
          })
          .then(res => {
            if (res.code === 0) {
              this.getUserList(this.params);
            }
          });
      } else {
        $req
          .post("/athena/api/coreresource/auth/user/disableUser/v1", {
            userId: userInfo.userId,
            status: 0,
          })
          .then(res => {
            if (res.code === 0) {
              this.getUserList(this.params);
            }
          });
      }
    },
    changePassWord(userInfo) {
      // 密码8-20位、数字、字母、特殊符号组成
      let passReg = /^(?=.*?[a-zA-Z])(?=.*?\d)(?=.*?[*?!&￥$%^#,./@";:><\[\]}{\-=+_\\|》《。，、？’‘“”~ ]).{8,20}$/
      if (!passReg.test(this.newPassword)) {
        this.$message.error(this.$t('lang.auth.PwdMgrAPI.item0002'))
        return
      }
      $req
        .post("/athena/api/coreresource/auth/user/updateUserPassword/v1", {
          user: {
            userName: userInfo.userName,
            userId: userInfo.userId,
            // password: this.newPassword,
            password: md5(this.newPassword + userInfo.userName),
            expireDate: "2030-01-01 00:00:00",
            status: userInfo.status,
          },
        })
        .then(res => {
          if (res.code === 0) {
            this.popoverVisible = false;
            this.$message({
              message: res.msg,
              type: "success",
            });
          }
        });
    },
    resetPassWord() {
      this.newPassword = "";
    },
    changePwdShow() {
      const ps = !this.passwordEyeShow;
      this.passwordEyeShow = ps;
      this.passwordType = ps ? "text" : "password";
    },
  },
};
</script>

<style lang="less" scoped>
.user-list {
  .table-content {
    padding-top: 10px;
  }
  .handle-btn {
    padding-bottom: 20px;
    text-align: right;

    .el-button {
      width: 120px;
    }
  }
}
.change-pwd {
  .rowSpace {
    margin: 0px 0px 16px 0px;
  }
  .vText {
    line-height: 32px;
  }
  .cSpace {
    text-align: center;
  }
  .customInput {
    width: 150px;
    height: 20px;
  }
}
.pass-input {
  width: 100%;
}
.pass-col {
  position: relative;
}
.pwd-eye {
  display: inline-block;
  position: absolute;
  right: 0;
  top: 0;
  width: 28px;
  height: 28px;
  z-index: 1;
  cursor: pointer;
  background-image: url(~@imgs/login/icon-eye-invisible.png);
  background-size: 18px;
  background-repeat: no-repeat;
  background-position: 50% 50%;

  &.show {
    background-image: url(~@imgs/login/icon-eye.png);
  }
}
</style>
