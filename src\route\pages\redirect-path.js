/* ! <AUTHOR> at 2021/01 */

/** 需要重定向的链接 供别的系统引用 */
export default [
  /** ************** 地图监控 这个一样的不需要做redirect ****************/
  /** ************** 对接模型 560产品干掉了 链接到新的容器模型管理上 ****************/
  {
    path: "/warehouseManage/dockModel",
    redirect: "/warehouseManage/containerModelManage",
    meta: { notMenu: true },
  },
  /** ************** 地图管理/地图信息 ****************/
  {
    path: "/monitor/mapManage",
    redirect: "/warehouseManage/mapManage",
    meta: { notMenu: true },
  },
  /** ************** 机器人信息 ****************/
  {
    path: "/monitor/robotMonitor",
    redirect: "/warehouseManage/robotMonitor",
    meta: { notMenu: true, noPermissionGuest: true },
  },
  /** ************** 机器人软件管理 ****************/
  {
    path: "/rysMonitor/softwareControl",
    redirect: "/operationManage/softwareControl",
    meta: { notMenu: true },
  },
  /** ************** 机器人升级日志 ****************/
  {
    path: "/rysMonitor/versionControl",
    redirect: "/operationManage/versionControl",
    meta: { notMenu: true },
  },
  /** ************** 控制策略 ****************/
  // {
  //   path: "/warehouseManage/control",
  //   redirect: "/systemConfig/control",
  //   meta: { notMenu: true, noPermissionGuest: true },
  // },
  /** ************** 货架操作 ****************/
  {
    path: "/warehouseManage/shelfmanage",
    redirect: "/operationManage/shelfManage",
    meta: { notMenu: true, noPermissionGuest: true },
  },
  /** ************** 异常处理 ****************/
  {
    path: "/warehouseManage/exceptionHand",
    redirect: "/operationManage/exceptionHand",
    meta: { notMenu: true },
  },
  /** ************** 机器人型号管理 这个560没有了用机器人信息代替 ****************/
  {
    path: "/warehouseManage/robotListMange",
    redirect: "/warehouseManage/robotManage",
    meta: { notMenu: true, noPermissionGuest: true },
  },
  /** ************** 参数配置页面 ****************/
  {
    path: "/warehouseManage/parameterConfigOuter",
    redirect: "/systemConfig/parameterConfigOuter",
    meta: { notMenu: true, noPermissionGuest: true },
  },
];
