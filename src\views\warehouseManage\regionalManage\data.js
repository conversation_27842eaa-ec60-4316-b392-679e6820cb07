import mapVerify from "element-geek/utils/formValidateV2";

const getValue = value => (value ? String(value) : String(value) === "0" ? "0" : "");

export const getSearchFormData = (that, options) => [
  // 区域id
  {
    name: "logicId",
    value: "",
    span: 6,
    component: "elInput",
    label: that.$t("lang.rms.fed.mapArea.id"),
    rules: mapVerify(["positiveInt"]),
  },
];

// searchTable列表
export const getSearchTableItem = (that, options) => [
  // 区域id
  {
    prop: "logicId",
    label: that.$t("lang.rms.fed.mapArea.id"),
  },
  // 分配车辆比例
  {
    prop: "robotDemand",
    label: that.$t("lang.rms.fed.carProportion"),
  },
  // {
  //   prop: "forkliftActualNumber",
  //   label: that.$t("lang.rms.fed.area.robot.forkliftActualNumber"),
  // },
  // {
  //   prop: "forkliftNeedNumber",
  //   label: that.$t("lang.rms.fed.area.robot.forkliftNeedNumber"),
  // },
  // {
  //   prop: "forkliftTaskNumber",
  //   label: that.$t("lang.rms.fed.area.robot.forkliftTaskNumber"),
  // },
  // 区域内车辆实际数量
  {
    prop: "robotActualNumber",
    label: that.$t("lang.rms.fed.carActualMount"),
  },
  // 预计分配车辆数量
  {
    prop: "robotNeedNumber",
    label: that.$t("lang.rms.fed.carMount"),
  },
  // 当前并发任务数
  {
    prop: "robotTaskNumber",
    label: that.$t("lang.rms.fed.currentTaskMount"),
  },
];

// 编辑框form表单
export const getEditFormData = (that, row) => {
  const formList = [
    // 机器人比例
    {
      name: "robotDemand",
      value: getValue(row.robotDemand),
      span: 12,
      component: "elInput",
      label: that.$t("lang.rms.fed.robotProportion"),
      rules: mapVerify(["required", "inputLen15", "positiveInt"]),
    },
  ];

  return formList;
};
