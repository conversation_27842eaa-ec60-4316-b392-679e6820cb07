<template>
  <div class="geek-form-table-con">
    <div class="form-content">
      <el-form ref="form" :model="form" label-width="80px" label-position="top">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-form-item prop="stationId" required :label="$t('lang.rms.fed.selectWorkstation')">
              <el-select v-model="form.stationId" :placeholder="$t('lang.rms.fed.pleaseChoose')">
                <el-option
                  v-for="item in stationList"
                  :key="item.id"
                  :label="item.id + $t('lang.rms.fed.workstation')"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item prop="shelfCode" required :label="$t('lang.rms.fed.shelfNumber')">
              <el-input v-model="form.shelfCode" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item prop="shelfSide" :label="$t('lang.rms.fed.selectShelfOrientation')">
              <el-select v-model="form.shelfSide" :placeholder="$t('lang.rms.fed.pleaseChoose')">
                <el-option
                  v-for="(item, index) in shelfSideList"
                  :key="index"
                  :disabled="checkoutSideDisabled(item.value)"
                  :label="$t(item.label)"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <span v-if="checkPermission('ShelfOperationPickUpShelf', 'natural')" class="btn-warp">
              <el-button type="primary" @click="getShelf">
                {{ $t("lang.rms.fed.pick-upShelves") }}
              </el-button>
            </span>
            <el-button @click="reset">
              {{ $t("lang.rms.fed.reset") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="table-content">
      <div
        slot="header"
        class="clearfix"
        style="border-bottom: 1px solid #eee; padding-bottom: 12px"
      >
        <span>{{ $t("lang.rms.fed.shelfInTheTask") }}</span>
      </div>
      <el-table :data="taskList" style="width: 100%">
        <el-table-column type="index" />
        <el-table-column prop="shelfCode" :label="$t('lang.rms.fed.shelfNumber')" />
        <el-table-column prop="robotId" :label="$t('lang.rms.fed.robot')" />
        <el-table-column prop="shelfState" :label="$t('lang.rms.fed.state')">
          <template slot-scope="scope">{{ $t(shelfStatus[scope.row.shelfState]) }}</template>
        </el-table-column>
        <el-table-column prop="shelfSide" :label="$t('lang.rms.fed.turnTheShelf')">
          <template v-if="scope.row.shelfState === 'READY'" slot-scope="scope">
            <el-button
              v-for="side in shelfSideList"
              :key="side.value"
              :type="scope.row.shelfSide.slice(0, 1) === side.value ? 'success' : 'primary'"
              :disabled="scope.row.shelfSide === side.value || checkoutSideDisabled(side.value)"
              size="small"
              @click="turnShelf(scope.row, side.value)"
            >
              <span>{{ $t(side.label) }}</span>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="$t('lang.rms.fed.operation')">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.shelfState === 'READY'"
              type="primary"
              size="small"
              @click="sendShelf(scope.row)"
            >
              {{ $t("lang.rms.fed.deliveryShelf") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { Message } from "element-ui";
export default {
  props: ["activeName"],
  data() {
    return {
      form: {
        stationId: "",
        shelfCode: "",
        shelfSide: "",
      },
      shelfSideList: [
        {
          value: "F",
          label: "lang.rms.fed.fSide",
        },
        {
          value: "B",
          label: "lang.rms.fed.bSide",
        },
        {
          value: "L",
          label: "lang.rms.fed.lSide",
        },
        {
          value: "R",
          label: "lang.rms.fed.rSide",
        },
      ],
      shelfStatus: {
        READY: this.$t("lang.rms.fed.inTheOperation"),
        QUEUING: this.$t("lang.rms.fed.queueing"),
        FETCHING: this.$t("lang.rms.fed.beingCarried"),
        GO_TURN: this.$t("lang.rms.fed.turningSide"),
        GO_RETURN: this.$t("lang.rms.fed.return"),
      },
      stationList: [],
      taskList: [],
      tasksetInterval: null,
    };
  },
  watch: {
    activeName(name) {
      if (name === "shelfOperations") {
        this.getStations();
        this.getTaskByStation();
        this.tasksetInterval = setInterval(() => {
          this.getTaskByStation();
        }, 2000);
      } else {
        clearInterval(this.tasksetInterval);
      }
    },
  },

  methods: {
    getStations() {
      $req.get("/athena/station/findAll").then(res => {
        this.stationList = res.data;
      });
    },
    getTaskByStation() {
      $req.get("/athena/task/findByStation", { stationId: this.form.stationId }).then(res => {
        this.taskList = res.data;
      });
    },
    getShelf() {
      if (!this.form.shelfCode || !this.form.stationId) return;
      $req.get("/athena/shelf/exists", { shelfCode: this.form.shelfCode }).then(res => {
        if (res.data.status === 2) {
          Message({
            message: this.$t(res.data.descr),
            type: "error",
            duration: 5 * 1000,
          });
        } else {
          $req
            .get("/athena/task/deleverToStation/fetch", {
              stationId: this.form.stationId,
              shelfCode: this.form.shelfCode,
              shelfSide: this.form.shelfSide,
            })
            .then(res => {
              this.getTaskByStation();
            });
        }
      });
    },
    reset() {
      this.$refs["form"].resetFields();
    },
    turnShelf(value, side) {
      $req
        .post(
          "/athena/task/deleverToStation/turn",
          $utils.Tools.getParams({
            taskId: value.taskId,
            stationId: value.stationId,
            shelfSide: side,
          }),
          { headers: { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" } },
        )
        .then(res => {
          this.getTaskByStation();
        });
    },
    sendShelf(value) {
      $req
        .get("/athena/task/deleverToStation/return", {
          taskId: value.taskId,
          stationId: value.stationId,
        })
        .then(res => {
          this.getTaskByStation();
        });
    },
    checkoutSideDisabled(value) {
      // 2021.1.15 去掉权限控制
      // if (this.$rmsConfig.openPermissionSystem) {
      //   // 有权限不控制
      //   return false;
      // } else {
      const $rmsConfig = $utils.Data.getRMSConfig();
      return $rmsConfig.disableShelfSide && $rmsConfig.disableShelfSide.includes(value);
      // }
    },
  },
};
</script>
<style lang="less" scoped>
.form-content {
  .el-select {
    width: 100%;
  }
}
</style>
