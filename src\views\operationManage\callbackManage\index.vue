<template>
  <geek-main-structure>
    <el-tabs v-model="activeName">
      <!-- 回调消息管理 -->
      <el-tab-pane
        v-if="tabNamePerssion['msgConfiguration']"
        :label="$t('lang.rms.fed.CallbackMsgConfiguration')"
        name="msgConfiguration"
      >
        <MsgConfiguration />
      </el-tab-pane>
      <!-- 回调通道管理 -->
      <el-tab-pane
        v-if="tabNamePerssion['channelConfiguration']"
        :label="$t('lang.rms.fed.CallbackMsgChannelConfiguration')"
        name="channelConfiguration"
      >
        <ChannelConfiguration />
      </el-tab-pane>
    </el-tabs>
  </geek-main-structure>
</template>

<script>
import MsgConfiguration from "./callbackMsgConfigure";
import ChannelConfiguration from "./callbackMsgChannel";
export default {
  components: { MsgConfiguration, ChannelConfiguration },
  data() {
    return {
      tabNamePerssion: {
        msgConfiguration: this.getTabPermission(
          "TabCallbackManagementMsgPage",
          "callbackManagement",
        ),
        channelConfiguration: this.getTabPermission(
          "TabCallbackManagementChannelPage",
          "callbackManagement",
        ),
      },
      defaultActive: "msgConfiguration",
    };
  },

  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
  },
  mounted() {
    this.defaultActive = $utils.Tools.getRouteQueryTabName(
      this.defaultActive,
      this.tabNamePerssion,
    );
  },
};
</script>
<style lang="scss" scoped></style>
