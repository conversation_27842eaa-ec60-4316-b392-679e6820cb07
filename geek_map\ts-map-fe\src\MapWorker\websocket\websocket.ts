/* ! <AUTHOR> at 2022/07/15 */
import md5 from "js-md5";
import SocketMsg from "./msg-socket";

class MonitorWebSocket {
  wsUrl: string;
  socketMsg: SocketMsg = new SocketMsg();
  private reqQueue: Array<{ msgName: string; resolve: any; reject: any }> = [];
  private ws: WebSocket;
  private wsStatus: number = 0; // websocket状态， 0 idle, 1 creating, 2 open, 3 closed
  private md5Switch: boolean = false;
  private isDestroy: boolean = false;
  private responseFlag: boolean = false;
  private initMsgParams: MWorker.wsParams = null;
  private restartTimer: any = null;
  private responseTimer: any = null;

  private wsCallback: MWorker.wsCallback;
  private _onceInit: boolean = true;
  private _isFloorChanging: boolean = false; // 地图是否正在初始化
  constructor(url: string) {
    const { info } = console;
    info("[websocket] >>>>> url::", url);

    this.wsUrl = url;
    this.md5Switch = this._getMd5Switch();
  }

  init(recreate: boolean = false): void {
    let wsStatus = this.wsStatus;
    if (wsStatus !== 0 && wsStatus !== 3) return;
    console.log("[websocket] ----- socket create: " + Date.now());
    const websocket = new WebSocket(this.wsUrl);
    websocket.onopen = () => {
      console.log("[websocket] ----- socket connected: " + Date.now());
      this.wsStatus = 2;
      if (!this.initMsgParams) return;
      if (recreate || this._onceInit) {
        this.reqInit(this.initMsgParams);
      }
    };
    websocket.onmessage = this.onmessage.bind(this);
    websocket.onclose = this.onclose.bind(this);
    websocket.onerror = (e: any) => {
      const { error } = console;
      error("[websocket] >>>>> error::", e);
    };

    this.ws = websocket;
  }

  reqInit(params: MWorker.wsParams): void {
    const msgType = params.msgType;
    if (msgType !== "MapInitRequestMsg") return;
    this.reqQueue = [];
    this.initMsgParams = params;
    if (!this.ws || this.wsStatus !== 2) return;
    this._isFloorChanging = true;
    this._onceInit = false;
    const body = params.body || {};
    const bodyFormat = this.formatWsData(msgType, body);
    this.ws.send(bodyFormat);
    this._heartbeatDetect();
  }
  reqUpdate(params: MWorker.wsParams): void {
    if (this._isFloorChanging || !this.ws) return;
    const msgType = params.msgType;
    if (msgType !== "MapUpdateRequestMsg") return;

    const body = params.body || {};
    const bodyFormat = this.formatWsData(msgType, body);
    this.ws.send(bodyFormat);
    this._heartbeatDetect();
  }

  reqSingleQuery(params: { layer: string; code: number | string }): void {
    this.socketMsg.reqSingleQuery(params, this.wsCallback);
  }
  stopQuery(): void {
    this.socketMsg.stopQuery();
  }

  reqSocket(params: MWorker.wsParams) {
    if (this._isFloorChanging || !this.ws) {
      return new Promise(async (resolve, reject) => {
        resolve({ code: -66666, msg: "连接中断或正在请求地图" });
      });
    } else {
      const msgType = params.msgType;
      const body = params.body || {};
      const bodyFormat = this.formatWsData(msgType, body);
      return new Promise(async (resolve, reject) => {
        this.reqQueue.push({ msgName: msgType.slice(0, -10), resolve, reject });
        this.ws.send(bodyFormat);
        this._heartbeatDetect();
      });
    }
  }

  onCallBack(cb: MWorker.wsCallback) {
    this.wsCallback = cb;
  }

  destroy() {
    this.isDestroy = true;
    if (this.restartTimer) {
      clearTimeout(this.restartTimer);
      this.restartTimer = null;
    }
    if (this.responseTimer) {
      clearTimeout(this.responseTimer);
      this.responseTimer = null;
    }
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    if (this.socketMsg) {
      this.socketMsg.destroy();
      this.socketMsg = null;
    }

    this.wsStatus = 0;
    this.initMsgParams = null;
    this.responseFlag = false;
    this.reqQueue = null;

    this.wsCallback = null;
    this._onceInit = true;
    this._isFloorChanging = false;
  }

  private onmessage(e: any): void {
    this._heartbeatResponse();
    let data = JSON.parse(e.data);
    if (!data) return;
    const msgType = data.msgType;
    if (!msgType) return;

    const res = data?.response || {};
    const { header, body } = res;

    const cb = this.wsCallback;
    if (msgType === "MapInitResponseMsg") {
      this.reqQueue = [];
      if (this._isResponseError(msgType, header)) return;
      if (body?.retry) {
        const { warn } = console;
        warn("数据retry了，请检查数据传输");
        if (this.initMsgParams) this.reqInit(this.initMsgParams);
        return;
      }
      const initType = header?.ext || "";
      const isInitFinish = initType.indexOf("MapFinished") !== -1;
      if (isInitFinish) this._isFloorChanging = false;
      this.socketMsg.MapInitResponseMsg(header, body, cb);
      return;
    }

    if (this._isFloorChanging) return;
    let promiseIndex: number, promise: any;
    switch (msgType) {
      case "MapUpdateResponseMsg": // 地图数据更新
        if (this._isResponseError(msgType, header)) return;
        if (body?.retry) {
          const { warn } = console;
          warn("数据retry了，请检查数据传输");
          if (this.initMsgParams) this.reqInit(this.initMsgParams);
          return;
        }
        this.socketMsg.MapUpdateResponseMsg(body, cb);
        break;
      case "EventMessageCountResponseMsg": // 消息、待办
        if (this._isResponseError(msgType, header)) return;
        const { backLogCount, notificationCount } = body || {};
        cb && cb("wsMessageCount", { backLogCount, notificationCount });
        break;
      case "EventMessageCardResponseMsg": // 消息提醒（卡片事件推送、左下角卡片） 语音播报需要的 逐条消息
         console.log("🎯 找到了！EventMessageCardResponseMsg:", body);
        if (this._isResponseError(msgType, header)) return;
        cb && cb("wsMessageCard", body);
        break;
      case "EventMessageDeviceResponseMsg": // 推送设备异常信息 数组格式[] 左上角透明层内容
        if (this._isResponseError(msgType, header)) return;
        cb && cb("wsMessageDevice", body.result || []);
        break;
      case "EventMessageRobotResponseMsg": // 异常机器人详细信息
        if (this._isResponseError(msgType, header)) return;
        cb && cb("wsMessageRobot", body);
        break;
      case "TaskStatResponseMsg": // 机器人任务相关
        if (this._isResponseError(msgType, header)) return;
        cb && cb("wsMessageTask", body);
        break;
      case "QueryInstructionResponseMsg": // 查询指令
      case "FastSearchResponseMsg": // 快速查找
        promiseIndex = this.reqQueue.findIndex(queue => msgType.indexOf(queue.msgName) !== -1);
        if (promiseIndex === -1) return;
        promise = this.reqQueue.splice(promiseIndex, 1);
        if (!promise[0]) return;
        promise[0].resolve({ msgType, body: { code: header.code, data: body || null } });
        break;
      case "WarehouseInstructionResponseMsg": // 仓库指令响应
      case "ForcedTaskResponseMsg":
      case "RobotInstructionResponseMsg": // 机器人指令响应
      case "ShelfInstructionResponseMsg": // 货架指令响应
      case "BoxInstructionResponseMsg": // 货箱（固定货架）指令响应
      case "ChargerInstructionResponseMsg": // 充电站
      case "RealtimeObstacleInstructionResponseMsg": // 外部障碍物
        promiseIndex = this.reqQueue.findIndex(queue => msgType.indexOf(queue.msgName) !== -1);
        if (promiseIndex === -1) return;
        promise = this.reqQueue.splice(promiseIndex, 1);
        if (!promise[0]) return;
        promise[0].resolve({ msgType, body: header });
        break;
    }
  }

  private onclose(e: any): void {
    const { warn } = console;
    warn("[websocket] >>>>> closed::", e);
    this.wsStatus = 3;
    if (this.ws) {
      this.ws.onopen = undefined;
      this.ws.onmessage = undefined;
      this.ws.onclose = undefined;
    }
    this.ws = null;
    this.reqQueue = [];

    if (this.isDestroy) return;
    if (this.restartTimer) {
      clearTimeout(this.restartTimer);
      this.restartTimer = null;
    }
    this.restartTimer = setTimeout(() => {
      this.init(true);
    }, 3000);
  }

  private formatWsData(wsMsg: string, body: { [propName: string | number]: any }): any {
    let data = {
      id: "GEEK",
      msgType: wsMsg,
      request: {
        header: {
          requestId: this._guid(),
          clientCode: "GEEK",
          warehouseCode: "GEEK",
        },
        body,
      },
    };

    let md5Switch = this.md5Switch,
      result = JSON.stringify(data);
    if (md5Switch) return `${result}@@@${md5(result + "signature#gk")}`;
    else return result;
  }

  private _heartbeatDetect() {
    this.responseFlag = false;

    if (this.isDestroy) return;
    if (this.responseTimer) {
      clearTimeout(this.responseTimer);
      this.responseTimer = null;
    }
    this.responseTimer = setTimeout(() => {
      if (this.responseFlag) return;
      if (this.ws) this.ws.close();
    }, 57 * 1000);
  }

  private _heartbeatResponse() {
    this.responseFlag = true;
    if (this.responseTimer) {
      clearTimeout(this.responseTimer);
      this.responseTimer = null;
    }
  }

  private _isResponseError(msgType: string, header: any) {
    // 响应报错
    if (!header || header.code !== 0) {
      const { error } = console;
      error(`[websocket] >>>>> ${msgType}::`, header);
      return true;
    }
    return false;
  }

  private _guid() {
    return (
      (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) +
      (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) +
      "-" +
      (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) +
      "-" +
      (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) +
      "-" +
      (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) +
      "-" +
      (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) +
      (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1) +
      (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
    );
  }

  private _getMd5Switch() {
    let config: any = {};
    let RMSConfig = localStorage.getItem("Geek_RMSConfig");
    if (RMSConfig) config = JSON.parse(RMSConfig);

    return config.md5Switch || false;
  }
}

export default MonitorWebSocket;
