<template>
  <div class="dashboard-chart" :style="curChartStyle">
    <template v-if="chartType === 'cred'">
      <number-item :option="chartData" />
    </template>
    <template v-else-if="chartType === 'table'">
      <table-item :option="chartData" />
    </template>
    <template v-else-if="chartType === 'annular'">
      <annular-item :option="chartData" />
    </template>
    <template v-else>
      <ChartItem :option="chartData" :isFilter="option.isChartFilter" @filter="filter" />
    </template>
  </div>
</template>

<script>
import VueDragResize from 'vue-drag-resize'
import dashboardHandler, { requestdata } from '../chart/dashboard'
import NumberItem from "../components/common/number-item.vue";
import ChartItem from "../components/common/chart-item.vue";
import TableItem from "../components/common/table-item.vue";
import AnnularItem from "../components/common/chart-annular.vue"


export default {
  name: "dashboard",
  props: {
    option: {
      type: Object,
      default: () => { },
    },
    gridWidth: {
      type: Number,
      default: 100,
    },
    disabled: {
      type: Boolean,
      default: true,
    },
  },
  components: { VueDragResize, NumberItem, ChartItem, TableItem, AnnularItem },
  data() {
    return {
      isDataLoad: true,
      chartData: null,
      pollingTimer: null,
      curFilterData: {},
    };
  },
  mounted() { },
  computed: {
    chartOption() {
      return this.option?.chart || {};
    },
    chartType() {
      return this.chartOption?.type || '';
    },
    curChartStyle() {
      const { width, height } = this.option;
      const { gridWidth } = this;
      let curWidth = width;
      let curHeight = height;
      if (typeof width === 'number') curWidth = `${parseInt(width * gridWidth) - 1}px`;
      if (typeof height === 'number') curHeight = `${parseInt(height * gridWidth)}px`;
      return { width: curWidth, height: curHeight }
    },
  },
  watch: {
    option: {
      handler() {
        this.gridX = this.option.x;
        this.gridY = this.option.y;
        this.gridW = this.option.width;
        this.gridH = this.option.height;
      },
      immediate: true,
      deep: true,
    },
  },
  async created() {
    const { data } = await this.requestChartData();
    this.chartData = await this.reLoadChart(data);
  },

  mounted() {
    const timer = this.chartOption.request.timer;
    if (timer) {
      this.pollingTimer = setInterval(async () => {
        const { data } = await this.requestChartData();
        this.chartData = await this.reLoadChart(data);
      }, timer);
    }
  },

  destroyed() {
    clearInterval(this.pollingTimer);
  },
  methods: {
    async filter(filterData) {
      this.curFilterData = filterData;
      const { data } = await this.requestChartData();
      this.chartData = await this.reLoadChart(data);
    },

    async requestChartData(params = {}) {
      const { chartOption, curFilterData } = this;
      const { request } = chartOption;
      const { defFilters } = request;

      const queryFilter = { ...(defFilters || {}), ...params, ...curFilterData };
      // 这里先根据filters组装查询条件
      const requestData = {};
      (request.filters || []).forEach(filterKey => {
        const value = queryFilter[filterKey];
        if (!value) return;

        switch (filterKey) {
          case 'date':
            requestData[filterKey] = $utils.Tools.formatDate(value, "yyyy-MM-dd");
            break;
          default:
            requestData[filterKey] = value;
            break;
        }
      })

      if (request.url) {
        return await requestdata(request.url, requestData);
      }

      return { data: null }
    },

    async reLoadChart(data) {
      const { chartOption } = this;
      const dataHandler = chartOption.dataHandler;
      return await dashboardHandler[dataHandler.handler](data, dataHandler.params || {});
    }
  },
};
</script>

<style lang="less" scoped>
.dashboard-chart {
  display: inline-block;
  overflow: hidden;
  padding: 5px;
}
</style>

<style lang="less">
.dashboard-chart {
  .content-container {
    overflow: hidden;
    padding: 5px;
  }

  &.vdr.active:before {
    outline: none;
  }
}
</style>
