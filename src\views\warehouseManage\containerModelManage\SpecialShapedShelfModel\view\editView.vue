<template>
    <div class="editView">
      <div class="title">{{ title }}</div>
      <div class="content">
        <Model auxiliary :shelf-data="shelfData" />
        <Config @change="changeModel" />
      </div>
    </div>
  </template>
  
  <script>
  import { mapState } from "vuex";
  import Model from "../Components/model/index.vue";
  import Config from "../Components/config/index.vue";
  
  export default {
    components: {
      Model,
      Config,
    },
    data() {
      return {
        shelfData: {},
      };
    },
    computed: {
      ...mapState("containerModal", [
        "specialShapedShelfModelViewType",
        "specialShapedShelfModelViewData",
      ]),
  
      title() {
        switch (this.specialShapedShelfModelViewType) {
          case "add":
            return this.$t('lang.rms.fed.add') + this.$t("lang.rms.fed.shapedShelfModel");
          case "view":
            return this.$t("lang.rms.fed.buttonView") + this.$t("lang.rms.fed.shapedShelfModel");
          case "edit":
            return this.$t("lang.rms.fed.edit") + this.$t("lang.rms.fed.shapedShelfModel");
          default:
            break;
        }
        return "";
      },
    },
    watch: {},
    mounted() {},
    methods: {
      changeModel(modelData) {
        this.shelfData = modelData;
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .editView {
    height: 100%;
    display: flex;
    flex-direction: column;
    .title {
      text-indent: 20px;
      font-size: 16px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }
  
    .content {
      flex: 1;
      display: flex;
      flex-direction: row;
      overflow: hidden;
    }
  }
  </style>
  