<template>
  <div class="geek-form-table-con">
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    </div>
    <div class="table-content">
      <el-table :data="tableData" style="width: 100%">
        <!-- 货架编码 -->
        <el-table-column prop="shelfCode" :label="$t('lang.rms.fed.shelfNumber')" width="100" />
        <!-- 区域ID -->
        <el-table-column prop="logicId" :label="$t('lang.rms.fed.region')" width="70" />
        <!-- 锁定 -->
        <el-table-column prop="state" :label="$t('lang.rms.fed.lock')" width="70" />
        <!-- 长-宽-高 -->
        <el-table-column prop="length" :label="$t('lang.rms.fed.textLength')" width="60" />
        <el-table-column prop="width" :label="$t('lang.rms.fed.textWidth')" width="60" />
        <el-table-column prop="height" :label="$t('lang.rms.fed.textHeight')" width="60" />
        <!-- 货架位置 -->
        <el-table-column prop="location" :label="$t('lang.rms.fed.shelfPosition')" width="180" />
        <!-- 摆放位置 老家位置-->
        <el-table-column prop="placement" :label="$t('lang.rms.fed.placementCode')" width="180" />
        <!-- 货架角度 -->
        <el-table-column prop="angle" :label="$t('lang.rms.fed.angle')" width="80" />
        <!-- 货架热度 -->
        <el-table-column prop="shelfHeat" :label="$t('lang.rms.fed.shelfHeatDisplay')" width="70" />
        <!-- 当前单元格 -->
        <el-table-column prop="locationCellCode" :label="$t('lang.rms.fed.cell')" />
        <!-- 老家单元格 -->
        <el-table-column prop="placementCellCode" :label="$t('lang.rms.fed.placementCell')" />
      </el-table>
      <div style="text-align: right">
        <geek-pagination
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          :total-page="pageCount"
          @currentPageChange="currentPageChange"
          @pageSizeChange="pageSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["activeName"],
  data() {
    return {
      // 搜索条件
      form: {
        shelfCode: "",
        logicId: "",
        state: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          shelfCode: {
            label: "lang.rms.fed.shelfNumber",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          logicId: {
            label: "lang.rms.fed.region",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterAnNumber",
          },
          state: {
            label: "lang.rms.fed.lock",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "",
                label: "lang.rms.fed.wholeStatus",
              },
              {
                value: 0,
                label: "lang.rms.fed.no",
              },
              {
                value: 1,
                label: "lang.rms.fed.yes",
              },
            ],
          },
        },
        rules: [],
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      page: {
        currentPage: 1,
        pageSize: 10,
      },
      value: null,
      tableData: [], // 设置table的数据
      pageCount: 0, // 当前数据总数
    };
  },
  watch: {
    activeName: {
      handler(name) {
        if (name === "shelfQuery") {
          this.getTableList();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 分页
    currentPageChange(val) {
      this.page.currentPage = val;
      this.getTableList();
    },
    // 改变每页显示条数
    pageSizeChange(val) {
      this.page.pageSize = val;
      this.getTableList();
    },
    onQuery(val) {
      this.page.currentPage = 1;
      this.form = Object.assign(this.form, val);
      if (!this.form.state) this.form.state = "";
      if (this.form.logicId && !Number(this.form.logicId) && Number(this.form.logicId) !== 0) {
        this.form.logicId = "";
        this.$message.error(this.$t("lang.rms.fed.pleaseEnterAnNumber"));
        return;
      }
      this.getTableList();
    },
    onReset(val) {
      this.page.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const params = this.form;
      params.currentPage = this.page.currentPage;
      params.pageSize = this.page.pageSize;
      $req.get("/athena/shelf/findShelf", params).then(res => {
        let result = res.data;
        if (result != null) {
          let list = result.recordList;
          list.forEach(item => {
            if (item.state === "NORMAL") {
              item.state = this.$t("lang.rms.fed.no");
            } else {
              item.state = this.$t("lang.rms.fed.yes");
            }
          });
          this.tableData = list;
          this.page.currentPage = result.currentPage || 1;
          this.pageCount = result.pageCount;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
