<template>
  <div class="create-user-dialog">
    <!-- 新增用户 -->
    <el-dialog
      :title="$t('lang.rms.fed.add')"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      @closed="onClose"
    >
      <el-form ref="rulesForm" :model="formObj" :rules="rulesObj">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.inputUserName')" prop="userName">
              <el-input
                v-model="formObj.userName"
                :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.inputFullName')" prop="realName">
              <el-input
                v-model="formObj.realName"
                :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.inputTelephone')" prop="phone">
              <el-input
                v-model="formObj.phone"
                :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.password')" prop="password">
              <!-- <el-input
                v-model="formObj.password"
                type="password"
                :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
              /> -->
              <div class="pass-col">
                <input
                  v-model="formObj.password"
                  :type="passwordType"
                  :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
                  autocomplete="on"
                  class="pass-input"
                />
                <span class="pwd-eye" :class="{ show: passwordEyeShow }" @click="changePwdShow" />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.role')" prop="roleIds">
              <el-select
                v-model="formObj.roleIds"
                :placeholder="$t('lang.rms.fed.pleaseChoose')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in roleList"
                  :key="item.roleId"
                  :label="item.name"
                  :value="item.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button type="primary" :loading="loading" @click="onSubmit">{{
          $t("lang.rms.fed.save")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import md5 from "js-md5";
export default {
  data() {
    return {
      passwordType: "password",
      passwordEyeShow: false,
      formObj: {
        userName: "", // 用户名
        password: "", // 密码
        realName: "", // 姓名
        phone: "", // 电话
        roleIds: "", // 角色
      },
      loading: false,
      roleList: [],
      
      rulesObj: {
        userName: [{ required: true, message: this.$t('lang.rms.fed.canNotBeEmpty'), trigger: "change" }],
        password: [
          // 密码8-20位、数字、字母、特殊符号组成
          { required: true, message: this.$t('lang.rms.fed.canNotBeEmpty'), trigger: "change" },
          { pattern: /^(?=.*?[a-zA-Z])(?=.*?\d)(?=.*?[*?!&￥$%^#,./@";:><\[\]}{\-=+_\\|》《。，、？’‘“”~ ]).{8,20}$/, message: this.$t('lang.auth.PwdMgrAPI.item0002'), trigger: "blur" }
        ],
        realName: [{ required: true, message: this.$t('lang.rms.fed.canNotBeEmpty'), trigger: "change" }],
        // phone: [
        //   { required: true, message: '不能为空, 请输入', trigger: 'change' }
        // ],
        roleIds: [{ required: true, message: this.$t('lang.rms.fed.canNotBeEmpty'), trigger: "change" }],
      },
      dialogVisible: true,
    };
  },
  created() {
    this.getRoleList();
  },
  methods: {
    getRoleList() {
      $req.get("/athena/api/coreresource/auth/role/pageQuery/v1").then(res => {
        if (res.code === 0) {
          this.roleList = res.data.recordList;
        }
      });
    },
    onClose() {
      this.$emit("close");
    },
    onSubmit() {
      this.$refs.rulesForm.validate(valid => {
        if (valid) {
          const params = {
            user: {
              userName: this.formObj.userName,
              // password: this.formObj.password,
              password: md5(this.formObj.password + this.formObj.userName),
              realName: this.formObj.realName,
              phone: this.formObj.phone,
            },
            roleIds: [Number(this.formObj.roleIds)],
          };
          this.loading = true;
          $req
            .post("/athena/api/coreresource/auth/user/addUser/v1", params)
            .then(res => {
              if (res.code === 0) {
                this.loading = false;
                let msg = $utils.Tools.transMsgLang(res.msg);
                this.$message.success(msg);
                this.$emit("close", "success");
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    changePwdShow() {
      const ps = !this.passwordEyeShow;
      this.passwordEyeShow = ps;
      this.passwordType = ps ? "text" : "password";
    },
  },
};
</script>

<style lang="less">
.create-user-dialog {
  .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 520px;
    max-height: calc(100% - 30px);
  }
  .el-dialog__body {
    padding: 15px 20px;
    flex: 1;
    overflow: auto;
  }
  .el-input {
    width: 220px;
  }
  .pass-input {
    width: 220px;
  }
  .pass-col {
    position: relative;
  }
  .pwd-eye {
    display: inline-block;
    position: absolute;
    left: 192px;
    bottom: 3px;
    width: 28px;
    height: 28px;
    z-index: 1;
    cursor: pointer;
    background-image: url(~@imgs/login/icon-eye-invisible.png);
    background-size: 18px;
    background-repeat: no-repeat;
    background-position: 50% 50%;

    &.show {
      background-image: url(~@imgs/login/icon-eye.png);
    }
  }
}
</style>
