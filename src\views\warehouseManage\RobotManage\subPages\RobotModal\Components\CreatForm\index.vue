<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2021-12-27 14:30:15
 * @Description:
-->
<template>
  <el-form ref="createForm" :model="modelData" class="creat-form" label-position="right" label-width="135px"
    :disabled="!editProp" :rules="rules">
    <div class="form-group-title division">
      {{ $t("auth.rms.robotType.button.robotModelInformation") }}:
    </div>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotModel')" prop="product">
      <el-input v-model="modelData.product" :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterRobotModel')" />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotModelAlias')" prop="displayName">
      <el-input v-model="modelData.displayName"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterRobotModeAlias')" />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.ontologyModel')" prop="chassisModelId">
      <el-select v-model="modelData.chassisModelId" :placeholder="$t('lang.rms.fed.choose')">
        <el-option v-for="item in actualList" :key="item.id" :label="item.label" :value="item.chassisModelId" />
      </el-select>
    </el-form-item>

    <!-- 机构模型 -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.mechanismModel')" prop="mechanismModelId">
      <el-select v-model="modelData.mechanismModelId" :placeholder="$t('lang.rms.fed.choose')">
        <el-option v-for="item in mechanismList" :key="item.id" :label="item.label" :value="item.mechanismId" />
      </el-select>
    </el-form-item>

    <!-- 业务特征模型 -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.businessModel')" prop="businessModelId">
      <el-select v-model="modelData.businessModelId" :placeholder="$t('lang.rms.fed.choose')">
        <el-option v-for="item in businessList" :key="item.id" :label="item.label" :value="item.businessModelId" />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.protocolModel')" prop="protocolModelId">
      <el-select v-model="modelData.protocolModelId" :placeholder="$t('lang.rms.fed.pleaseChooseWithRobot')">
        <el-option v-for="item in protocolList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import robotManageRequest from "@/api/robotManage";
import { cloneDeep } from "lodash";

export default {
  name: "ModelCreateForm",
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    slopProps: {
      type: Object,
      default() {
        return {};
      },
    },
    editProp: {
      type: Boolean,
      default() {
        return false;
      },
    },
    show: {
      // 当前弹窗显示状态
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    // 这里存放数据
    return {
      modelData: { ...this.slopProps },
      rules: {
        product: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterRobotModeAlias"),
            trigger: "blur",
          }, // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        // displayName: [
        //   {
        //     required: true,
        //     message: this.$t("lang.rms.api.result.warehouse.pleaseEnterRobotModeAlias"),
        //     trigger: "blur",
        //   }, // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        // ],
        chassisModelId: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.chooseRobotModel"),
            trigger: "blur",
          },
        ],
        // mechanismModelId: [
        //   {
        //     required: true,
        //     message: this.$t("lang.rms.api.result.warehouse.chooseRobotMechanismModel"),
        //     trigger: "blur",
        //   },
        // ],
        // businessModelId: [
        //   {
        //     required: true,
        //     message: this.$t("lang.rms.api.result.warehouse.selectRobotBusinessModel"),
        //     trigger: "blur",
        //   },
        // ],
        // protocolModelId: [
        //   {
        //     required: true,
        //     message: this.$t("lang.rms.api.result.warehouse.selectRobotProtocolModel"),
        //     trigger: "blur",
        //   },
        // ],
      },
      actualList: [],
      mechanismList: [],
      businessList: [],
      protocolList: [],
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {
    slopProps: {
      handler(newObj) {
        if (newObj.product) {
          this.getActualList();
          this.getMechanismList();
          this.getBusinessList();
          this.getProtocolList();
        }
        this.modelData = { ...newObj };
      },
      immediate: true,
    },
    show: {
      handler(nV) {
        if (nV) {
          this.getActualList();
          this.getMechanismList();
          this.getBusinessList();
          this.getProtocolList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  // 方法集合
  methods: {
    labelFun(type, arr) {
      arr.forEach(item => {
        if (type === 0) {
          item.label = item.chassisCode
            ? item.chassisCode + "(" + item.name + ")"
            : "(" + item.name + ")";
          this.actualList = cloneDeep(arr);
        } else if (type === 1) {
          item.label = item.mechanismCode
            ? item.mechanismCode + "(" + item.name + ")"
            : "(" + item.name + ")";
          this.mechanismList = cloneDeep(arr);

          // 这里当创建机器人的时候没有数据,则提供一个默认值
          if (!this.modelData.mechanismModelId) {
            let id = ''
            if (item.name === '500TurnLift') {
              id = item.id
            }
            this.modelData.mechanismModelId = id;
          }
        } else if (type === 2) {
          item.label = item.businessCode
            ? item.businessCode + "(" + item.name + ")"
            : "(" + item.name + ")";
          this.businessList = cloneDeep(arr);

          // 这里当创建机器人的时候没有数据,则提供一个默认值
          if (!this.modelData.businessModelId) {
            let id = ''
            if (item.name === 'P500L') {
              id = item.id
            }
            this.modelData.businessModelId = id;
          }

        } else {
          item.label = item.protocolCode
            ? item.protocolCode + "(" + item.name + ")"
            : "(" + item.name + ")";
          this.protocolList = cloneDeep(arr);
        }
      });
    },
    getActualList() {
      robotManageRequest
        .ontologyModelSearch(
          {
            name: "",
            chassisCode: "",
            series: "",
            batteryType: "",
            navigationModes: [],
            currentPage: 1,
            pageSize: 1000,
          }
        )
        .then(({ data }) => {
          this.labelFun(0, data.recordList);
        });
    },
    getMechanismList() {
      robotManageRequest
        .getMechanismPageList(
          {
            name: "",
            mechanismCode: "",
            controlAbilities: [],
            actionAbilities: [],
          },
          {
            currentPage: 1,
            pageSize: 1000,
          },
        )
        .then(({ data }) => {
          this.labelFun(1, data.recordList);
        });
    },
    getBusinessList() {
      robotManageRequest
        .getBusinessPageList(
          {
            businessCode: "",
            sizeTypes: [],
          },
          {
            currentPage: 1,
            pageSize: 1000,
          },
        )
        .then(({ data }) => {
          this.labelFun(2, data.recordList);
        });
    },
    async getProtocolList() {
      const { code, data } = await $req.get("/athena/robot/manage/findRobotProtocols");
      this.protocolList = code ? [] : data;
    },
    getFormValues() {
      const $form = this.$refs["createForm"];
      return $form.validate();
    },
    resetFormValues() {
      this.$refs["createForm"].resetFields();
    },
  },
};
</script>
<style lang="scss" scoped>
.creat-form .el-input,
.creat-form .el-select {
  width: 100%;
}

.form-group-title {
  font-size: 16px;
  font-weight: bold;
  margin: 3px auto;
  text-align: left;
}

.division {
  margin-bottom: 20px;
}
</style>
