<template>
  <el-dialog
    :title="$t('lang.rms.fed.edit')"
    :visible.sync="dialogRobotParamRecordEditVisible"
    :before-close="closeDialog"
    center
    :append-to-body="true"
    width="30%"
  >
    <el-form label-position="top" label-width="80px" :model="itemData" class="padding_20">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('lang.rms.fed.nameOfParameter')">
            <el-input v-model="itemData.paramCode" class="w_100x" disabled="disabled" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('lang.rms.fed.robotId')">
            <el-input v-if="isEdit" v-model="itemData.robotId" class="w_100x" disabled="disabled" />
            <el-input v-if="!isEdit" v-model="itemData.robotId" class="w_100x" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('lang.rms.fed.robotParamValue')">
            <el-input v-model="itemData.paramValue" class="w_100x" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">{{ $t("lang.rms.fed.save") }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: "RobotParamRecordEdit",
  props: {
    dialogRobotParamRecordEditVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
    isEdit: {
      type: Boolean,
      default() {
        return false;
      },
    },
    itemData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  created() {},
  computed: {},
  methods: {
    // 关闭弹出
    closeDialog() {
      this.$emit("update:dialogRobotParamRecordEditVisible", false);
    },
    save() {
      this.itemData.paramConfigId = this.itemData.configId;
      this.$emit("save", this.itemData);
      this.closeDialog();
    },
  },
  watch: {},
  components: {},
};
</script>
<style scoped>
.w_100x {
  width: 100%;
}

.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  height: 28px;
  line-height: 28px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  line-height: 28px;
  width: 90px;
  vertical-align: bottom;
}
</style>
