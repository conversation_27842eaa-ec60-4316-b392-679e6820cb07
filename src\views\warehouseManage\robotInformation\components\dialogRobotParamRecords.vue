<template>
  <el-dialog
    :title="$t('lang.rms.fed.robotParamRecord')"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :append-to-body="true"
    width="70%"
    @close="onClose"
  >
    <div class="app-container">
      <el-card class="mt-20">
        <!-- 列表信息 -->
        <el-table :data="tableData" style="width: 100%">
          <el-table-column
            :label="$t('lang.rms.fed.lineNumber')"
            :formatter="formatIndex"
            width="50"
          />
          <!--机器人ID-->
          <el-table-column prop="robotId" :label="$t('lang.rms.fed.robotId')" />
          <!-- 参数值 -->
          <el-table-column prop="paramConfigJson" :label="$t('lang.rms.fed.robotParamValue')" />
          <!--创建时间-->
          <el-table-column
            prop="createTime"
            :label="$t('lang.rms.fed.createTime')"
            :formatter="timeformatter"
          />
          <!--创建人-->
          <el-table-column prop="createUser" :label="$t('lang.rms.fed.createUser')" />
          <!--更新时间-->
          <!-- <el-table-column
            prop="updateTime"
            :label="$t('lang.rms.fed.updateTime')"
            :formatter="timeformatter"
          /> -->
          <!--更新人-->
          <!-- <el-table-column prop="updateUser" :label="$t('lang.rms.fed.updateUser')" /> -->
        </el-table>
        <div style="text-align: right">
          <geek-pagination
            :current-page="page.currentPage"
            :page-size="page.pageSize"
            :total-page="pageCount"
            @currentPageChange="currentPageChange"
            @pageSizeChange="pageSizeChange"
          />
        </div>
      </el-card>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: "RobotParamRecords",
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      pageCount: 0,
      page: {
        currentPage: 1,
        pageSize: 10,
      },
      mainTableRowData: null,
    };
  },
  methods: {
    open(data) {
      this.mainTableRowData = data;
      this.dialogVisible = true;
      this.getTableList();
    },
    onClose() {
      this.mainTableRowData = null;
      this.dialogVisible = false;
      this.$emit("updateMainTale");
    },

    formatIndex(row, column, cellValue, index) {
      return ((this.page.currentPage - 1) * this.page.pageSize || 0) + index + 1;
    },

    /* 翻页 */
    pageSizeChange(data) {
      this.page.pageSize = data;
      this.getTableList();
    },
    // 跳页
    currentPageChange(pageCurrent) {
      this.page.currentPage = pageCurrent;
      this.getTableList();
    },
    // 查询
    getTableList() {
      const data = {
        language: $utils.Data.getLocalLang(),
        // paramConfigId: this.mainTableRowData.id,
        robotId: this.mainTableRowData.id,
      };
      const pageData = {
        pageSize: this.page.pageSize,
        currentPage: this.page.currentPage || 1,
      };

      $req
        .post(
          "/athena/robot/paramConfig/robotRecordPageList?" + $utils.Tools.getParams(pageData),
          data,
        )
        .then(res => {
          let result = res.data;
          if (result != null) {
            let list = result.recordList;
            list.forEach(item => {
              item.descr = this.$t(item.descrI18nCode);
            });
            this.tableData = list;
            this.page.currentPage = result.currentPage || 1;
            this.pageCount = result.pageCount;
          }
        });
    },

    // 保存
    save(data) {
      $req.post("/athena/robot/paramConfig/saveRobotRecord", data).then(res => {
        if (res.code === 0) this.getTableList();
      });
    },
    // 编辑
    itemAdd() {
      this.$refs.recordAdd.open(this.mainTableRowData, "add");
    },
    itemEdit(row) {
      this.$refs.recordAdd.open(row, "edit");
    },
    timeformatter(row, column) {
      let v = row[column["property"]];
      if (!v) return null;
      return $utils.Tools.formatDate(v, "yyyy-MM-dd");
    },
  },
};
</script>
<style scoped>
.mt-20 {
  margin-top: 20px;
}

.w_100x {
  width: 100%;
}

.btnwarp {
  padding: 43px 0 0;
}

.floor {
  height: 50px;
}

.floor > .page {
  float: left;
  line-height: 50px;
}

.floor > .jump {
  float: right;
  line-height: 50px;
}
</style>
