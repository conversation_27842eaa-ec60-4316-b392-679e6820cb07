/* ! <AUTHOR> at 2021/01 */

import _$utils from "./_utils";
import _$req from "./request";

// window.__geek__$i18n = i18n; // 这里后续看怎么优化吧
window.$req = _$req;
window.$utils = _$utils;

// 处理sessionId
const credExp = /cred=([^&]+)/.exec(location.hash);
if (credExp) $utils._sessionId = credExp[1];
window.addEventListener("message", function (event) {
  const { info } = console;
  info(".>>> iframe sessionId", event);
  // console.log("addEventListener", event);
  const data = event.data || {};
  $utils._sessionId = data.sessionId || "";

  if ($utils._sessionId && $app) {
    const currentPath = $app.$route.path;
    if (currentPath && $utils._sessionIframePath && $utils._sessionIframePath !== currentPath) {
      $app.$router.replace($utils._sessionIframePath);
    }
  }
});
