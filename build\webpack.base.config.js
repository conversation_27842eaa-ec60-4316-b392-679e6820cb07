/* ! <AUTHOR> at 2021/01 */
const { resolve } = require("path");
const ESLintPlugin = require("eslint-webpack-plugin");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const { VueLoaderPlugin } = require("vue-loader");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const CopyPlugin = require("copy-webpack-plugin");
const webpack = require("webpack");
const { STATIC_VER, isDev, htmlTitle, linkPath } = require("../config/_conf/build.config");

let copyGeekModules = [{ from: resolve("./static"), to: resolve("./dist/static") }];
if (!isDev) {
  copyGeekModules = copyGeekModules.concat([
    { from: resolve(`${linkPath["monitor2D"]}/monitor2D`), to: resolve("./dist/monitor2D") }, // map2D静态文件
    { from: resolve(`${linkPath["edit2D"]}/dist`), to: resolve("./dist/singleEdit2D") }, // 地图编辑静态文件
  ]);
}
module.exports = {
  devtool: "cheap-module-source-map",
  stats: "none",
  entry: {
    app: ["./src/app.js"],
  },
  output: {
    path: resolve("./dist"),
    filename: `js/[name].[chunkhash:7].js?${STATIC_VER}`,
    chunkFilename: `js/[name].[chunkhash:7].chunk.js?${STATIC_VER}`,
    devtoolModuleFilenameTemplate: "webpack://src/[resource-path]?[loaders]",
  },
  resolve: {
    alias: {
      "@geek_map": resolve("./geek_map"),
      "@": resolve("./src"),
      "@data": resolve("./src/data"),
      "@imgs": resolve("./src/imgs"),
      "@lang": resolve("./src/lang"),
      "@less": resolve("./src/less"),
      "@libs": resolve("./src/libs"),
      "@plugins": resolve("./src/plugins"),
      "@route": resolve("./src/route"),
      "@store": resolve("./src/store"),
      "@views": resolve("./src/views"),
      vue$: "vue/dist/vue.esm.js",
    },
    extensions: [".js", ".vue", ".css", ".less", ".json", ".ts"],
    symlinks: false,
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        // exclude: [/node_modules/, /geek_map/, /static/, /libs\/monitor2d.min.js/],
        exclude: /(node_modules|geek_map|static|monitor2d.min.js)/,
        loader: "babel-loader",
      },
      { test: /\.vue$/, loader: "vue-loader" },
      {
        test: /\.css$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: { publicPath: "../" },
          },
          "css-loader",
          "postcss-loader",
        ],
      },
      {
        test: /\.less$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: { publicPath: "../" },
          },
          "css-loader",
          "postcss-loader",
          "less-loader",
          {
            loader: "sass-resources-loader",
            options: {
              resources: [
                resolve("./src/less/mixins/_variable.less"),
                resolve("./src/less/mixins/_mixins.less"),
              ].concat(isDev ? resolve("./src/less/mixins/dev_test.less") : []),
            },
          },
        ],
      },
      {
        test: /\.scss$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: { publicPath: "../" },
          },
          "css-loader",
          "postcss-loader",
          "sass-loader",
        ],
      },
      {
        test: /\.(png|jpg|gif|svg)$/,
        type: "asset",
        parser: {
          dataUrlCondition: {
            maxSize: 300 * 1024, // 4kb
          },
        },
        generator: {
          filename: "imgs/[name].[hash:7].[ext]",
        },
      },
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/,
        type: "asset/resource",
        generator: {
          filename: "fonts/[name].[hash:7].[ext]",
        },
      },
    ],
  },
  plugins: [
    new CleanWebpackPlugin(),
    new VueLoaderPlugin(),
    new ESLintPlugin({
      context: resolve(__dirname, "./"),
      lintDirtyModulesOnly: true,
    }),
    new webpack.ProvidePlugin({
      process: "process/browser",
    }),
    new MiniCssExtractPlugin({
      filename: `css/[name].[chunkhash:7].css?${STATIC_VER}`,
      chunkFilename: `css/[name].[chunkhash:7].chunk.css?${STATIC_VER}`,
      ignoreOrder: false,
    }),
    new CopyPlugin({ patterns: copyGeekModules }),
    new HtmlWebpackPlugin({
      title: htmlTitle,
      minify: {
        // 压缩HTML文件
        removeComments: true, // 移除HTML中的注释
        collapseWhitespace: false, // 删除空白符与换行符
        minifyCSS: true, // 压缩内联css
      },
      inject: "body",
      favicon: "./static/favicon.ico",
      chunks: ["vendor", "vendor.ui", "app"],
      template: "./src/index.html",
      filename: isDev ? "./index.html" : "../index.html",
    }),
  ],
  optimization: {
    splitChunks: {
      chunks: "all", // 'all'对同步和异步引入模块都进行代码分割;'async: 只对异步引入模块进行代码分割;'initial': 只对同步代码进行代码分割
      minSize: 30000, // 代码分割模块的最小大小要求，不满足不会进行分割，单位byte
      minChunks: 1, // 最小被引用次数，只有在模块上述条件并且至少被引用过一次才会进行分割
      maxAsyncRequests: 30, // 最大的异步按需加载次数
      maxInitialRequests: 30, // 初始话并行请求不得超过30个
      enforceSizeThreshold: 50000,
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/](vue|vue-i18n|vue-router|vuex|axios|nprogress|js-cookie)[\\/]/,
          name: "vendor",
          chunks: "all",
          priority: 10,
        },
        vendor2: {
          test: /[\\/]node_modules[\\/]element-ui[\\/]/,
          name: "vendor.ui",
          chunks: "all",
          priority: 9,
        },
      },
    },
    emitOnErrors: true,
  },
};
