<template>
  <section>
    <div class="detail-info">
      <div v-if="currentLibrary.template" class="info">
        <p>
          {{ $t("lang.rms.fed.libraryName") }}:
          <span>{{ currentLibrary.template.name || "--" }}</span>
        </p>
        <p>
          {{ $t("lang.rms.fed.paramsCount") }}:
          <span>{{ totalParmaNum || 0 }}</span>
        </p>
        <p>
          {{ $t("lang.rms.fed.editor") }}:
          <span>{{ currentLibrary.template.editor || "--" }}</span>
        </p>
        <p>
          {{ $t("lang.rms.fed.editTime") }}:
          <span>{{ currentLibrary.template.updateTime | timeformatter }}</span>
        </p>
        <p>
          {{ $t("lang.rms.fed.recentApplication") }}:
          <span>{{ currentLibrary.template.applyTime | timeformatter }}</span>
        </p>
        <p>
          {{ $t("lang.rms.fed.describe") }}:
          <span>{{ currentLibrary.template.description || "--" }}</span>
        </p>
      </div>
      <div class="btn-group">
        <el-button
          v-if="checkPermission('ParamsNewLibrary', 'natural')"
          type="text"
          @click="editLibrary('add')"
        >
          {{ $t("lang.rms.fed.newLibrary") }}
        </el-button>
        <i class="spacer">|</i>
        <el-button
          v-if="checkPermission('ParamsCopyToCreateLibrary', 'natural')"
          type="text"
          @click="copyLibrary"
        >
          {{ $t("lang.rms.fed.copyToCreateLibrary") }}
        </el-button>
        <i class="spacer">|</i>
        <el-button
          v-if="checkPermission('ParamsApplyThisConfiguration', 'natural')"
          type="text"
          @click="applyLibrary"
        >
          {{ $t("lang.rms.fed.applyThisConfiguration") }}
        </el-button>
        <i class="spacer">|</i>
        <el-button
          v-if="checkPermission('ParamsEditMasterInformation', 'natural')"
          type="text"
          @click="editLibrary('edit')"
        >
          {{ $t("lang.rms.fed.editMasterInformation") }}
        </el-button>
        <i class="spacer">|</i>
        <el-button
          v-if="checkPermission('ParamsEditParams', 'natural')"
          type="text"
          @click="handleParmaTree"
        >
          {{ $t("lang.rms.fed.editParams") }}
        </el-button>
        <i class="spacer">|</i>
        <el-button
          v-if="checkPermission('ParamsDeleteLibrary', 'natural')"
          type="text"
          class="red-color"
          @click="deleteLibrary"
        >
          {{ $t("lang.rms.fed.deleteLibrary") }}
        </el-button>
      </div>
    </div>

    <div class="params-btn">
      <label>{{ $t("lang.rms.fed.configurationLibraryParameters") }}: </label>
      <el-input
        v-model="searchValue"
        :placeholder="$t('lang.rms.fed.pleaseEnter')"
        class="input-with-select"
      >
        <el-select slot="prepend" v-model="searchType" :default-first-option="true">
          <el-option :label="$t('lang.rms.fed.parameterValues')" value="1" />
          <el-option :label="$t('lang.rms.fed.nameOfParameter')" value="2" />
          <el-option :label="$t('lang.rms.fed.effectiveImmediately')" value="3" />
          <el-option :label="$t('lang.rms.fed.parameterLabel')" value="4" />
          <el-option :label="$t('lang.rms.fed.describe')" value="5" />
        </el-select>
        <el-select
          v-if="searchType === '3'"
          slot="prepend"
          v-model="isImmediate"
          :placeholder="$t('lang.rms.fed.pleaseChoose')"
          class="cover-original-input"
        >
          <el-option
            v-for="item in immediateList"
            :key="`immediate-${item.key}`"
            :label="$t(item.value)"
            :value="item.key"
          />
        </el-select>
        <el-button slot="append" icon="el-icon-search" @click="onSearch" />
      </el-input>
      <el-switch
        v-model="showDisabledParams"
        :inactive-text="$t('lang.rms.fed.displayDisabledParameters')"
      />
    </div>

    <el-table
      ref="paramsTable"
      v-loading="tableLoading"
      :data="tableData"
      :row-class-name="toggleEnableParams"
      style="width: 100%"
      tooltip-effect="dark"
      @cell-dblclick="editParams"
    >
      <el-table-column width="80">
        <template slot="header" slot-scope="scope">
          <span class="check-text">{{ $t("lang.rms.fed.enable") }}</span>
          <el-checkbox
            v-model="checkAll"
            :indeterminate="isIndeterminate"
            class="check-btn"
            @change="allParameterDisableAll"
          />
        </template>
        <template slot-scope="scope">
          <el-checkbox
            v-model="scope.row._status"
            class="check-btn"
            @change="paramsStatusChange(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column
        prop="value"
        :label="$t('lang.rms.fed.parameterValues') + '(' + $t('lang.rms.fed.dbToEdit') + ')'"
        width="220"
      />
      <el-table-column prop="code" :label="$t('lang.rms.fed.nameOfParameter')" width="120" />
      <el-table-column
        prop="immediate"
        :label="$t('lang.rms.fed.effectiveImmediately')"
        width="80"
        :formatter="formatterImmediate"
        align="center"
      />
      <el-table-column
        prop="tags"
        :label="$t('lang.rms.fed.parameterLabel')"
        width="100"
        :formatter="formatterTags"
        align="center"
      />
      <el-table-column prop="descr" :label="$t('lang.rms.fed.describe')" width="180">
        <template slot-scope="scope">
          <span>{{ $t(scope.row.descr) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <dialog-library-edit ref="libraryEdit" @updatedLibrary="updatedLibrary" />
    <dialog-params-tree ref="paramsTree" @updatedLibrary="updatedLibrary" />
    <dialog-params-edit
      ref="paramsEdit"
      :immediate-list="immediateList"
      @save="updateTemplateParams"
    />
  </section>
</template>

<script>
import DialogLibraryEdit from "./dialogLibraryEdit";
import DialogParamsTree from "./dialogParamsTree";
import DialogParamsEdit from "./dialogParamsEdit";

export default {
  name: "LibraryDetail",
  components: { DialogLibraryEdit, DialogParamsTree, DialogParamsEdit },
  props: ["currentLibrary"],
  data() {
    return {
      currentId: "",
      searchValue: "",
      searchType: "",
      isImmediate: "1",
      immediateList: [
        {
          key: "0",
          value: "lang.rms.fed.no",
        },
        {
          key: "1",
          value: "lang.rms.fed.yes",
        },
      ],
      totalParmaNum: 0,
      paramsTree: [],

      tableData: [],
      isIndeterminate: false,
      checkAll: true,
      showDisabledParams: false,
      tableLoading: false,
    };
  },
  watch: {
    currentLibrary(data) {
      if (!data) return;

      if (this.currentId !== data.template.id) {
        this.searchValue = "";
        this.searchType = "";
        this.isImmediate = "1";
      }

      this.currentId = data.template.id;
      this.getCurrentLibraryTree();
      if (data.hasOwnProperty("items") && data.items) {
        let tableData = data.items;
        tableData.forEach(item => {
          item._status = !!item.status;
        });
        this.tableData = tableData;
      }
      this.getCheckStatus();
    },
  },
  filters: {
    timeformatter(value) {
      if (!value) return "--";
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss");
    },
  },
  methods: {
    editLibrary(type) {
      let template = this.currentLibrary.template;
      this.$refs.libraryEdit.open(type, template);
    },
    copyLibrary() {
      let template = this.currentLibrary.template;
      $req
        .post("/athena/config/template/copy?templateId=" + template.id, {
          name: template.name + "_copy",
          description: template.description,
        })
        .then(res => {
          if (res.code === 0) {
            this.updatedLibrary(res.data.id);
          }
        });
    },
    applyLibrary() {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmTheOperation"))
        .then(() => {
          $req
            .postParams("/athena/config/template/apply", {
              templateId: this.currentId,
            })
            .then(() => {
              this.updatedLibrary(null);
            });
        })
        .catch(e => console.log(e));
    },
    handleParmaTree() {
      this.$refs.paramsTree.open(this.currentLibrary, this.paramsTree);
    },
    deleteLibrary() {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete"), {
        confirmText: $app.$t("lang.rms.fed.delete"),
      })
        .then(() => {
          $req
            .get("/athena/config/template/delete", {
              templateId: this.currentId,
            })
            .then(res => {
              // 配置库-删除配置库
              if (res.code === 0) {
                this.updatedLibrary(null);
              }
            });
        })
        .catch(e => console.log(e));
    },

    onSearch() {
      this.$emit("updatedLibraryParams", this.currentId, {
        language: $utils.Data.getLocalLang(),
        searchType: this.searchType || 1,
        searchValue: this.searchValue,
        isImmediate: this.isImmediate,
      });
    },

    editParams(row) {
      this.$refs.paramsEdit.open(row);
    },

    updatedLibrary(updatedId) {
      this.$emit("updatedLibrary", updatedId);
    },
    allParameterDisableAll(checked) {
      this.tableLoading = true;
      const status = checked ? 1 : 0;
      $req
        .postParams("/athena/config/template/item/all/updateStatus", {
          templateId: this.currentId,
          status: status,
        })
        .then(res => {
          if (res.code === 0) {
            this.tableData.forEach(item => {
              item.status = status;
              item._status = checked;
            });
            this.getCheckStatus();
          }
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    paramsStatusChange(rowData) {
      this.tableLoading = true;
      const _status = rowData._status;
      let data = Object.assign({}, rowData, { status: _status ? 1 : 0 });
      delete data._status;
      $req
        .post(
          "/athena/config/template/item/update?templateId=" + this.currentId, // 配置库-参数项更新
          data,
        )
        .then(res => {
          rowData.status = _status ? 1 : 0;
          this.getCheckStatus();
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    updateTemplateParams(data) {
      data.status = Number(data.status);
      $req
        .post(
          "/athena/config/template/item/update?templateId=" + this.currentId, // 配置库-参数项更新
          data,
        )
        .then(res => {
          this.updatedLibrary(id);
        });
    },

    getCurrentLibraryTree() {
      $req
        .get("/athena/config/template/item/select", {
          templateId: this.currentId,
        })
        .then(res => {
          // 配置库-参数项选择（参数树结构）
          if (res.code !== 0) return;
          let data = res.data;
          this.totalParmaNum = data.existCodes.length;
          this.paramsTree = data.groups;
        });
    },
    getCheckStatus() {
      console.log(11111111, this.checkAll);
      const tableLen = this.tableData.length;
      const checkLen = this.tableData.filter(item => item.status).length;
      this.isIndeterminate = checkLen !== 0 && checkLen !== tableLen;
      this.checkAll = tableLen === checkLen;
      console.log(22222222, this.checkAll);
    },

    toggleEnableParams(scope) {
      if (!scope.row.status) {
        if (this.showDisabledParams) {
          return "row-gray";
        } else {
          return "row-hide";
        }
      }
      return "";
    },
    formatterImmediate(row, column, cellValue, index) {
      const immediateList = this.immediateList;
      let obj = immediateList.find(item => item["key"] === cellValue.toString());
      return obj ? obj.value : cellValue;
    },
    formatterTags(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue.join(",");
      } else {
        return cellValue;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.detail-info {
  border-bottom: 10px solid #eee;

  .info {
    .g-flex();
    justify-content: flex-start;
    padding: 16px 16px 5px;
    flex-wrap: wrap;

    > p {
      margin-right: 20px;
      font-size: 14px;
      font-weight: 600;
      padding: 0 0 5px;

      span {
        font-weight: 400;
        color: #666;
      }
    }
  }

  .btn-group {
    border-top: 1px solid #e5e9ec;
    border-bottom: 1px solid #e5e9ec;
    background: #f7f9fa;
    padding: 0 16px;

    .el-button {
      line-height: 16px;
    }

    .red-color {
      color: #f56c6c;
    }

    .spacer {
      font-style: italic;
      color: #ddd;
      margin: 0 12px 0 10px;
    }
  }
}

.params-btn {
  .g-flex();
  justify-content: flex-start;
  padding: 10px 16px 5px;

  label {
    font-size: 14px;
    font-weight: 600;
    padding: 0 8px 0 0;
  }

  :deep(.el-input-group) {
    width: 280px;

    .el-input-group__prepend {
      width: 100px;
    }
  }

  .el-switch {
    padding-left: 26px;
    width: 30%;
  }

  .cover-original-input {
    position: absolute;
    top: 0;
    left: 100px;
    margin: 0 !important;
    z-index: 99;
    width: 134px;
    background: #fff;
    height: 30px;
    border-right: 1px solid #dcdfe6;
  }
}

.check-text {
  font-weight: 800;
  color: #555;
}

.check-btn {
  :deep(.el-checkbox__original) {
    min-height: unset;
  }
}

:deep(.el-table__body) {
  .row-gray {
    background: #eee;
  }

  .row-hide {
    display: none;
  }
}
</style>
