# dir
geek_modules/
public/
!geek_map/map-edit/public/
monitor2D/
dist/
!geek_map/map-edit-3d/dist/
config/_conf/app.config.js
config/_conf/build.config.js
static/version.config.json
**/.DS_Store
.DS_Store

# local env files
.env.local
.env.*.local

# Log files
**/*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pm2logs/
*.class
*.log
!auditReport.log

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw*
.history

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# next.js build output
.next

# idea conf
*.iml
target/
.idea/

# Package Files #
*.jar
*.war
*.ear
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# eclipse conf
.classpath
.project
.settings/
.pydevproject
.externalToolBuilders/
.vscode/

# build
build/tools/config.js
.geekplusExpand
