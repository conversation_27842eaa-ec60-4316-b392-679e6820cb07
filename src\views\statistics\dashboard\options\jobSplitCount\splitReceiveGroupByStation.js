export default {
  x: 0, 
  y: 17,
  width: 24, 
  height: 12,

  // 地图数据信息
  chart: {
    type: 'stackBar', // 图表类型

    // 地图数据来源
    request: {
      url: '/athena/stats/query/job/split/count/',  // 请求接口
      filters: ['date', 'cycle', 'showReceive', 'showToStationDone', 'showOrgData'], // 筛选条件
      defFilters: {
        date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
        cycle: "60",
        showReceive: true,
        showToStationDone: true,
        showOrgData: false
      },
      timer: 5000,  // 轮询时间, 如果是0, 则不轮询
    },

    // 地图数据处理
    dataHandler: {  // 数据处理
      handler: 'getStationReceiveBySplit',
      params: {
        type: 'stackBar',
        paramsName: 'split_receiveGroupByStation',
        title: '工作站分组接收任务数量'
      },
    },
  }
}