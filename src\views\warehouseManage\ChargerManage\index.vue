<!--
 * @Author: z<PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2021-12-27 12:13:50
 * @Description:
-->
<template>
  <geek-main-structure><ChargerInstance /></geek-main-structure>

  <!-- <el-card>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane :label="$t('lang.rms.fed.chargerChargingStationManagement')" name="first1">
        <ChargerInstance />
      </el-tab-pane>
    </el-tabs>
  </el-card> -->
</template>

<script>
import ChargerInstance from "./subPages/ChargerInstance";

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    ChargerInstance,
  },
  data() {
    // 这里存放数据
    return {
      activeName: "first1",
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    handleClick(tab) {
      console.log(tab);
    },
  },
};
</script>
<style lang="scss" scoped>
.outer-wrap {
  background: white !important;
  height: auto;
}
.inner-wrap {
  width: 100%;
  box-sizing: border-box;
  margin-right: 25px;
  margin-left: 25px;
}
</style>
