import { ToolPanelType } from "@packages/type/editUiType";
import {
  OMNI_DIR_CELL,
  SHELF_CELL,
  BLOCKED_CELL,
  PALLET_RACK_CELL,
  DEVICE_REFLECTIVE,
  DEVICE_STATION,
  DEVICE_CHARGER,
  DEVICE_ELEVATOR,
  DEVICE_SAFE
} from "@packages/configure/dict/nodeType";

// 添加单元格
export const ADD_CELL: ToolPanelType = {
  option: {
    icon: "map-font-addCell",
    name: "addCell",
    group: "add",
    isSelect: true,
    describe: "lang.rms.fed.cell",
    eventName: "map:addElement",
    addElementName: OMNI_DIR_CELL,
    //只能通过Esc清空激活态
    resetByEsc: true
  },
};

// 添加二维码单元格
export const ADD_BUMADIAN: ToolPanelType = {
  option: {
    icon: "map-font-bumadian",
    name: "addBumadian",
    group: "add",
    isSelect: true,
    describe: "lang.rms.web.map.cell.type.QRCell",
    eventName: "map:addElement",
    addElementName: OMNI_DIR_CELL,
    addQR: true,
    //只能通过Esc清空激活态
    resetByEsc: true
  },
};

// 添加货架
export const ADD_SHELF: ToolPanelType = {
  option: {
    icon: "map-font-huojia",
    name: "addShelf",
    group: "add",
    isSelect: true,
    describe: "lang.rms.fed.shelf",
    eventName: "map:addElement",
    addElementName: SHELF_CELL,
  },
};

// 工作站
export const ADD_STATION: ToolPanelType = {
  option: {
    icon: "map-font-gongzuozhan",
    name: "addStation",
    group: "add",
    isSelect: true,
    describe: "lang.rms.fed.station",
    eventName: "map:addDevice",
    addElementName: DEVICE_STATION,
  },
};

// 充电站
export const ADD_CHARGING: ToolPanelType = {
  option: {
    icon: "map-font-chongdianzhan",
    name: "addCharg",
    group: "add",
    isSelect: true,
    describe: "lang.rms.web.map.cell.type.charger",
    eventName: "map:addDevice",
    addElementName: DEVICE_CHARGER,
  },
};

// 安全设备
export const ADD_SAFE: ToolPanelType = {
  option: {
    icon: "map-font-shebei",
    name: "addSafe",
    group: "add",
    isSelect: true,
    describe: "lang.rms.map.dmp.edit",
    eventName: "map:addDevice",
    addElementName: DEVICE_SAFE,
  },
};

// 阻塞点
export const ADD_BLOCK: ToolPanelType = {
  option: {
    icon: "map-font-luzhang",
    name: "addBlock",
    group: "add",
    isSelect: true,
    describe: "lang.rms.web.map.cell.type.blocked",
    eventName: "map:addElement",
    addElementName: BLOCKED_CELL,
  },
};

// 反光柱
export const ADD_REFLECTOR: ToolPanelType = {
  option: {
    icon: "map-font-dingshitufanguangzhu",
    name: "addReflector",
    group: "add",
    eventName: "map:addDevice",
    addElementName: DEVICE_REFLECTIVE,
    isSelect: true,
    describe: "lang.rms.web.map.element.marker",
  },
};

// 托盘架点
export const ADD_TRAYRACK: ToolPanelType = {
  option: {
    name: "addTrayRack",
    icon: "map-font-tuopanziyuan",
    group: "add",
    isSelect: true,
    describe: "lang.rms.web.map.cell.type.palletRack",
    eventName: "map:addElement",
    addElementName: PALLET_RACK_CELL,
  },
};

// 电梯
export const ADD_ELEVATOR: ToolPanelType = {
  option: {
    name: "addElevator",
    icon: "map-font-dianti",
    group: "add",
    isSelect: true,
    describe: "添加电梯",
    eventName: "map:addDevice",
    addElementName: DEVICE_ELEVATOR,
  },
};

// 批量添加单元格
export const ADD_CELLBATCH: ToolPanelType = {
  option: {
    icon: "map-font-batch-point",
    name: "addBatchCell",
    group: "add",
    eventName: "map:addBatchCell",
    isSelect: true,
    describe: "lang.rms.fed.batchCell",
    // disabled: () => {
    //   return;
    // },
  },
};

// 格式刷
export const FORMAT_BRUSH: ToolPanelType = {
  option: {
    icon: "map-font-geshishua",
    name: "addFormatBrush",
    group: "add",
    eventName: "map:formatBrush",
    isSelect: true,
    describe: "格式刷",
  },
};
