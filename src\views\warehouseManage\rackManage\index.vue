<template>
  <geek-main-structure>
    <el-tabs v-model="activeName" class="tab-class">
      <!-- 货箱查询 -->
      <el-tab-pane
        v-if="tabNamePerssion['packQuery']"
        :label="$t('auth.rms.packQuery.page')"
        name="packQuery"
      >
        <packQuery />
      </el-tab-pane>
      <!-- 货箱轨迹查询 -->
      <el-tab-pane
        v-if="tabNamePerssion['boxHistorySearch']"
        :label="$t('auth.rms.boxHistorySearch.page')"
        name="boxHistorySearch"
      >
        <boxHistorySearch />
      </el-tab-pane>
    </el-tabs>
  </geek-main-structure>
</template>

<script>
import packQuery from "./packQuery";
import boxHistorySearch from "./boxHistorySearch";
export default {
  components: { packQuery, boxHistorySearch },
  data() {
    return {
      tabNamePerssion: {
        packQuery: this.getTabPermission("TabBoxSearchPage", "boxManage"),
        boxHistorySearch: this.getTabPermission("TabBoxTrackPage", "boxManage"),
      },
      defaultActive: "packQuery",
    };
  },
  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
  },
  mounted() {
    this.defaultActive = $utils.Tools.getRouteQueryTabName(
      this.defaultActive,
      this.tabNamePerssion,
    );
  },
};
</script>
<style lang="scss" scoped></style>
