# geek+ PC RMS system(<EMAIL>)

# 此分支版本为 v5.6.1.0-sandbox 分支

### 一、项目启动构建

#### 1、clone 或切换相应分支（如已操作过则忽略）

```shell
- git clone -b v5.6.1.0-sandbox http://gitlab.geekplus.cc/rms-fed/rms-5.6.git
```

#### 2、安装依赖包,全局安装 eslint（如已操作过则忽略），确保所有的库都在同一个分支

```shell
- npm install eslint -g # 如已操作过则忽略
- npm install
- cd .\geek_map\map-edit\
- npm install
- cd .\geek_map\ts-map-fe\
- npm install
```

#### 3、启动开发环境（dev）

```shell
- npm run dev # 启动所有功能
- npm run _dev:ts-map-fe  # 只启动地图监控（有权限登录模式需处理下sessionId）
- npm run _dev:map-edit  # 只启动地图编辑（有权限登录模式需处理下sessionId）
- npm run _dev:rms  # 只启动非地图的rms系统
```

#### 4、打包代码（pro build）

```shell
- npm run build:pro  # 打包全部
- npm run _build:ts-map-fe  # 只打包地图监控
- npm run _build:map-edit  # 只打包地图编辑
- npm run _build:rms  # 只打包rms
```

#### 5、浏览

- 打开浏览器，访问 用户名 admin 密码 Geekplus_2020
- > localhost:9527

#### 6、其他运行指令

```shell
- npm run _dev:ts-map-fe # dev环境下 单独启动 ts-map-fe
- npm run _build:ts-map-fe # dev环境下 单独 build ts-map-fe
- npm run _dev:rms # dev环境下 单独启动主架构 rms
- npm run _build:rms # dev环境下 单独 build 主架构 rms
```

#### 7、tips

- 如启动报错，请优先查看 node 和 npm 版本

---

### 二、技术选型

```
 - vue
 - vue-router
 - vuex          //数据状态管理
 - element-ui    //vue PC组件库
 - axios、qs     //请求接口用
```

---

### 三、目录规范

```
├── RMS-fe
│   ├── build   # 构建脚本目录（包含auto build、auto link、auto config、auto webpack）
│   ├── config  # 项目配置目录
│   │   ├── _conf       # 自动生成的文件
│   │   ├── app.dev.conf.js       # 配置dev环境config、如api地址等
│   │   ├── app.pro.conf.js       # 配置pro环境config、如htmlTitle等
│   │   └── common.conf.js        # 打包需要的config
│   ├── geek_map    # 需要link的项目文件夹
│   │   ├── map-edit    # 2d地图编辑
│   │   ├── ts-map-fe    # 2d地图监控
│   │   ├── map-edit-3d    # 3d地图监控
│   ├── src     # 源码目录
│   │   ├── imgs
│   │   ├── lang    # 国际化相关配置、公共方法、配置目录
│   │   ├── less    # 公共样式目录
│   │   ├── libs    # 公共封装工具库目录
│   │   │   └── index.js       # 我们组装模块并导出 libs 的地方
│   │   ├── plugins # 公共vue插件、vue方法目录
│   │   ├── route      # 前端路由
│   │   ├── store      # 应用级数据（state）
│   │   │   ├── index.js          # 我们组装模块并导出 store 的地方
│   │   │   ├── rootState.js      # 根级别的state
│   │   │   └── modules
│   │   │       └── XXX.js        # 模块 模块级别必须设置namespaced: true,
│   │   │
│   │   ├── views     # 页面目录
│   │   │   ├── dir/XXX    # 各种页面组件
│   │   │   └── root.vue   # 根组件
│   │   │
│   │   ├── app.js     # 入口js文件
│   │   └── index.html # 入口页面
│   │
│   ├── static  # 纯静态资源（现场可配置化文件、version版本文件），不会被wabpack构建
│   │   ├── area.js     # 区域配置文件
│   │   ├── config.json     # 以前的权限模式配置文件、为方面现场人员配置虽没用但留着它别删！
│   │   ├── map.config.js     # 地图监控色彩配置
│   │   └── version.config.js     # 自动生成的版本文件
```
