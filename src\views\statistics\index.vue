<template>
  <geek-main-structure :space="false" style="padding: 0; background: #f4f5f7">
    <geek-tabs-nav class="tab-class" :nav-list="navList" :active-nav-id="activeNavId" @select="tabsNavChange" />
    <component :is="activeNavId" :options="curData" />
  </geek-main-structure>
</template>

<script>
import statisticsChart from "./dashboard/index.view.vue";
import statisticsMap from "./components/map";
import dashboard from "./dashboard";
import testDemoOption from './dashboard/options/demo';
import chartDemoOption from './dashboard/chart/all';

export default {
  name: "statistics",
  components: { statisticsChart, statisticsMap, dashboard },
  data() {
    return {
      activeNavId: "dashboard",
      navList: [
        {
          id: "dashboard",
          text: "dashboard",
          data: testDemoOption,
        },
        {
          id: "statisticsChart",
          text: "图表",
          data: chartDemoOption
        },
        {
          id: "statisticsMap",
          text: "地图热度",
        }
      ],
    };
  },
  computed: {
    curData() {
      return this.navList.find(item => this.activeNavId === item.id).data;
    },
  },
  methods: {
    tabsNavChange(id) {
      if (this.activeNavId === id) return;
      this.activeNavId = id;
    },
  },
};
</script>

<style lang="less" scoped>
@tab-nav-height: 31px;

.tab-class {
  width: 100%;
  height: @tab-nav-height;
  padding: 0 12px;
  color: #fff;

  :deep(> .el-menu-item) {
    padding-top: 2px;
    height: @tab-nav-height;
    line-height: @tab-nav-height;
    font-size: 13px;
  }
}
</style>
