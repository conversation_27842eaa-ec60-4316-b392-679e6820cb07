<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2021-12-27 14:30:15
 * @Description:
-->
<template>
  <el-form
    ref="createForm"
    class="creat-form"
    :model="modelData"
    label-position="right"
    label-width="135px"
    :disabled="!editProp"
    :rules="rules"
    :validate-on-rule-change="false"
  >
    <div class="form-group-title division">
      {{ $t("lang.rms.api.result.warehouse.baseProperies") }}:
    </div>
    <!-- <el-form-item :label="$t('lang.rms.api.result.warehouse.orgCode')" prop="mechanismCode">
      <el-input
        v-model="modelData.mechanismCode"
        :placeholder="$t('lang.rms.api.result.warehouse.pleseEnterOrganizationCode')"
      />
    </el-form-item> -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.mechanism.spuName')" prop="name">
      <el-input
        v-model="modelData.name"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterInstitutionSPU')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotMechanismModel')" prop="file">
      <el-upload
        v-if="!modalUrl"
        action="javascript:void(0)"
        drag
        accept=".glb, .gltf"
        :limit="1"
        :before-upload="beforeUpload"
        :on-change="handleUploadBefore"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">{{ $t("lang.rms.api.result.warehouse.dragFileHere") }}</div>
        <div slot="tip" class="el-upload__tip">
          {{ $t("lang.rms.api.result.warehouse.uploadOntologyModel") }}
        </div>
      </el-upload>
      <ModalRenderer v-if="modalUrl" :modal-url="modalUrl" />
      <i
        v-if="modalUrl && editProp"
        style="cursor: pointer"
        class="el-icon-delete"
        @click="uploadClear()"
      />
    </el-form-item>
    <div class="form-group-title division">
      {{ $t("lang.rms.api.result.warehouse.physicalProperty") }}:
    </div>
    <el-form-item :label="$t('lang.rms.fed.length')" prop="length">
      <el-input
        v-model="modelData.length"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMerchanismLength')"
        class=""
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.fed.width')" prop="width">
      <el-input
        v-model="modelData.width"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMerchanismWidth')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.fed.high')" prop="height">
      <el-input
        v-model="modelData.height"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMerchanismHeight')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.maxLoad')" prop="maxLoad">
      <el-input
        v-model="modelData.maxLoad"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMerchanismMaxLoad')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.fed.maxWorkingHeight')" prop="maxWorkHeight">
      <el-input
        v-model="modelData.maxWorkHeight"
        :placeholder="$t('lang.rms.api.result.warehouse.maxWorkingHeight')"
      />
    </el-form-item>
    <!-- 旋转直径 -->
    <el-form-item :label="$t('lang.rms.fed.rotationDiameter')" prop="diameter">
      <el-input
        v-model="modelData.diameter"
        :placeholder="$t('lang.mb.login.pleaseEnter', [`${$t('lang.rms.fed.rotationDiameter')}`])"
      />
    </el-form-item>
    <!-- 坐标偏移值 -->
    <el-form-item :label="$t('lang.rms.fed.locationOffset')" prop="locationOffset">
      <el-input
        v-model="modelData.locationOffset"
        :placeholder="$t('lang.mb.login.pleaseEnter', [`${$t('lang.rms.fed.locationOffset')}`])"
      />
      </el-form-item>
    <!-- 滚筒上装对接面 -->
    <el-form-item
      :label="$t('lang.rms.fed.dockingSide')"
      prop="dockingSides"
    >
      <el-select
        v-model="modelData.dockingSides"
        multiple
        :placeholder="$t('lang.rms.fed.dockingSide')"
      >
        <el-option value="F" :label="$t('F')" />
        <el-option value="B" :label="$t('B')" />
        <el-option value="L" :label="$t('L')" />
        <el-option value="R" :label="$t('R')" />
      </el-select>
    </el-form-item>
    <!-- 是否A面取A面放 -->
     <el-form-item
      :label="$t('lang.rms.fed.deliverStrategy.aGetaDrop')"
      prop="deliverStrategy"
    >
      <el-select
        v-model="modelData.deliverStrategy"
        :placeholder="$t('lang.rms.fed.deliverStrategy.aGetaDrop')"
      >
        <el-option :value="1" :label="$t('lang.rms.fed.yes')" />
        <el-option :value="0" :label="$t('lang.rms.fed.no')" />
      </el-select>
    </el-form-item>
    <!-- 是否有后退传感器 -->
    <el-form-item :label="$t('lang.rms.fed.isHasBackupSensor')" prop="hasBackupSensor">
      <el-switch
        v-model="modelData.hasBackupSensor"
        :active-value="1"
        :inactive-value="0"
      ></el-switch>
    </el-form-item>
     
    <div class="form-group-title division mt20">{{ $t("lang.rms.fed.mechanismCapability") }}:</div>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.movementAbility')"
      prop="controlAbilities"
    >
      <el-select
        v-model="modelData.controlAbilities"
        multiple
        :placeholder="$t('lang.rms.fed.choose')"
      >
        <el-option value="BEEP" :label="$t('鸣笛')" />
        <el-option value="LIGHT" :label="$t('lang.rms.api.result.warehouse.light')" />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.capacity')" prop="actionAbilities">
      <el-select
        v-model="modelData.actionAbilities"
        multiple
        :placeholder="$t('lang.rms.fed.choose')"
      >
        <el-option
          v-for="item in (dictionary || {}).ROBOT_BEHAVIOR || []"
          :key="item.fieldCode"
          :label="item.fieldCode"
          :value="item.fieldValue"
        />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
// 没测过吗？！！全是BUG
import { mapState } from "vuex";
import ModalRenderer from "../../../../Components/ModalRenderer";

export default {
  name: "ModelCreateForm",
  // import引入的组件需要注入到对象中才能使用
  components: {
    ModalRenderer,
  },
  props: {
    slopProps: {
      type: Object,
      default() {
        return {};
      },
    },
    editProp: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    // 这里存放数据
    return {
      modelData: { file: this.slopProps.mechanismImage, ...this.slopProps },
      rules: {
        name: [
          {
            required: true,
            message: this.$t("lang.rms.web.robot.pleaseEnterProductSpu"),
            trigger: "blur",
          }, // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        // mechanismCode: [
        //   {
        //     required: true,
        //     message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMechanismCode"),
        //     trigger: "blur",
        //   },
        // ],
        length: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMerchanismLength"),
            trigger: "blur",
          }, // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        width: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMerchanismWidth"),
            trigger: "blur",
          }, // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        height: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMerchanismHeight"),
            trigger: "blur",
          },
        ],
        actionAbilities: [
          {
            required: true,
            message: this.$t("lang.rms.api.robot.pleaseSelectBehaviorAbility"),
            trigger: "blur",
          },
        ],
      },
      modalUrl: this.slopProps.mechanismImage || "",
    };
  },
  // 监听属性 类似于data概念
  computed: {
    ...mapState(["dictionary"]),
  },
  // 监控data中的数据变化
  watch: {
    slopProps(newObj) {
      this.modelData = { file: newObj.mechanismImage, ...newObj };
      this.modalUrl = newObj.mechanismImage || "";
    },
  },
  // 方法集合
  methods: {
    fileValidator(rule, value, callback) {
      if (value) {
        return callback();
      }

      return callback(
        new Error(this.$t("lang.rms.api.result.warehouse.pleaseSelectLocalModeFile")),
      );
    },
    getFormValues() {
      const $form = this.$refs["createForm"];
      return $form.validate();
    },
    resetFormValues() {
      this.$refs["createForm"].resetFields();
    },
    beforeUpload() {
      return false;
    },
    handleUploadBefore(file) {
      if (file.size >= 52428800) {
        this.$message({
          type: "error",
          message: "文件多大，请上传50M内文件",
        });
        return;
      }

      this.modalUrl = URL.createObjectURL(file.raw);
      const demoData = new FormData();
      demoData.append("file", file.raw);
      this.modelData.file = demoData.get("file");
      this.$refs["createForm"].validateField("file");
    },
    uploadClear() {
      this.modalUrl = "";
      this.modelData.file = "";
      this.$forceUpdate();
    },
  },
};
</script>
<style lang="scss" scoped>
.creat-form .el-input,
.creat-form .el-select {
  width: 100%;
}
.form-group-title {
  font-size: 16px;
  font-weight: bold;
  margin: 3px auto;
  text-align: left;
}
.division {
  margin-bottom: 20px;
}
</style>
