export default {
  x: 0, 
  y: 0,
  width: 24, 
  height: 12,

  // 地图数据信息
  chart: {
    // 改为饼图
    type: 'table', // 图表类型

    // 地图数据来源
    request: {
      url: '/athena/map/monitor/stat/fault/detailByDay',  // 请求接口
      filters: ['startTime', 'endTime', 'statType', 'mapId', 'codes', 'currentPage', 'pageSize'], // 筛选条件
      defFilters: {
        // 今日0点的时间戳
        startTime: new Date().setHours(0, 0, 0, 0),
        endTime: new Date().setHours(23, 59, 59, 0),
        statType: 'GROUND_MISSING_COUNT',
        currentPage: 1,
        pageSize: 20,
      },
      timer: 5000,  // 轮询时间, 如果是0, 则不轮询
    },

    // 地图数据处理
    dataHandler: {  // 数据处理
      handler: 'getMonitorStatFaultDetailByDay',
      params: {
        type: 'table',
        paramsName: 'N2S_PATH_CELL',
        title: '机器人类型丢码率'
      },
    },
  }
}