<template>
  <geek-main-structure class="containerLocation">
    <m-page
      ref="myPage"
      :is-pagination="true"
      :formatter-search-params="formatterSearchParams"
      :search-sync="searchSync"
    >
      <div class="form-content">
        <m-form ref="myForm" :form-data="formData" @reset="handlerFormReset"></m-form>
      </div>
      <div class="table-content">
        <m-table :table-item="tableItem" :table-data="tableData" :page-data="pageData"></m-table>
      </div>
    </m-page>
  </geek-main-structure>
</template>
<script>
import { mapActions, mapState } from "vuex";
import { getSearchFormItem, getSearchTableItem } from "./data";
export default {
  data() {
    return {
      tableData: [],
      pageData: {
        currentPage: 1,
        pageSize: 10,
        recordCount: 0,
      },
      once: true,
    };
  },
  computed: {
    ...mapState(["BOX_SEARCH_DICT"]),
    formData() {
      return getSearchFormItem();
    },
    tableItem() {
      console.log(this.BOX_SEARCH_DICT);
      return getSearchTableItem({ destShelfSide: this.BOX_SEARCH_DICT });
    },
  },
  activated() {
    this.fetchDict(["BOX_SEARCH"]);
  },
  methods: {
    ...mapActions(["fetchDict"]),
    // 处理search
    formatterSearchParams(val) {
      const { dataRange, ...others } = val;
      if (dataRange && dataRange.length) {
        const [startTime, endTime] = dataRange;
        return {
          ...others,
          startTime: new Date(startTime).valueOf(),
          endTime: new Date(endTime).valueOf(),
        };
      }
      return val;
    },
    // searchSync
    async searchSync({ params }, next) {
      if (!params.boxCode) {
        !this.once &&
          this.$warning(this.$t("lang.mb.login.required", [this.$t("lang.rms.fed.boxCode")]));
        this.once = false;
        next({ pageData: this.pageData });
        return;
      }
      const { code, data } = await $req.get("/athena/box/movePath", params);
      if (code) return;
      this.tableData = data.recordList;
      this.once = false;
      next(data);
    },
    handlerFormReset() {
      let start = new Date();
      let end = new Date();
      start.setTime(start.getTime() - 3 * 60 * 60 * 1000);
      this.$refs.myForm.changeFormValue({ dataRange: [start, end] });
    },
  },
};
</script>
<style lang="less" scoped>
.form-content {
  padding-bottom: 10px;
  border-bottom: 5px solid #eee;
}

.table-content {
  padding-top: 10px;
}
</style>
