/* ! <AUTHOR> at 2021/01 */

export default {
  state: {
    menuList: null,
    menuAuth: [],
    btnAuth: [],
    headerTabs: [{ title: "lang.rms.fed.home", path: "/dashboard" }],
    selectedHeaderTab: "",
    logs: [], // 右上角 error log
    sideBarChange: Date.now(), // sidebar展开收起变化时间
    BOX_SEARCH_DICT: [], // 货箱位置查询-货架面
    dictionary: [],
  },
  getters: {},
  mutations: {
    setSideBarChange(state) {
      state.sideBarChange = Date.now();
    },
    setMenuList(state, menuList) {
      state.menuList = menuList;
    },
    setMenuAuth(state, menuAuth) {
      state.menuAuth = menuAuth;
      $utils.Data.setAuthTabList(JSON.stringify(menuAuth));
    },
    setBtnAuth(state, btnAuth) {
      state.btnAuth = btnAuth;
      $utils.Data.setAuthBtnList(JSON.stringify(btnAuth));
    },
    setErrorLog(state, logs) {
      state.logs.push(logs);
    },

    addHeaderTabs(state, tab) {
      let index = state.headerTabs.findIndex(item => item.path === tab.path);
      state.selectedHeaderTab = tab;
      if (index === -1) {
        state.headerTabs.push(tab);
      }
    },
    delHeaderTabs(state, tab) {
      let index = state.headerTabs.findIndex(item => item.path === tab.path);
      if (index !== -1) {
        $app.$delete(state.headerTabs, index);
      }
    },
    removeHeaderTabs(state) {
      state.headerTabs = [{ title: "lang.rms.fed.home", path: "/dashboard" }];
    },
    // 通用更新
    common(state, payLoad) {
      const keys = Object.keys(payLoad);
      keys.map(item => {
        state[item] = payLoad[item];
        return true;
      });
    },

    setDictionary(state, dictionary) {
      state.dictionary = dictionary;
    },
  },
  actions: {
    addErrorLog({ commit }, logs) {
      let url = window.location.href;
      const obj = {
        url,
        err: logs.err.message || "",
        info: logs.info,
        tag: logs.vm.$vnode.tag || "",
        stack: logs.err.stack || "",
      };
      commit("setErrorLog", obj);
    },
    async fetchDict({ commit }, payLoad) {
      const { code, data } = await $req.post("/athena/dict/query", { types: payLoad });
      if (code !== 0) return;
      for (let i = 0, l; (l = payLoad[i++]); ) {
        const options = data[l];
        if (!options) continue;
        commit("common", {
          [`${l}_DICT`]: options.map(i => ({ label: i.descr, value: i.fieldCode })),
        });
      }
    },
  },
};
