// 折线图
import groundMissingCount from "./statRobotFaultSumByDay/groundMissingCount";
import doneJobCount from "./jobDashboardCount/doneJobCount";
import receiveJobCount from "./jobDashboardCount/receiveJobCount";

// jobSplitCount 接口
import totalReceive from "./jobSplitCount/totalReceive";
import totalStationDone from "./jobSplitCount/totalStationDone";
import splitReceiveGroupByStation from "./jobSplitCount/splitReceiveGroupByStation";
import splitStationDoneGroupByStation from "./jobSplitCount/splitStationDoneGroupByStation";

// jobCollect 接口
import JobDoneCost from "./jobCollect/JobDoneCost";
import p40StayStationCost from "./jobCollect/p40StayStationCost";
import rmsJobReceiveToP40StartMove from "./jobCollect/rmsJobReceiveToP40StartMove";
import rsJobStartMoveToDestLatticeCostHigh from "./jobCollect/rsJobStartMoveToDestLatticeCostHigh";
import rmsJobBoxArriveTransferToP40StartMove from "./jobCollect/rmsJobBoxArriveTransferToP40StartMove";
import p40ToStationJobStartMoveToStation from "./jobCollect/p40ToStationJobStartMoveToStation";
import p40StationBackStartMoveToDestLattice from "./jobCollect/p40StationBackStartMoveToDestLattice";

// detailByDay 接口
// import W2E_S2N_PATH_CELL from "./statFaultDetailByDay/W2E_S2N_PATH_CELL";
import N2S_PATH_CELL from "./statFaultDetailByDay/N2S_PATH_CELL";
import ROBOT_COUNT from "./jobSnapshot/ROBOT_COUNT";
import ROBOT_WORK from "./jobSnapshot/ROBOT_WORK";
import P40_WORK_PR from "./jobSnapshot/P40_WORK_PR";
import taskCompletionRate from "./jobSplitCount/taskCompletionRate";
import UtilizationTrends from "./jobSnapshot/UtilizationTrends";
/**
 * 这是一个dashboard 的测试模板
 */
export default [
  { ...doneJobCount, x: 0,y: 0, width: 7, height: 5},
  { ...totalStationDone, x: 7,y: 0, width: 7, height: 5},
  { ...taskCompletionRate, x: 14,y: 0, width: 10, height: 5},
  { ...ROBOT_COUNT, x: 24, y: 0, width: 7, height: 5},
  { ...ROBOT_WORK, x: 31, y: 0, width: 7, height: 5},
  { ...P40_WORK_PR, x: 38, y: 0, width: 10, height: 5, isChartFilter: false },
  { ...totalReceive, x: 0, y: 5, width: '50%', height: 9, isChartFilter: true },
  { ...UtilizationTrends, x: 24, y: 5, width: '50%', height: 9, isChartFilter: true },
  { ...splitStationDoneGroupByStation, x: 0, y: 14, width: '100%', height: 9, isChartFilter: true },
  // receiveJobCount,
  // groundMissingCount,
  // totalReceive,
  // splitReceiveGroupByStation,
  // JobDoneCost,
  // p40StayStationCost,
  // rsJobStartMoveToDestLatticeCostHigh,
  // rmsJobReceiveToP40StartMove,
  // rmsJobBoxArriveTransferToP40StartMove,
  // p40ToStationJobStartMoveToStation,
  // p40StationBackStartMoveToDestLattice,
  // N2S_PATH_CELL,
  // groundMissingCount
]