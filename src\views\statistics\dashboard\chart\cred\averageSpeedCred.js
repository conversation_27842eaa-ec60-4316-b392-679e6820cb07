import Chart, { requestCache } from "../common";

/**
 * 2.3.5路径运行平均速度
 */
export default class AverageSpeedCred extends Chart {
  /**
   * 初始化图表 - 2.3.5路径运行平均速度
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('line', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "路径运行平均速度";
    this.isFilterParams = option.isFilterParams || true;
    this.filterList = option.filterList || ['startTime', 'endTime', 'statType'];
  }

  async request(params) {
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('	/athena/path/plan/stat/sumByDayList', {
      startTime: new Date().setHours(0, 0, 0, 0),
      endTime: new Date().setHours(23, 59, 59, 0),
      statType: 'PATH_AVERAGE_SPEED',
      ...params
    })
    
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const robotSpeed = 0;
    const xAxis = data.xAxis;

    // 机器人类型
    const robotTypeKeys = Object.keys(data || {});

    robotTypeKeys.forEach(robotType => {
      // 机器人任务类型
      const curRobotSpeed = 0;

      if (robotType !== 'DEFAULT') {
        const robotTypeItem = data.robotType2Data[robotType];
        Object.keys(robotTypeItem).forEach(robotAction => {
          // 机器人任务动作

          if (robotAction !== 'DEFAULT') {
            const robotActionItem = robotTypeItem[robotAction];
            Object.keys(robotActionItem).forEach(instruct => {
              // 动作指令

              if (instruct !== 'DEFAULT') {
                const instructItem = robotActionItem[instruct]
                Object.keys(instructItem).forEach(xData => {
                  curRobotSpeed += xData;
                })
              }
            })
          }
        })
      }

      curRobotSpeed = curRobotSpeed / xAxis.length;
      robotSpeed += curRobotSpeed;
    });

    robotSpeed = robotSpeed / robotTypeKeys.length;
    return {
      id: 'averageSpeedCred',
      title: this.title || '',
      number: robotSpeed,
      append: '米/秒',
      color: "#8543e0",
    }
  }
}