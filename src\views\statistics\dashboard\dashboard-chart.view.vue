<template>
  <div class="dashboard-chart" :style="curChartStyle">
    <template v-if="chartType === 'cred'">
      <number-item :option="chartData" />
    </template>
    <template v-else-if="chartType === 'table'">
      <table-item :option="chartData" />
    </template>
    <template v-else-if="chartType === 'annular'">
      <annular-item :option="chartData" />
    </template>
    <template v-else>
      <ChartItem :option="chartData" :isFilter="option.isChartFilter" @filter="filter" />
    </template>
  </div>
</template>

<script>
import dashboardHandler, { requestdata } from '../chart/dashboard'
import NumberItem from "../components/common/number-item.vue";
import ChartItem from "../components/common/chart-item.vue";
import TableItem from "../components/common/table-item.vue";
import AnnularItem from "../components/common/chart-annular.vue"

export default {
  name: "dashboard",
  props: {
    option: {
      type: Object,
      default: () => { },
    },
    gridWidth: {
      type: Number,
      default: 100,
    },
    disabled: {
      type: Boolean,
      default: true,
    },
  },
  components: { NumberItem, ChartItem, TableItem, AnnularItem },
  data() {
    return {
      isDataLoad: true,
      chartData: null,
      pollingTimer: null,
      curFilterData: {},
    };
  },
  computed: {
    chartType() {
      return this.option?.type || '';
    },
    curChartStyle() {
      const { width, height } = this.option;
      const { gridWidth } = this;
      let curWidth = width;
      let curHeight = height;
      if (typeof width === 'number') curWidth = `${parseInt(width * gridWidth) - 1}px`;
      if (typeof height === 'number') curHeight = `${parseInt(height * gridWidth)}px`;
      return { width: curWidth, height: curHeight }
    },
  },
  watch: {},
  async created() {
    const { option } = this;
    const data = await option.request();
    this.chartData = await option.handler(data);
  },

  mounted() {
    const { option } = this;
    const intervalTimer = option.intervalTimer;
    if (intervalTimer) {
      this.pollingTimer = setInterval(async () => {
        const data = await option.request(await option.requestChartData(this.curFilterData));
        this.chartData = await option.handler(data);
      }, intervalTimer);
    }
  },

  destroyed() {
    clearInterval(this.pollingTimer);
  },

  methods: {
    async filter(filterData) {
      this.curFilterData = filterData || {};
      const { option } = this;
      const data = await option.request(await option.requestChartData(this.curFilterData));
      this.chartData = await option.handler(data);
    },
  },
};
</script>

<style lang="less" scoped>
.dashboard-chart {
  display: inline-block;
  overflow: hidden;
  padding: 5px;
}
</style>

<style lang="less">
.dashboard-chart {
  .content-container {
    overflow: hidden;
    padding: 5px;
  }

  &.vdr.active:before {
    outline: none;
  }
}
</style>
