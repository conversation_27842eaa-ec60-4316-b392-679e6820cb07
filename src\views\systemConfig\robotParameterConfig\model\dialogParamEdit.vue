<template>
  <el-dialog
    :title="$t('lang.rms.fed.edit')"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="onClose"
    width="70%"
  >
    <el-form label-position="top" label-width="80px" :model="mainTableRowData">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item :label="$t('lang.rms.fed.nameOfParameter')">
            <el-input :value="mainTableRowData.paramCode" :disabled="true" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item :label="$t('lang.rms.fed.robotParamValueType')">
            <el-input
              :value="paramValueTypeList[mainTableRowData.paramValueType]"
              class="w_100x"
              :disabled="true"
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item :label="$t('lang.rms.fed.robotType')">
            <el-input v-model="mainTableRowData.robotType" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item :label="$t('lang.rms.fed.robotParamValueLimit')">
            <el-input v-model="mainTableRowData.paramLimit" />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item :label="$t('lang.rms.fed.robotParamUnit')">
            <el-input v-model="mainTableRowData.unit" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="$t('lang.rms.fed.describe')">
            <el-input v-model="mainTableRowData.descr" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="onClose">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">{{ $t("lang.rms.fed.save") }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      dialogVisible: false,
      paramValueTypeList: {
        0: this.$t("lang.rms.fed.robotParamValueTypeDigit"),
        1: this.$t("lang.rms.fed.robotParamValueTypeBool"),
        2: this.$t("lang.rms.fed.robotParamValueTypeString"),
        3: this.$t("lang.rms.fed.robotParamValueTypeJson"),
      },
      mainTableRowData: {},
    };
  },
  methods: {
    open(data) {
      this.mainTableRowData = data;
      this.dialogVisible = true;
    },
    onClose() {
      this.mainTableRowData = {};
      this.dialogVisible = false;
      // this.$emit("updateMainTale");
    },
    save() {
      
      this.dialogVisible = false;
      this.$emit("save", this.mainTableRowData);
      this.mainTableRowData = {};
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped></style>
