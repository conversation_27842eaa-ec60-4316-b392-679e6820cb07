<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2021-12-27 14:30:15
 * @Description:
-->
<template>
  <el-form
    ref="createForm"
    :model="modelData"
    class="creat-form"
    label-position="right"
    label-width="160px"
    :disabled="!editProp"
    :rules="rules"
    :validate-on-rule-change="false"
  >
    <div class="form-group-title division">
      {{ $t("lang.rms.api.result.warehouse.baseProperies") }}:
    </div>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.ontologyName')" prop="name">
      <el-input
        v-model="modelData.name"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterOntologyName')"
      />
    </el-form-item>
    <!-- <el-form-item :label="$t('lang.rms.api.result.warehouse.bodyNo')" prop="chassisCode">
      <el-input
        v-model="modelData.chassisCode"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterTheBodyNum')"
      />
    </el-form-item> -->

    <el-form-item :label="$t('lang.rms.api.result.warehouse.series')" prop="series">
      <el-select v-model="modelData.series" :placeholder="$t('lang.rms.fed.choose')">
        <el-option
          v-for="item in (dictionary || {}).ROBOT_SERIES || {}"
          :key="item.fieldCode"
          :label="item.fieldCode"
          :value="item.fieldValue"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotOntologyModel')" prop="file">
      <el-upload
        v-if="!modalUrl"
        action="javascript:void(0)"
        accept=".glb, .gltf"
        drag
        :limit="1"
        :before-upload="beforeUpload"
        :on-change="handleUploadBefore"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">{{ $t("lang.rms.api.result.warehouse.dragFileHere") }}</div>
        <div slot="tip" class="el-upload__tip">
          {{ $t("lang.rms.api.result.warehouse.uploadOntologyModel") }}
        </div>
      </el-upload>
      <ModalRenderer v-if="modalUrl" :modal-url="modalUrl" />
      <i
        v-if="modalUrl && editProp"
        style="cursor: pointer"
        class="el-icon-delete"
        @click="uploadClear()"
      />
    </el-form-item>
    <div class="form-group-title division">
      {{ $t("lang.rms.api.result.warehouse.physicalProperty") }}:
    </div>
    <el-form-item :label="$t('lang.rms.fed.length')" prop="length">
      <el-input
        v-model="modelData.length"
        type="number"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterLengthOfModel')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.fed.width')" prop="width">
      <el-input
        v-model="modelData.width"
        type="number"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterModelWidth')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.fed.high')" prop="height">
      <el-input
        v-model="modelData.height"
        type="number"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterModelHeight')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.rotationDiameter')" prop="diameter">
      <el-input
        v-model="modelData.diameter"
        type="number"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterRotationDiameter')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.bodyWeight')" prop="weight">
      <el-input
        v-model="modelData.weight"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterBodyWeight')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.xDirection')" prop="headOffsetRatio">
      <el-input
        v-model="modelData.headOffsetRatio"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterXscale')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.yDirection')" prop="tailOffsetRatio">
      <el-input
        v-model="modelData.tailOffsetRatio"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterYScale')"
      />
    </el-form-item>
    <div class="form-group-title division">
      {{ $t("lang.rms.api.result.warehouse.motionAttr") }}:
    </div>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.navigationMode')"
      prop="navigationModes"
    >
      <el-select
        v-model="modelData.navigationModes"
        multiple
        :placeholder="$t('lang.rms.fed.choose')"
      >
        <el-option
          v-for="item in dictionary.NAVIGATION_MODE"
          :key="item.fieldCode"
          :label="
            item.fieldCode === '0'
              ? $t('lang.rms.fed.navigation.slam')
              : $t('lang.rms.fed.navigation.qr')
          "
          :value="item.fieldCode"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.maxDrivingSpeed')" prop="maxVelocity">
      <el-input
        v-model="modelData.maxVelocity"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMaxDrivingSpeed')"
      />
    </el-form-item>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.maxTuringSpeed')"
      prop="maxAngularVelocity"
    >
      <el-input
        v-model="modelData.maxAngularVelocity"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMaxTurnSpeed')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.fed.robotParamConfig.maxAcc')" prop="maxAcceleration">
      <el-input
        v-model="modelData.maxAcceleration"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMaxAcceleration')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.moveMode')" prop="movingModes">
      <el-select
        v-model="modelData.movingModes"
        multiple
        :placeholder="$t('lang.rms.fed.pleaseChoose')"
      >
        <el-option
          v-for="item in dictionary.ROBOT_MOTION_MODE"
          :key="item.fieldCode"
          :label="item.fieldCode"
          :value="item.fieldValue"
        />
      </el-select>
    </el-form-item>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.maxDistanceError')"
      prop="maxPositionError"
    >
      <el-input
        v-model="modelData.maxPositionError"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMaxDistanceError')"
      />
    </el-form-item>
    <div class="form-group-title division">
      {{ $t("lang.rms.api.result.warehouse.powerProperties") }}:
    </div>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.batteryType')" prop="batteryType">
      <el-select v-model="modelData.batteryType" :placeholder="$t('lang.rms.fed.choose')">
        <el-option :label="$t('lang.rms.fed.battery.lto')" value="0" />
        <el-option :label="$t('lang.rms.fed.battery.lfpo')" value="1" />
        <el-option :label="$t('lang.rms.api.result.warehouse.LithiumTernary')" value="2" />
      </el-select>
    </el-form-item>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.batteryMaxMah')"
      prop="maxBatteryCapacity"
    >
      <el-input
        v-model="modelData.maxBatteryCapacity"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterMaxBatteryMah')"
      />
    </el-form-item>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.workingPowerConsumption')"
      prop="workingTimePerPercent"
    >
      <el-input
        v-model="modelData.workingTimePerPercent"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterWorkAh')"
      />
    </el-form-item>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.idlePowerConsumption')"
      prop="idleTimePerPercent"
    >
      <el-input
        v-model="modelData.idleTimePerPercent"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterIdleAh')"
      />
    </el-form-item>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.sleepPowerConsumption')"
      prop="sleepingTimePerPercent"
    >
      <el-input
        v-model="modelData.sleepingTimePerPercent"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterSleepAh')"
      />
    </el-form-item>
  </el-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import { mapState } from "vuex";
import ModalRenderer from "../../../../Components/ModalRenderer";

export default {
  name: "ModelCreateForm",
  // import引入的组件需要注入到对象中才能使用
  components: { ModalRenderer },
  props: {
    slopProps: {
      type: Object,
      default() {
        return {};
      },
    },
    editProp: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    // 这里存放数据
    return {
      modelData: {
        file: this.slopProps.chassisImage,
        navigationModes: [],
        movingModes: [],
        batteryType: "2",
        ...this.slopProps,
      },
      tempLocalCoverArray: [],
      modalUrl: this.slopProps.chassisImage || "",
    };
  },
  // 监听属性 类似于data概念
  computed: {
    ...mapState(["dictionary"]),
    rules() {
      return {
        // chassisCode: [
        //   {
        //     required: true,
        //     message: this.$t("lang.rms.api.result.warehouse.pleaseEnterTheBodyNum"),
        //     trigger: "blur",
        //   }, // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        // ],
        name: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterOntologyName"),
            trigger: "blur",
          }, // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        series: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.enterProductSerials"),
            trigger: "change",
          }, // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        length: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.enterOntologyModelLength"),
            trigger: "blur",
          },
          { transform: value => String(value) },
          // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        width: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterOntologyWidth"),
            trigger: "blur",
          },
          { transform: value => String(value) },
          // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        height: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterOntologyHeight"),
            trigger: "blur",
          },
          { transform: value => String(value) },
          // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        weight: [
          // {
          //   required: true,
          //   message: this.$t("lang.rms.api.result.warehouse.pleaseEnterRobotBodyWeight"),
          //   trigger: "blur",
          // },
          { transform: value => String(value) },
        ],
        headOffsetRatio: [{ transform: value => String(value) }],
        tailOffsetRatio: [{ transform: value => String(value) }],
        file: [
          // {
          //   required: true,
          //   message: this.$t("lang.rms.api.result.warehouse.chooseRobotMechanismModel"),
          //   trigger: "change",
          // },
          // {
          //   validator: this.fileValidator,
          //   message: this.$t("lang.rms.api.result.warehouse.pleaseSelectLocalModeFile"),
          //   trigger: "blur",
          // },
        ],
        navigationModes: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseSelectNavMode"),
            trigger: ["blur", "change"],
          },
        ],
        maxVelocity: [{ transform: value => String(value) }],
        maxAngularVelocity: [{ transform: value => String(value) }],
        maxAcceleration: [{ transform: value => String(value) }],
        movingModes: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseSelectMovMethod"),
            trigger: ["blur", "change"],
          },
        ],
        maxPositionError: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMaxDistanceError"),
            trigger: "blur",
          },
          { transform: value => String(value) },
          // { min: 1, max: 60, message: '长度在 1 到 60 个字符', trigger: 'blur' }
        ],
        maxBatteryCapacity: [{ transform: value => String(value) }],
        workingTimePerPercent: [{ transform: value => String(value) }],
        idleTimePerPercent: [{ transform: value => String(value) }],
        sleepingTimePerPercent: [{ transform: value => String(value) }],
      };
    },
  },
  // 监控data中的数据变化
  watch: {
    slopProps(newObj) {
      this.modelData = { file: newObj.chassisImage, ...newObj };
    },
  },
  // 方法集合
  methods: {
    fileValidator(rule, value, callback) {
      if (value) {
        return callback();
      }

      return callback(
        new Error(this.$t("lang.rms.api.result.warehouse.pleaseSelectLocalModeFile")),
      );
    },
    getFormValues() {
      const $form = this.$refs["createForm"];
      return $form.validate();
    },
    resetFormValues() {
      this.$refs["createForm"].resetFields();
    },
    beforeUpload() {
      return false;
    },
    handleUploadBefore(file) {
      if (file.size >= 52428800) {
        this.$message({
          type: "error",
          message: "文件多大，请上传50M内文件",
        });
        return;
      }

      this.modalUrl = URL.createObjectURL(file.raw);
      const demoData = new FormData();
      demoData.append("file", file.raw);
      this.modelData.file = demoData.get("file");

      this.$refs["createForm"].validateField("file");
    },
    uploadClear() {
      this.modalUrl = "";
      this.modelData.file = "";
      this.$forceUpdate();
    },
  },
};
</script>
<style lang="scss" scoped>
.el-upload__tip {
  margin-top: 0px;
}
.creat-form .el-input,
.creat-form .el-select {
  width: 100%;
}
.form-group-title {
  font-size: 16px;
  font-weight: bold;
  margin: 3px auto;
  text-align: left;
}
.division {
  margin-bottom: 20px;
}
</style>
