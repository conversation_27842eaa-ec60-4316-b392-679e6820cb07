<template>
  <el-menu
    :default-active="activeNavId || navList[0].id"
    mode="horizontal"
    class="geek-tabs-menu"
    @select="handleSelect"
  >
    <el-menu-item v-for="item in navList" :index="item.id" :key="item.id" v-show="!item.hide">
      {{ $t(item.text) }}
    </el-menu-item>
  </el-menu>
</template>

<script>
export default {
  name: "GeekTabsNav",
  props: {
    navList: {
      type: Array,
      require: true,
    },
    activeNavId: {
      type: String,
      default: "",
    },
  },
  methods: {
    handleSelect(type) {
      this.$emit("select", type);
    },
  },
};
</script>

<style lang="less" scoped></style>
