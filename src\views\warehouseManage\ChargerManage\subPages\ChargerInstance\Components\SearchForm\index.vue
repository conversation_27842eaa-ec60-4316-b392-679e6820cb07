<template>
  <el-form
    ref="searchForm"
    :inline="true"
    class="demo-form-inline"
    label-position="top"
    :model="searchForm"
  >
    <el-form-item :label="$t('lang.rms.web.charger.chargerId')">
      <el-input
        v-model="searchForm.chargerId"
        :placeholder="$t('lang.rms.web.charger.chargerId')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.web.charger.type')">
      <el-input
        v-model="searchForm.type"
        :placeholder="$t('lang.rms.web.charger.type')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.web.charger.hostCode')">
      <el-input
        v-model="searchForm.hostCode"
        :placeholder="$t('lang.rms.web.charger.hostCode')"
      />
    </el-form-item>
    <!-- <el-form-item :label="$t('lang.rms.web.charger.protocol')">
      <el-select
        v-model="searchForm.protocol"
        :style="{ width: '120px' }"
        :placeholder="$t('lang.rms.fed.pleaseChoose')"
      >
        <el-option v-for="item in protocols" :key="item" :label="item" :value="item" />
      </el-select>
    </el-form-item> -->
    <el-form-item :label="$t('lang.rms.web.charger.interactive')">
      <el-select
        v-model="searchForm.interactiveModel"
        :style="{ width: '120px' }"
        :placeholder="$t('lang.rms.fed.pleaseChoose')"
      >
        <el-option v-for="(label, key) in models" :key="key" :label="$t(label)" :value="key" />
      </el-select>
    </el-form-item>
    <el-form-item class="align-bottom">
      <el-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.query") }}</el-button>
      <el-button type="primary" @click="resetForm">{{ $t("lang.rms.fed.reset") }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import chargerManageRequest from "@/api/chargerManage";

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    // 这里存放数据
    return {
      searchForm: {
        chargerId: "",
        type: "",
        hostCode: "",
        protocol: "",
        interactiveModel: "",
      },
      protocols: {},
      models: [],
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  created: function () {
    this.loadSearchInfo();
  },
  // 方法集合
  methods: {
    onSubmit() {
      this.$emit("onsubmit", { ...this.searchForm });
    },
    resetForm() {
      this.searchForm = {
        chargerId: "",
        type: "",
        hostCode: "",
        protocol: "",
        interactiveModel: "",
      };
      this.$emit("onsubmit", { ...this.searchForm });
    },
    loadSearchInfo() {
      chargerManageRequest.getProtocol().then(({ data }) => {
        this.protocols = data;
      });
      chargerManageRequest.getInteractiveModel().then(({ data }) => {
        this.models = data;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.align-bottom {
  vertical-align: bottom;
}
</style>
