# npm audit report

d3-color  <3.1.0
Severity: high
d3-color vulnerable to ReDoS - https://github.com/advisories/GHSA-36jr-mh4h-2g58
fix available via `npm audit fix --force`
Will install element-geek@1.0.0, which is a breaking change
node_modules/d3-color
  d3  4.0.0-alpha.1 - 6.7.0
  Depends on vulnerable versions of d3-color
  node_modules/d3
    @antv/g6  2.0.0-alpha - 3.2.10
    Depends on vulnerable versions of @antv/g
    Depends on vulnerable versions of d3
    node_modules/@antv/g6
      element-geek  >=1.0.1
      Depends on vulnerable versions of @antv/g6
      node_modules/element-geek
  d3-interpolate  0.1.3 - 2.0.1
  Depends on vulnerable versions of d3-color
  node_modules/d3-interpolate
    @antv/g  <=3.5.0-beta.6
    Depends on vulnerable versions of d3-interpolate
    node_modules/@antv/g
    d3-brush  0.1.0 - 2.1.0
    Depends on vulnerable versions of d3-interpolate
    node_modules/d3-brush
    d3-scale  0.1.5 - 3.3.0
    Depends on vulnerable versions of d3-interpolate
    node_modules/d3-scale
    d3-scale-chromatic  0.1.0 - 2.0.0
    Depends on vulnerable versions of d3-color
    Depends on vulnerable versions of d3-interpolate
    node_modules/d3-scale-chromatic
    d3-transition  0.0.7 - 2.0.0
    Depends on vulnerable versions of d3-color
    Depends on vulnerable versions of d3-interpolate
    node_modules/d3-transition
    d3-zoom  0.0.2 - 2.0.0
    Depends on vulnerable versions of d3-interpolate
    node_modules/d3-zoom

nth-check  <2.0.1
Severity: high
Inefficient Regular Expression Complexity in nth-check - https://github.com/advisories/GHSA-rp65-9cf3-cjxr
fix available via `npm audit fix`
node_modules/nth-check
  css-select  <=3.1.0
  Depends on vulnerable versions of nth-check
  node_modules/css-select
    cheerio  0.19.0 - 1.0.0-rc.3
    Depends on vulnerable versions of css-select
    node_modules/cheerio

14 high severity vulnerabilities

To address issues that do not require attention, run:
  npm audit fix

To address all issues (including breaking changes), run:
  npm audit fix --force
