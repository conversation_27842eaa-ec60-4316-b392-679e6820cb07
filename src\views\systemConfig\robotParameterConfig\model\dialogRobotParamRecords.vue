<template>
  <el-dialog
    :title="$t('lang.rms.fed.robotParamRecord')"
    :visible.sync="dialogRobotParamRecordsVisible"
    :before-close="closeDialog"
    width="70%"
    center
  >
    <dialogRobotParamRecordEdit
      v-if="dialogRobotParamRecordEditVisible"
      :dialog-robot-param-record-edit-visible.sync="dialogRobotParamRecordEditVisible"
      :item-data="recordItemData"
      :is-edit="isEdit"
      @save="save"
    />
    <div class="app-container">
      <el-card>
        <el-button
          type="primary"
          @click="itemClick({ configId: itemData.id, paramCode: itemData.paramCode }, false)"
        >
          {{ $t("lang.rms.fed.add") }}
        </el-button>
      </el-card>
      <el-card class="mt-20">
        <!-- 列表信息 -->
        <el-table :data="tableData" style="width: 100%">
          <el-table-column
            :label="$t('lang.rms.fed.lineNumber')"
            width="50"
            align="center"
            :formatter="formatIndex"
          />
          <!--机器人ID-->
          <el-table-column prop="robotId" :label="$t('lang.rms.fed.robotId')" align="center" />
          <!-- 参数值 -->
          <el-table-column
            prop="paramValue"
            :label="$t('lang.rms.fed.robotParamValue')"
            align="center"
          />
          <!--创建时间-->
          <el-table-column prop="createTime" :label="$t('lang.rms.fed.createTime')" align="center">
            <template slot-scope="scope">{{ setTime(scope.row.createTime) }}</template>
          </el-table-column>
          <!--创建人-->
          <el-table-column
            prop="createUser"
            :label="$t('lang.rms.fed.createUser')"
            align="center"
          />
          <!--更新时间-->
          <el-table-column prop="updateTime" :label="$t('lang.rms.fed.updateTime')" align="center">
            <template slot-scope="scope">{{ setTime(scope.row.updateTime) }}</template>
          </el-table-column>
          <!--更新人-->
          <el-table-column
            prop="updateUser"
            :label="$t('lang.rms.fed.updateUser')"
            align="center"
          />
          <el-table-column :label="$t('lang.rms.fed.operation')" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="itemClick(scope.row, true)">
                {{ $t("lang.rms.fed.edit") }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageOption.pagecurrent"
          :page-sizes="[10, 25, 50, 100]"
          :page-size="pageOption.pagesize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="taskCount"
          @size-change="pageSizeChange"
          @current-change="goPage"
        />
      </el-card>
    </div>
  </el-dialog>
</template>
<script>
import dialogRobotParamRecordEdit from "./dialogRobotParamRecordEdit";
export default {
  name: "RobotParamRecords",
  props: {
    dialogRobotParamRecordsVisible: {
      type: Boolean,
      default() {
        return false;
      },
    },
    itemData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      // 搜索内容
      searchData: {
        descriptionChKey: "",
        paramCodeKey: "",
        robotTypeKey: "",
      },
      dialogRobotParamRecordEditVisible: false,
      // 设置table的数据
      tableData: [],
      // 当前数据总数
      taskCount: 0,
      pageOption: {
        // 当前页数
        pagecurrent: 1,
        // 每页展示数
        pagesize: 10,
      },
      isEdit: true, // true-编辑 false-新增
      recordItemData: {},
    };
  },
  computed: {},
  created() {
    this.search();
  },
  methods: {
    // 关闭弹出
    closeDialog() {
      this.$emit("update:dialogRobotParamRecordsVisible", false);
    },
    /* 格式化内容 */
    formatterTags(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue.join(",");
      } else {
        return cellValue;
      }
    },

    formatImmediate(row, column, cellValue, index) {
      const { immediateList } = this;
      for (let index = immediateList.length - 1; index >= 0; index -= 1) {
        if (immediateList[index].key === cellValue + "") {
          return immediateList[index].value;
        }
      }
      return cellValue;
    },

    formatIndex(row, column, cellValue, index) {
      return ((this.pageOption.pagecurrent - 1) * this.pageOption.pagesize || 0) + index + 1;
    },

    /* 翻页 */
    pageSizeChange(data) {
      this.pageOption.pagecurrent = 1;
      this.pageOption.pagesize = data;
      this.search();
    },
    // 跳页
    goPage(pageCurrent) {
      this.pageOption.pagecurrent = pageCurrent;
      this.search();
    },
    // 重置搜索参数
    resetSearchData() {
      for (const key in this.searchData) {
        if (this.searchData.hasOwnProperty(key)) {
          this.searchData[key] = "";
        }
      }
      this.searchData.tags = [];
      this.search();
    },

    // 查询
    search() {
      /*   const { searchData } = this
           const { descriptionChKey, paramCodeKey, robotTypeKey } = searchData*/
      const data = { language: localStorage.getItem("curRMSLanguage") || "zh_cn" };
      data.paramConfigId = this.itemData.id;
      const pageData = {
        pageSize: this.pageOption.pagesize,
        currentPage: this.pageOption.pagecurrent || 1,
      };

      $req
        .post(
          "/athena/robot/paramConfig/robotRecordPageList",
          $utils.Tools.getParams(pageData),
          data,
        )
        .then((data = {}) => {
          const { currentPage = 0, pageSize = 0, recordCount = 0, recordList = [] } =
            data.data || {};
          this.tableData = recordList.map(item => {
            const descr = this.$t(item.descr);
            return { ...item, descr };
          });
          // id
          this.pageOption.pagesize = pageSize;
          this.pageOption.pagecurrent = currentPage;
          this.taskCount = recordCount;
        });
    },

    // 保存
    save(data) {
      $req.post("/athena/robot/paramConfig/saveRobotRecord", data).then(json => {
        if (json.code === 0) {
          this.search();
        }
      });
    },
    // 编辑
    itemClick(data, isEdit) {
      this.recordItemData = data;
      this.dialogRobotParamRecordEditVisible = true;
      this.isEdit = isEdit;
    },
    setTime(value) {
      if (!value) {
        return null;
      }
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss");
    },
  },
  components: {
    dialogRobotParamRecordEdit,
  },
};
</script>
<style scoped>
.mt-20 {
  margin-top: 20px;
}

.w_100x {
  width: 100%;
}

.btnwarp {
  padding: 43px 0 0;
}

.floor {
  height: 50px;
}

.floor > .page {
  float: left;
  line-height: 50px;
}

.floor > .jump {
  float: right;
  line-height: 50px;
}
</style>
