<template>
  <section>
    <div class="table-content">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="languageName" :label="$t('lang.rms.fed.languageName')" />
        <el-table-column prop="languageCode" :label="$t('lang.rms.fed.languageField')" />
        <el-table-column prop="createTime" :label="$t('lang.rms.fed.uploadTime')" />
        <el-table-column prop="updateTime" :label="$t('lang.rms.fed.updateTime')" />
        <el-table-column fixed="right" :label="$t('lang.rms.fed.operation')">
          <template slot-scope="scope">
            <el-button v-if="!isRoleGuest" type="text" @click="handleDel(scope.row)">
              {{ $t("lang.rms.fed.delete") }}
            </el-button>
            <el-button type="text" @click="handleExport(scope.row)">
              {{ $t("lang.rms.fed.buttonExport") }}
            </el-button>
            <el-button v-if="!isRoleGuest" type="text" @click="handleApply(scope.row)">
              {{ $t("lang.rms.fed.application") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </section>
</template>

<script>
/**
 * lang.rms.fed.languageName:"语言名称"
 * lang.rms.fed.languageField:"语言字段"
 * lang.rms.fed.uploadTime:"上传时间"
 */

export default {
  name: "LanguageList",
  props: {
    tableData: Array,
  },
  data() {
    return {
      pageCount: 0,
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  activated() {
    this.$emit("getTableList");
  },
  mounted() {
    console.log(this.tableData);
  },
  methods: {
    handleDel(rowData) {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete")).then(() => {
        $req
          .postParams("/athena/api/coreresource/i18n/deleteLanguage", {
            languageCode: rowData.languageCode,
          })
          .then(res => {
            if (res.code === 0) {
              this.$success(this.$t("lang.rms.api.result.ok"));
              this.$emit("getTableList");
            }
          });
      });
    },

    // 应用
    handleApply(rowData) {
      $req
        .postParams("/athena/api/coreresource/i18n/applyLanguage", {
          languageCode: rowData.languageCode,
        })
        .then(res => {
          if (res.code === 0) {
            this.$success(this.$t("lang.rms.api.result.ok"));
            $utils.Data.setLocalLang(rowData.languageCode);
            this.$emit("getTableList");
          }
        });
    },

    // 导出
    handleExport(rowData) {
      $req
        .postParams("/athena/api/coreresource/i18n/exportI18nItem", {
          languageCode: rowData.languageCode,
        })
        .then(res => {
          if (res.code === 0) {
            // let a = document.createElement('a');
            // a.style.display = 'none';
            // a.href = res.data;
            // document.body.append(a);
            // a.click();
            // document.body.removeChild(a);
            if (window.location.host == "127.0.0.1" || window.location.host == "localhost") {
              window.open("http://" + process.env.VUE_APP_serverIP + res.data);
            } else {
              window.open(window.location.origin + res.data);
            }
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
.table-content {
  box-shadow: 0px -5px 10px -5px rgb(0 0 0 / 10%);

  .btn-opt {
    padding: 3px 5px;
    min-height: 10px;
  }
}
</style>
