
<template>
  <el-form
    ref="searchForm"
    :inline="true"
    class="demo-form-inline"
    label-position="top"
    :model="searchForm"
  >
    <el-form-item :label="$t('lang.rms.web.station.stationId')">
      <el-input v-model="searchForm.stationId" :placeholder="$t('lang.rms.web.station.stationIdPlaceHolder')" />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.fed.hostCode')">
      <el-input v-model="searchForm.hostCode" :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterExternalCode')" />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.web.station.stationType')">
      <el-select
        v-model="searchForm.type"
        :style="{ width:'120px'}"
        :placeholder="$t('lang.rms.fed.pleaseChoose')"
      >
        <el-option
          v-for="(label,key) in stationTypes"
          :key="key"
          :label="$t(label)"
          :value="key"
        />
      </el-select>
    </el-form-item>
    <el-form-item class="align-bottom">
      <el-button type="primary" @click="onSubmit">{{ $t('lang.rms.fed.query') }}</el-button>
      <el-button type="primary" @click="resetForm">{{ $t('lang.rms.fed.reset') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import stationManageRequest from '@/api/stationManage'

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    // 这里存放数据
    return {
      searchForm: {
        stationId: '', hostCode: '', type: ''
      },
      stationTypes: {},
      maps: []
    }
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  created: function() {
    this.loadSearchInfo()
  },
  // 方法集合
  methods: {
    onSubmit() {
      this.$emit('onsubmit', { ...this.searchForm })
    },
    resetForm() {
      this.searchForm = {
        stationId: '', hostCode: '', type: ''
      }
      this.$emit('onsubmit', { ...this.searchForm })
    },
    loadSearchInfo() {
      stationManageRequest.getStationType().then(({ data }) => {
        this.stationTypes = data
      })
    }
  }
}
</script>
<style lang='scss' scoped>
.align-bottom {
  vertical-align:bottom;
}
</style>
