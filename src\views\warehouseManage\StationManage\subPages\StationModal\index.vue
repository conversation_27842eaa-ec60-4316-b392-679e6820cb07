<template>
  <div>
    <SearchWrap>
      <SearchForm @onsubmit="onSubmit" />
    </SearchWrap>
    <el-table
      v-loading="loading"
      :data="recordList"
      style="width: 100%"
    >
      <el-table-column :label="$t('lang.rms.web.stationModel.modelId')" prop="modelId" />
      <el-table-column :label="$t('lang.rms.web.stationModel.name')" prop="name" />
      <el-table-column :label="$t('lang.rms.web.station.stationType')" prop="typeDesc">
        <template slot-scope="scope">
          {{ $t(scope.row.typeDesc) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('lang.rms.web.stationModel.layout')" prop="layoutDesc">
        <template slot-scope="scope">
          {{ $t(scope.row.layoutDesc) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('lang.rms.web.stationModel.maxQueueNumber')" prop="maxRobotQueueSize" />
    </el-table>

    <div style="text-align: right; margin-top: 30px">
      <el-pagination
        background
        layout="total,prev, pager, next, sizes, jumper"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="paginationParams.pageSize"
        :total="paginationParams.total"
        @current-change="paginationChange"
        @size-change="paginationChange"
      />
    </div>
  </div>
</template>

<script>
import SearchWrap from '../../Components/SearchWrap'
import SearchForm from './Components/SearchForm'
import stationManageRequest from '@/api/stationManage'

export default {
  name: 'StationModal',
  components: {
    SearchWrap,
    SearchForm
  },
  data() {
    return {
      slopProps: {},
      showCreatDialog: false,
      demo: '123',
      ruleForm: {
        user: '',
        region: ''
      },
      rules: {
        user: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },
          { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
        ],
        region: [
          { required: true, message: '请选择活动区域', trigger: 'change' }
        ]
      },
      loading: false,
      recordList: [{}],
      paginationParams: { pageSize: 10, currentPage: 1, total: 0 },
      searchFormData: {}
    }
  },
  computed: {},
  watch: {},
  created: function() {
    this.paginationChange(1)
  },
  methods: {
    onSubmit(searchFormData) {
      console.log(searchFormData)
      const { paginationParams } = this
      this.searchFormData = searchFormData

      return this.reqTableList(searchFormData, paginationParams)
    },
    reqTableList(searchFormData, paginationParams) {
      this.loading = true
      stationManageRequest.getStationModalPageList(Object.assign(searchFormData, paginationParams)).then(({ data }) => {
        const { pageSize, currentPage, recordList, recordCount } = data
        this.recordList = recordList
        this.paginationParams = {
          pageSize,
          currentPage,
          total: recordCount
        }
        this.loading = false
      })
    },
    paginationChange(currentPage) {
      this.paginationParams.currentPage = currentPage
      this.reqTableList(this.searchFormData, this.paginationParams)
    }
  }
}
</script>
<style lang='scss' scoped>
.align-bottom {
  vertical-align:bottom;
}
</style>
