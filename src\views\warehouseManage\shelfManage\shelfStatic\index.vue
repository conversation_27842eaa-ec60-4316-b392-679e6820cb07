<template>
  <geek-main-structure class="shelf-static">
    <h3 class="page-title">{{ $t("lang.rms.fed.shelfStaticAdjustPageTitle") }}</h3>
    <section class="sec-con">
      <div class="sec-card">
        <el-card>
          <div slot="header">
            <span class="card-title">
              {{ $t("lang.rms.fed.shelfStaticAdjustPageLogicLimitNew") }}
            </span>
            <el-select
              v-model="areaValue"
              multiple
              value-key="areaId"
              :placeholder="$t('lang.rms.fed.pleaseChoose')"
            >
              <el-option
                v-for="item in areaOptions"
                :key="item.areaId"
                :label="item.areaName"
                :value="item"
              />
            </el-select>
          </div>
          <div v-html="logicAreaResult" />
        </el-card>
        <el-card>
          <div slot="header">
            <span class="card-title">
              {{ $t("lang.rms.fed.shelfStaticAdjustPageConfirmAdjustNew") }}
            </span>
          </div>
          <div>
            <el-radio v-model="radio" label="1">
              {{ $t("lang.rms.fed.shelfStaticAdjustPagediffArea") }}
            </el-radio>
            <el-radio v-model="radio" label="2">
              {{ $t("lang.rms.fed.shelfStaticAdjustPagenoDiffArea") }}
            </el-radio>
          </div>
        </el-card>

        <el-card>
          <div slot="header">
            <span class="card-title">
              {{ $t("lang.rms.fed.shelfStaticAdjustNum") }}
            </span>
          </div>
          <div>
            <el-input-number v-model="adjustNum" :min="0" style="width: 50%" />
          </div>
        </el-card>

        <el-card v-if="shelfScore">
          <div slot="header">
            <span class="card-title">
              {{ $t("lang.rms.fed.shelfStaticAdjustPageStepOneTitleNew") }}
            </span>
            <el-button class="fr" type="primary" size="mini" @click="handleScoring">
              {{ $t("lang.rms.fed.shelfStaticAdjustPageStepOneButton") }}
            </el-button>
          </div>
          <div>{{ scoreResult }}</div>
        </el-card>
        <el-card>
          <div slot="header">
            <span class="card-title">
              {{ $t("lang.rms.fed.shelfStaticAdjustPageStepTwoTitleNew") }}
            </span>
            <el-button class="fr" type="primary" size="mini" @click="handleCheckParam">
              {{ $t("lang.rms.fed.shelfStaticAdjustPageStepTwoButton") }}
            </el-button>
          </div>
          <div>{{ checkParamResult }}</div>
        </el-card>
        <el-card>
          <div slot="header">
            <span class="card-title">
              {{ $t("lang.rms.fed.shelfStaticAdjustPageStepThreeTitleNew") }}
            </span>
            <el-button class="fr" type="danger" size="mini" @click="handleAdjustStop">
              {{ $t("lang.rms.fed.shelfAdjustmentStop") }}
            </el-button>
            <el-button class="fr" type="success" size="mini" @click="handleAdjustStart">
              {{ $t("lang.rms.fed.shelfAdjustmentStart") }}
            </el-button>
          </div>
          <div>{{ adjustResult }}</div>
        </el-card>
      </div>

      <div class="sec-card">
        <el-card>
          <div class="warning">
            <div>{{ $t("lang.rms.fed.shelfStaticAdjustPagePrecautions") }}</div>
            <div>
              {{ $t("lang.rms.fed.shelfStaticAdjustPagePrecautionsOne") }}
            </div>
            <div>
              {{ $t("lang.rms.fed.shelfStaticAdjustPagePrecautionsTwo") }}
            </div>
            <div>
              {{ $t("lang.rms.fed.shelfStaticAdjustPagePrecautionsThreeNew") }}
            </div>
            <div>
              {{ $t("lang.rms.fed.shelfStaticAdjustPagePrecautionsFour") }}
            </div>
          </div>
        </el-card>

        <el-card>
          <div slot="header" class="clearfix">
            <span class="card-title">{{ $t("lang.rms.fed.shelfAdjustmentProgress") }}</span>
            <span class="fr card-info">
              {{ $t("lang.rms.fed.shelfAdjustmentProgress") }}: {{ totalCount }}/{{ shelveCount }}
            </span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12" class="logic-areas">
              <div>
                <span class="fr">{{ totalCount }}/{{ shelveCount }}</span>
                {{ $t("lang.rms.fed.adjustedVersusTotalQuantity") }}
              </div>
              <el-progress
                v-if="!isNaN((totalCount / shelveCount) * 100)"
                :text-inside="true"
                :stroke-width="18"
                status="success"
                :percentage="Math.floor((totalCount / shelveCount) * 100)"
              />
            </el-col>
          </el-row>
        </el-card>
      </div>
    </section>
  </geek-main-structure>
</template>
<script>
import { mapMutations, mapState } from "vuex";

export default {
  props: ["activeName"],
  data() {
    return {
      totalCount: 0,
      shelveCount: 0,
      scoreResult: "",
      checkParamResult: "",
      adjustResult: "",
      infoTimmer: null,

      areaOptions: [],
      areaValue: [],
      radio: "1",
      shelfScore: "",
      adjustNum: "",
    };
  },
  computed: {
    ...mapState("shelfStatic", ["storeAreas", "storeAreaRadio"]),
    ...mapState(["headerTabs"]),
    logicAreaResult() {
      let list = [];
      this.areaValue.forEach(item => {
        list.push(item.areaName);
      });
      return list.join("&nbsp;&nbsp;&nbsp;&nbsp;");
    },
  },
  watch: {
    activeName(name) {
      if (name === "shelfStatic") {
        this.getInfoApi();
        this.getLogicSelect();
        this.getShelfScore();
        this.radio = this.storeAreaRadio;
      } else {
        if (this.infoTimmer) clearInterval(this.infoTimmer);
        this.infoTimmer = null;
        this.shelfScore = "";

        const isHeaderTab = this.headerTabs.filter(
          item => item.path === "/warehouseManage/shelfStatic",
        );
        if (isHeaderTab && isHeaderTab[0]) {
          this.setAreas(this.areaValue);
          this.setAreaRadio(this.radio);
        } else {
          this.setAreas(null);
          this.setAreaRadio("1");
        }
      }
    },
  },
  methods: {
    ...mapMutations("shelfStatic", ["setAreas", "setAreaRadio"]),
    getShelfScore() {
      const rmsConfig = $utils.Data.getRMSConfig();
      const shelfScore = rmsConfig.shelfScoreStyle;

      if (shelfScore === null || shelfScore === "") {
        this.$alert(
          this.$t("lang.rms.fed.shelfScoreConfigConfirm"),
          this.$t("lang.rms.fed.prompt"),
          {
            confirmButtonText: this.$t("lang.rms.fed.confirm"),
            callback: action => {
              if (action === "confirm") {
                this.$router.replace("/systemConfig/parameterConfigOuter");
              } else {
                this.$emit("goFirstTab");
              }
            },
          },
        );
      } else if (shelfScore === true) {
        this.shelfScore = true;
      } else if (shelfScore === false) {
        this.shelfScore = false;
      }
    },
    getLogicSelect() {
      $req.get("/athena/shelfStaticAdjust/logicSelect").then(res => {
        if (res.code !== 0 || !res.data || !Array.isArray(res.data) || res.data.length <= 0) return;
        this.areaOptions = res.data;
        if (this.storeAreas) this.areaValue = this.storeAreas;
        else this.areaValue = res.data;
      });
    },
    handleScoring() {
      // 货架打分
      $req.post("/athena/shelfStaticAdjust/syncShelfScore", {}).then(res => {
        if (res.code === 0) {
          let msg = $utils.Tools.transMsgLang(res.msg);
          this.$success(msg);
          this.scoreResult = msg;
        }
      });
    },
    handleCheckParam() {
      // 系统参数检查
      $req.post("/athena/shelfStaticAdjust/checkParam", {}).then(res => {
        if (res.code === 0) {
          let msg = $utils.Tools.transMsgLang(res.msg);
          this.$success(msg);
          this.checkParamResult = msg;
        }
      });
    },
    handleAdjustStart() {
      let logicIds = [];
      this.areaValue.forEach(item => {
        logicIds.push(item.areaId);
      });
      const data = {
        diffArea: this.radio === "1",
        adjustNum: this.adjustNum,
        logicIds,
      };

      $req.post("/athena/shelfStaticAdjust/start", data).then(res => {
        if (res.code === 0) {
          let msg = $utils.Tools.transMsgLang(res.msg);
          this.$success(msg);
          this.adjustResult = msg;
        }
      });
    },
    handleAdjustStop() {
      $req.post("/athena/shelfStaticAdjust/stop", {}).then(res => {
        if (res.code === 0) {
          let msg = $utils.Tools.transMsgLang(res.msg);
          this.$success(msg);
          this.adjustResult = msg;
        }
      });
    },
    getInfoApi() {
      $req.post("/athena/shelfStaticAdjust/getInfo", {}).then(res => {
        // 货架调整进度获取
        this.shelveCount = res.data.totallyAdjustedShelveCount;
        this.totalCount = res.data.adjustedShelveCount;
        if (!this.adjustNum) this.adjustNum = this.shelveCount;

        this.infoTimmer = setTimeout(() => {
          this.getInfoApi();
        }, 2000);
      });
    },
  },
};
</script>
<style lang="less" scoped>
.page-title {
  text-align: center;
  font-size: 20px;
  font-weight: 600;
}

.sec-con {
  .g-flex();
  align-items: flex-start;
  margin: 10px 0;

  .sec-card {
    width: 50%;

    :deep(.el-card) {
      margin: 10px 0;
    }

    :deep(.el-card__header) {
      padding-bottom: 12px;

      button {
        margin-left: 10px;
      }
    }

    &:nth-child(2n + 1) {
      margin-right: 5px;
    }

    &:nth-child(2n) {
      margin-left: 5px;
    }

    .card-title {
      font-size: 14px;
      font-weight: 800;
    }

    .card-info {
      float: right;
      font-size: 14px;
      color: #666;
    }
  }
}

.logic-areas {
  margin: 0 0 20px;
  font-size: 12px;
}

.warning {
  color: red;
  font-size: 18px;
}
</style>
