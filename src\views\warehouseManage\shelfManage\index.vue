<template>
  <geek-main-structure>
    <el-tabs v-model="activeName" class="tab-class">
      <!-- 货架查询 -->
      <el-tab-pane
        v-if="tabNamePerssion['shelfQuery']"
        :label="$t('auth.rms.shelfQuery.page')"
        name="shelfQuery"
      >
        <ShelfQuery :active-name="activeName" />
      </el-tab-pane>
      <!-- 货架入场 -->
      <el-tab-pane
        v-if="tabNamePerssion['shelfEntering']"
        :label="$t('lang.rms.fed.shelfEntering')"
        name="shelfEntering"
      >
        <shelfEnter :active-name="activeName" />
      </el-tab-pane>
      <!-- 货架操作 -->
      <el-tab-pane
        v-if="tabNamePerssion['shelfOperations']"
        :label="$t('lang.rms.fed.shelfOperations')"
        name="shelfOperations"
      >
        <shelfOperations :active-name="activeName" />
      </el-tab-pane>
      <!-- 货架静态调整 -->
      <el-tab-pane
        v-if="tabNamePerssion['shelfStatic']"
        :label="$t('lang.rms.fed.shelfStaticAdjustmentControl')"
        name="shelfStatic"
      >
        <shelfStatic :active-name="activeName" @goFirstTab="goFirstTab" />
      </el-tab-pane>
      <!-- 货架静态调整历史 -->
      <el-tab-pane
        v-if="tabNamePerssion['shelfStaticHistory']"
        :label="$t('auth.rms.adjustShelfControllerManage.page')"
        name="shelfStaticHistory"
      >
        <shelfStaticHistory :active-name="activeName" />
      </el-tab-pane>
    </el-tabs>
  </geek-main-structure>
</template>

<script>
import ShelfQuery from "./shelfQuery";
import shelfEnter from "./shelfEnter";
import shelfOperations from "./shelfOperations";
import shelfStatic from "./shelfStatic";
import shelfStaticHistory from "./shelfStaticHistory";
export default {
  components: { ShelfQuery, shelfEnter, shelfOperations, shelfStatic, shelfStaticHistory },
  data() {
    return {
      tabNamePerssion: {
        shelfQuery: this.getTabPermission("TabShelfQueryManagePage", "shelfManage"),
        shelfEntering: this.getTabPermission("TabShelfAddManagePage", "shelfManage"),
        shelfOperations: this.getTabPermission("TabShelfHandleManagePage", "shelfManage"),
        shelfStatic: this.getTabPermission("TabShelfStaticAdjustControlPage", "shelfManage"),
        shelfStaticHistory: this.getTabPermission(
          "TabAdjustShelfControllerManagePage",
          "shelfManage",
        ),
      },
      defaultActive: "shelfQuery",
    };
  },
  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
  },
  activated() {
    this.activeName = "shelfQuery";

    this.defaultActive = $utils.Tools.getRouteQueryTabName(
      this.defaultActive,
      this.tabNamePerssion,
    );
  },
  beforeRouteLeave(to, from, next) {
    this.activeName = "";
    next();
  },
  methods: {
    goFirstTab() {
      this.activeName = "shelfQuery";
    },
  },
};
</script>
<style lang="scss" scoped></style>
