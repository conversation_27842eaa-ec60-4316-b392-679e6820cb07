<template>
  <section class="main-container " :class="{ 'main-container-space': !space}">
    <slot></slot>
  </section>
</template>

<script>
export default {
  name: "GeekMainStructure",
  props: {
    space: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang="less" scoped>
.main-container {
  height: calc(100%);
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  background: #fff;
  overflow: auto;

  &.main-container-space {
    position: relative;
    margin: -@g-main-padding;
    padding: 0;
    height: calc(100% + @g-main-padding * 2);
    overflow: hidden;
  }
}
</style>
