<template>
  <component :is="specialShapedShelfModelView || 'mainView'" />
</template>

<script>
import mainView from "./view/mainView.vue";
import editView from "./view/editView.vue";
import { mapState } from "vuex";

export default {
  components: {
    mainView,
    editView,
  },
  data() {
    return {};
  },
  computed: {
    ...mapState("containerModal", ["specialShapedShelfModelView"]),
  },
};
</script>
