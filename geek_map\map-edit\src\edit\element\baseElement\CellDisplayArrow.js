export default class CellDisplayArrow {
  static render($dir,ops) {
    const {radian,isVisible,color} = ops
    //箭头总长度
    const len = 16,w = 3,h = 4;
    const alpha = isVisible ? 1 : 0
    // '#1b9a25'
    // const color = isLoad ? 0xf60202 : 0x1b9a2
    const handlerLen = len - h
    //箭头底部
    const arrowBottomX = handlerLen * Math.cos(radian)
    const arrowBottomY = handlerLen * Math.sin(radian)
    //箭头顶部
    const arrowTopX = len * Math.cos(radian)
    const arrowTopY = len * Math.sin(radian)
    const tx = w / 2 * Math.sin(radian)
    const ty = w / 2 * Math.cos(radian)
    //箭头左
    const arrowLeftHalfX = arrowBottomX - tx / 2
    const arrowLeftHalfY = arrowBottomY + ty / 2
    const arrowLeftX = arrowBottomX - tx
    const arrowLeftY = arrowBottomY + ty
    //箭头右
    const arrowRightHalfX = arrowBottomX + tx / 2
    const arrowRightHalfY = arrowBottomY - ty / 2
    const arrowRightX = arrowBottomX + tx
    const arrowRightY = arrowBottomY - ty

    $dir.moveTo(0,0)
    $dir.lineStyle(0.5, color, alpha);
    $dir.beginFill(color,alpha)
    $dir.lineTo(arrowLeftHalfX,arrowLeftHalfY)
    $dir.lineTo(arrowLeftX,arrowLeftY)
    $dir.lineTo(arrowTopX,arrowTopY)
    $dir.lineTo(arrowRightX,arrowRightY)
    $dir.lineTo(arrowRightHalfX,arrowRightHalfY)
    $dir.lineTo(0,0)
    $dir.endFill()
    return $dir
  }
}
