<template>
  <div id="app-container" ref="appcontainer" v-loading="loading" class="app-container">
    <div class="export-btn">
      <el-button type="primary" style="right: 40px; z-index: 1000" @click="addRule">
        {{ $t("lang.rms.fed.addTo1") }}
      </el-button>
    </div>
    <DetailDialog
      v-if="dialogVisible"
      :rule-detail="ruleDetail"
      :dialog-visible.sync="dialogVisible"
      @onsubmit="handleSave"
    />
    <ResultDialog :results="logList" :result-visible.sync="resultVisible" />
    <div>
      <el-table :data="resultList" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="ruleName" :label="$t('lang.rms.hygeia.ruleName')" width="120" />
        <el-table-column prop="dataRetainDay" :label="$t('lang.rms.hygeia.leaveDay')" />
        <el-table-column prop="schedulerIntervalDay" :label="$t('lang.rms.hygeia.cleanSequence')" />
        <el-table-column prop="ruleStatus" :label="$t('lang.rms.fed.state')">
          <template slot-scope="scope">
            <span v-if="scope.row.ruleStatus === 'enabled'" style="color: green">
              {{ $t("lang.rms.fed.enable") }}
            </span>
            <span v-if="scope.row.ruleStatus === 'disabled'" style="color: red">
              {{ $t("lang.rms.fed.disable") }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="lastProcessTime"
          :label="$t('lang.rms.hygeia.lastExeTime')"
          width="160"
        >
          <template slot-scope="scope">
            <span>{{ setTime(scope.row.lastProcessTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="lastProcessResult" :label="$t('lang.rms.hygeia.lastExeResult')">
          <template slot-scope="scope">
            <span v-if="scope.row.lastProcessResult === 'SUCCESS'" style="color: green">
              {{ $t("lang.rms.hygeia.exeSuccess") }}
            </span>
            <span v-if="scope.row.lastProcessResult === 'NEW'">
              {{ $t("lang.rms.hygeia.init") }}
            </span>
            <span v-if="scope.row.lastProcessResult === 'ARCHIVING'">
              {{ $t("lang.rms.hygeia.archiving") }}
            </span>
            <span v-if="scope.row.lastProcessResult === 'CLEANING'">
              {{ $t("lang.rms.hygeia.cleaning") }}
            </span>
            <span v-if="scope.row.lastProcessResult === 'FAILED'" style="color: red">
              {{ $t("lang.rms.hygeia.exeFailed") }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="!isRoleGuest"
          fixed="right"
          :label="$t('lang.rms.fed.operation')"
          width="150"
        >
          <template slot-scope="scope">
            <div>
              <el-button type="text" size="small" @click="editRule(scope.row)">
                {{ $t("lang.rms.fed.edit") }}
              </el-button>
              <el-button type="text" size="small" @click="deleteRule(scope.row)">
                {{ $t("lang.rms.fed.delete") }}
              </el-button>
              <el-button type="text" size="small" @click="showResults(scope.row)">
                {{ $t("lang.rms.hygeia.exeResult") }}
              </el-button>
              <div>
                <el-button
                  type="text"
                  size="small"
                  :disabled="scope.row.ruleStatus === 'disabled'"
                  @click="showTask(scope.row)"
                >
                  {{ $t("lang.rms.data.clean.executeTask") }}
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import DetailDialog from "./DetailDialog";
import ResultDialog from "./ResultDialog";

export default {
  components: {
    DetailDialog,
    ResultDialog,
  },
  data() {
    return {
      dialogVisible: false,
      ruleDetail: {},
      resultList: [],
      resultVisible: false,
      logList: [],
      startTime: "",
      endTime: "",
      activeName: "",
      loading: false,
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  created() {
    this.showList();
  },
  methods: {
    // 格式化时间
    setTime(value) {
      if (!value) return "";
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss");
    },
    addRule() {
      this.ruleDetail = {
        systemCode: "",
        targetDbInfo: {
          passwordType: 0,
        },
        ruleType: "DELETE_ONLY",
        ruleStatus: "enabled",
        dataRetainDay: 360,
        schedulerIntervalDay: 7,
        schedulerFixedTime: "00:00:00",
        schedulerMaxTime: 0,
        ruleOrder: 1,
        targetFileInfo: "",
        sourceDbInfo: {
          host: "127.0.0.1",
          port: 3306,
          schema: "",
          user: "root",
          password: "",
          passwordType: 0,
        },
        items: [
          {
            sourceTable: "",
            timeColumn: "",
            order: 0,
          },
        ],
      };
      this.dialogVisible = true;
    },
    editRule(rule) {
      this.loading = true;
      $req
        .get("/athena/clean/rule/detail", {
          ruleId: rule.id,
        })
        .then(res => {
          this.loading = false;
          if (res.code === 0) {
            this.ruleDetail = res.data;
            if (this.ruleDetail.targetDbInfo === null) {
              this.ruleDetail.targetDbInfo = { passwordType: 0 };
            }
            this.dialogVisible = true;
          }
        });
    },
    deleteRule(rule) {
      this.$confirm(this.$t("lang.rms.fed.confirmDelete"), this.$t("lang.rms.fed.prompt"), {
        confirmButtonText: this.$t("lang.rms.fed.confirm"),
        cancelButtonText: this.$t("lang.rms.fed.cancel"),
        type: "error",
      }).then(() => {
        this.loading = true;
        $req
          .postParams("/athena/clean/rule/delete", {
            ruleId: rule.id,
          })
          .then(res => {
            this.loading = false;
            if (res.code === 0) {
              this.$message({
                showClose: false,
                message: this.$t("lang.rms.fed.deleteSuccessfully"),
                type: "success",
              });
              this.showList();
            }
          });
      });
    },
    // 执行任务
    showTask(row) {
      $req
        .postParams("/athena/clean/rule/execute", {
          ruleId: row.id,
        })
        .then(res => {
          if (res.code === 0) {
            this.$message({
              showClose: false,
              message: this.$t("lang.rms.hygeia.jobExecuting"),
              type: "success",
            });
          }
        });
    },
    showResults(rule) {
      this.loading = true;
      $req
        .get("/athena/clean/job/list", {
          ruleId: rule.id,
        })
        .then(res => {
          this.loading = false;
          if (res.code === 0) {
            this.logList = res.data;
            this.logList.map(item => {
              item.details = [];
            });
            this.resultVisible = true;
          }
        });
    },
    handleSave(detail) {
      this.loading = true;
      $req
        .post("/athena/clean/rule/save", {
          ...detail,
        })
        .then(res => {
          this.loading = false;
          if (res.code === 0) {
            this.$message({
              showClose: false,
              message: this.$t("lang.common.success"),
              type: "success",
            });
            this.dialogVisible = false;
            this.showList();
          } else {
            this.$message.error(res.message);
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    showList() {
      this.loading = true;
      $req
        .get("/athena/clean/rule/list")
        .then(res => {
          if (res.code === 0 && res.data.length > 0) {
            this.resultList = res.data;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>
