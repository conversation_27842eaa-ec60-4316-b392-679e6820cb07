<template>
  <!-- 角色列表 -->
  <geek-main-structure class="role-detail">
    <div class="form-content">
      <el-form
        ref="rulesForm"
        :model="formObj"
        label-position="top"
        inline
      >
        <el-form-item :label="$t('lang.rms.fed.inputRoleName')" required>
          <el-input v-model="formObj.name" :disabled="[1,2].includes(userInfo.roleId)" :placeholder="$t('lang.rms.fed.pleaseEnterContent')" />
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.inputRoleRoleDescription')" required>
          <el-input v-model="formObj.descr" :placeholder="$t('lang.rms.fed.pleaseEnterContent')" />
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.inputPermissionType')" required>
          <el-input :placeholder="$t('lang.rms.fed.pagePermission')" :disabled="true" />
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.authSystem')" required>
          <el-input placeholder="RMS" :disabled="true" />
        </el-form-item>
      </el-form>
    </div>
    <div class="table-content">
      <div class="table-content-permisson">
        <el-row>
          <el-col :span="8">
            <el-switch
              v-model="permissionStatus"
              :active-value="1"
              :inactive-value="0"
              @change="onChange"
            />
          </el-col>
          <el-col :span="12">
            <span class="switchText">{{ $t("lang.rms.fed.buttonEnablingAuthority") }}</span>
          </el-col>
        </el-row>
      </div>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane :label="$t('lang.rms.fed.tabAssignPagePermissions')" name="first">
          <sub-page-permisson ref="childPagePermisson" />
        </el-tab-pane>
        <!-- <el-tab-pane :label="$t('lang.rms.fed.tabAssociatedUsers')" name="second">
          <customize-table :table-config="tableConfig" @on-select="handleSelectionChange">
            <template #selection="{ column }">
              <el-table-column v-bind="column" type="selection" />
            </template>
          </customize-table>
          <div style="text-align: right">
            <el-pagination
              :current-page="page.currentPage"
              :page-size="page.pageSize"
              :total="page.total"
              :page-sizes="[10, 25, 50, 100]"
              layout="sizes, prev, pager, next"
              background
              class="geek-pagination"
              @size-change="pageSizeChange"
              @current-change="currentPageChange"
            />
          </div>
        </el-tab-pane> -->
      </el-tabs>
    </div>
    <div class="footer-button">
      <el-button type="primary" @click="savePagePermisson">{{ $t("lang.rms.fed.save") }}</el-button>
      <el-button @click="cancelPagePermisson">{{ $t("lang.rms.fed.cancel") }}</el-button>
    </div>
  </geek-main-structure>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import UserManager from './store'
import CustomizeTable from '../../../components/customize-table'
import SubPagePermisson from './sub-page-permisson'
export default {
  components: {
    CustomizeTable,
    SubPagePermisson
  },
  data() {
    return {
      permissionStatus: 1,
      activeName: 'first',
      params: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      // recordCount: 0,
      formObj: {
        name: '', // 角色名称
        descr: '' // 角色说明
      },
      loading: false,
      tableConfig: {
        attrs: {
          // height: '100%'
        },
        isPagination: false,
        columns: [
          {
            width: '55px',
            slot: 'selection'
          },
          {
            label: this.$t('lang.rms.fed.inputUserName'),
            'show-overflow-tooltip': true,
            // align: 'center',
            prop: 'userName'
          },
          {
            label: this.$t('lang.rms.fed.inputFullName'),
            'show-overflow-tooltip': true,
            // align: 'center',
            prop: 'realName'
          }
        ],
        data: [],
        pageSize: 10,
        total: 0
      }
    }
  },
  computed: {
    ...mapState('roleList', ['handleType', 'userInfo'])
  },
  created() {
    // console.log(this.userInfo)
    if (this.handleType === 'edit') {
      this.formObj = {
        name: this.userInfo.name,
        descr: this.userInfo.descr
      }
      this.permissionStatus = this.userInfo.status
    }

    this.params = {
      pageSize: 10,
      currentPage: 1
    }
    this.getUserList(this.params)
  },
  methods: {
    ...mapMutations('roleList', ['setPageShow']),
    // 列表接口请求
    getUserList(params) {
      $req
        .get('/athena/api/coreresource/auth/user/pageQuery/v1', {
          ...params,
          params: true
        })
        .then(res => {
          const data = res.data;
          this.page.currentPage = data.currentPage || 1;
          this.tableConfig.data = data.recordList;
          this.page.total = data.recordCount
          this.tableConfig.total = data.recordCount;
        });
    },
    onChange() {
      console.log(this.permissionStatus)
    },
    savePagePermisson() {
      this.$refs.childPagePermisson.savePagePermisson({
        permissionStatus: this.permissionStatus,
        name: this.formObj.name,
        descr: this.formObj.descr
      })
    },
    cancelPagePermisson() {
      this.setPageShow(false)
    },
    onCreate() {
      this.setPageShow(true)
    },
    handleClick(tab, event) {
      // if (tab.name === "second") {
      //     let tempStatus = this.createOrEdit;
      //     let tempType;
      //     if(tempStatus ===1){
      //         // 新建状态
      //         tempType = 'create'
      //     }else {
      //         // 编辑状态
      //         tempType = 'edit'
      //     }
      // let temp = UserManager.getUserList(tempType);
      // this.$refs.childRelateUserPermisson.setSourceType(""+ tempStatus);
      // this.$refs.childRelateUserPermisson.setMultipleSelection(temp);
      // }
    },
    handleSelectionChange(val) {
      // console.log(this.handleType)
      UserManager.clear(this.handleType)
      for (let j = 0; j < val.length; j++) {
        UserManager.saveUser(val[j].userId, this.handleType)
      }
    },
    // 分页
    currentPageChange(page) {
      this.page.currentPage = page;
      this.params = Object.assign(this.params, this.page);
      this.getUserList(this.params);
    },
    // 改变每页显示条数
    pageSizeChange(size) {
      this.page.pageSize = size;
      this.params = Object.assign(this.params, this.page);
      this.getUserList(this.params);
    },
    onQuery(val) {
      this.params = Object.assign(val, this.page)
      this.params.createTime = this.params.createTime && this.params.createTime.join('-')
      this.getUserList(this.params)
    },
    onReset(val) {
      this.params = Object.assign({}, val, this.page)
      this.getUserList(this.params)
    }
  }
}
</script>

<style lang="less">
.role-detail {
  .el-form-item__label {
    padding-bottom: 4px;
    font-size: 14px;
  }
  .el-date-editor.el-input {
    width: 185px;
  }
  .el-form-item {
    margin-bottom: 6px;
  }
  .table-content {
    position: relative;
    padding-top: 10px;
  }
  .table-content-permisson {
    width: 140px;
    height: 26px;
    position: absolute;
    right: 0px;
    z-index: 1001;
    .switchText {
      font-size: 14px;
      line-height: 26px;
    }
  }
  .handle-btn {
    padding-bottom: 20px;
    text-align: right;

    .el-button {
      width: 120px;
    }
  }
}
</style>
