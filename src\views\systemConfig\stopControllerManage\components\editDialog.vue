<template>
  <div>
    <!-- 主表弹框 -->
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      center
      :before-close="closed"
      :title="
        mode === 'add' ? $t('auth.rms.robotSoftwareManager.botton.add') : $t('lang.rms.fed.edit')
      "
      :before-save="beforeSave"
    >
      <!-- 编辑 -->
      <div class="ui-containerBox">
        <el-form ref="dynamicForm" :model="dynamicForm" label-position="right" label-width="150px">
          <el-row>
            <el-col :span="11">
              <el-form-item
                :label="$t('lang.rms.fed.controllerId')"
                prop="deviceId"
                :rules="[
                  {
                    required: true,
                    message: $t('lang.rms.fed.pleaseEnterControllerId'),
                    trigger: 'blur',
                  },
                  { pattern: /^(\-|\+)?\d+$/, message: $t('libsSz.key94') },
                  { min: 0, max: 64, message: $t('libsSz.key36', [64]), trigger: 'blur' },
                ]"
              >
                <el-input v-model="dynamicForm.deviceId" />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item
                :label="$t('lang.rms.web.monitor.robot.serverRobotIpMapIp')"
                prop="ip"
                :rules="[
                  {
                    required: true,
                    message: $t('lang.rms.fed.pleaseEnterIPAddress'),
                    trigger: 'blur',
                  },
                  {
                    pattern:
                      /((?:(?:25[0-5]|2[0-4]\d|(?:1\d{2}|[1-9]?\d))\.){3}(?:25[0-5]|2[0-4]\d|(?:1\d{2}|[1-9]?\d)))/,
                    message: $t('libsSz.key37'),
                  },
                  { min: 0, max: 64, message: $t('libsSz.key36', [64]), trigger: 'blur' },
                ]"
              >
                <el-input v-model="dynamicForm.ip" />
              </el-form-item>
            </el-col>
            <el-col :span="22">
              <el-form-item
                :label="$t('lang.rms.fed.textLogicArea')"
                prop="referBy"
                :rules="[
                  { pattern: /^[-,0-9]+$/, message: $t('libsSz.key95') },
                  { min: 0, max: 255, message: $t('libsSz.key36', [255]), trigger: 'blur' },
                ]"
              >
                <el-input
                  v-model="dynamicForm.referBy"
                  :placeholder="$t('lang.rms.fed.logiclsTip')"
                />
              </el-form-item>
            </el-col>
            <div v-for="(item, index) in dynamicForm.dynamicItem" :key="index">
              <el-col :span="11">
                <!-- :prop="form.dynamicItem[index].channelId" -->
                <el-form-item
                  :label="$t('lang.rms.fed.channel')"
                  :prop="'dynamicItem.' + index + '.channelId'"
                  :rules="[{ pattern: /^\d/, message: $t('libsSz.key94') }]"
                >
                  <el-input v-model="item.channelId" />
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item
                  :label="$t('lang.rms.web.monitor.robot.workStationId')"
                  :prop="'dynamicItem.' + index + '.referBy'"
                  :rules="[{ pattern: /^[-,0-9]+$/, message: $t('libsSz.key95') }]"
                >
                  <el-input v-model="item.referBy" />
                </el-form-item>
              </el-col>
              <el-col v-if="dynamicForm.dynamicItem.length > 1" :span="1" class="p12">
                <el-button class="el-icon-delete" @click="deleteItem(index)" />
              </el-col>
            </div>
          </el-row>
        </el-form>

        <div class="pct100 tc">
          <el-button class="el-icon-plus w180" plain @click="addItem">{{
            $t("lang.rms.fed.add")
          }}</el-button>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- 取消 -->
        <el-button type="primary" :loading="saveLoading" @click="save">{{
          $t("lang.rms.fed.save")
        }}</el-button>
        <el-button @click="closed">{{ $t("lang.rms.fed.cancel") }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getEditFormData } from "../data";
import { queryBaseDeviceSave } from "@/api/stopControllerManage";

export default {
  props: {
    visible: Boolean,
    mode: String,
    initRow: Object,
  },
  data() {
    return {
      extendFormConfig: {
        isNeedBtn: false,
      },
      dynamicForm: {
        deviceId: "",
        ip: "",
        channelId: "",
        referBy: "",
        dynamicItem: [
          {
            channelId: "",
            referBy: "",
          },
        ],
      },
      saveLoading: false,
    };
  },
  computed: {
    formData() {
      return getEditFormData(this, this.initRow);
    },
  },
  mounted() {
    let { dynamicItem } = this.dynamicForm;
    if (this.mode === 'edit') {
      const channels = this.initRow.channels || [];
      this.dynamicForm.ip = this.initRow.ip;
      this.dynamicForm.deviceId = String(this.initRow.deviceId);
      this.dynamicForm.referBy = this.initRow.referBy;
      if (channels.length > 0) {
        dynamicItem = [];
        channels.forEach(i => {
          dynamicItem.push({
            id: String(i.id),
            channelId: String(i.channelId),
            referBy: String(i.referBy),
          });
        });
      }
      this.$set(this.dynamicForm, "dynamicItem", dynamicItem);
    }
  },
  methods: {
    addItem() {
      const { dynamicItem } = this.dynamicForm;
      if (dynamicItem.length < 8) {
        this.dynamicForm.dynamicItem.push({
          channelId: "",
          referBy: "",
        });
      } else {
        this.$message.error(this.$t("lang.rms.fed.addUpToEightChannels"));
      }
    },
    deleteItem(index) {
      this.dynamicForm.dynamicItem.splice(index, 1);
    },
    // 保存前
    beforeSave() {
      return new Promise((resolve, reject) => {
        this.$refs.dynamicForm
          .validate()
          .then((vaild, formData2) => {
            const channelIdArr = this.dynamicForm.dynamicItem.map(el => el.channelId);
            if (this.dynamicForm.dynamicItem.length !== Array.from(new Set(channelIdArr)).length) {
              this.$message({
                message: "当前控制器下已存在相同通道，保存失败",
                type: "warning",
              });
              reject();

              return;
            }
            let arr = [];
            let isSave = true;
            if (this.dynamicForm.dynamicItem.length > 1) {
              this.dynamicForm.dynamicItem.forEach(item => {
                if (!item.channelId) {
                  isSave = false;
                } else {
                  arr.push(item);
                }
              });
            } else {
              this.dynamicForm.dynamicItem.forEach(item => {
                if (!item.channelId) {
                  if (item.referBy) {
                    isSave = false;
                  }
                }else{
                  arr.push(item)
                }
              });
            }
            if (!isSave) {
              this.$message.warning(this.$t("lang.rms.api.result.parameter.channelIdCannotBeNull"));
              this.saveLoading = false;
              return;
            }
            const params = {
              deviceId: this.dynamicForm.deviceId || "",
              ip: this.dynamicForm.ip,
              id: this.initRow.id || "",
              channels: arr,
              referBy: this.dynamicForm.referBy,
            };

            queryBaseDeviceSave(params)
              .then(res => {
                if (res.data.code === 0) {
                  this.$message.success(this.$t(res.data.msg));
                  resolve();
                } else {
                  this.$message.error(this.$t(res.data.msg)).catch(() => reject());
                }
              })
              .catch(() => reject());
          })
          .catch(() => reject());
      });
    },
    save() {
      this.saveLoading = true;
      const success = () => {
        this.saveLoading = false;
        this.$emit("saveSuccess");
        this.$emit("update:visible", false);
      };
      const fail = () => {
        this.saveLoading = false;
      };
      this.beforeSave ? this.beforeSave().then(success).catch(fail) : success();
    },
    // 关闭
    closed() {
      this.$emit("update:visible", false);
    },
  },
};
</script>
<style lang="scss">
.ui-containerBox .el-input--prefix .el-input__inner {
  padding-left: 15px;
}
.p12 {
  padding: 0 12px;
}
</style>
