<template>
  <el-dialog
    :title="$t('lang.rms.fed.RobotParamConfiguration')"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :before-close="beforeClose"
    :append-to-body="true"
    width="80%"
  >
    <div>
      <el-row class="rowSpace">
        <el-select v-model="storeValue">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="$t(item.label)"
            :value="item.value"
          />
        </el-select>
      </el-row>
      <el-table :data="tableData" border>
        <el-table-column prop="paramCode" :label="$t('lang.rms.fed.robotParamCode')" />
        <el-table-column prop="descrI18nCode" :label="$t('lang.rms.fed.robotDescr')" />
        <el-table-column prop="paramLimit" :label="$t('lang.rms.fed.robotParamValueLimit')" />
        <el-table-column prop="paramValue" :label="$t('lang.rms.fed.robotParamValue')">
          <template slot-scope="scope">
            <div>
              <el-input
                v-if="scope.row.editable"
                v-model="scope.row.paramValue"
                style="margin: -5px 0"
                @change="e => handleChange(e.target.value, scope.row.key, 'paramValue')"
              >
              </el-input>
              <template v-else>{{ scope.row.paramValue }}</template>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="paramValueType" :label="$t('lang.rms.fed.robotParamValueType')">
          <template slot-scope="scope">
            {{ paramValueTypeList[scope.row.paramValueType] }}
          </template>
        </el-table-column>
        <el-table-column prop="robotType" :label="$t('lang.rms.fed.robotType')" />
        <el-table-column :label="$t('lang.rms.fed.operation')">
          <template slot-scope="scope">
            <el-link
              v-if="scope.row.editable"
              type="primary"
              class="editable-row-operations"
              @click="save(scope.row.key)"
            >
              {{ $t("lang.rms.fed.save") }}
            </el-link>
            <el-link
              v-else
              type="primary"
              :disabled="editingKey !== ''"
              class="editable-row-operations"
              @click="edit(scope.row.key)"
            >
              {{ $t("lang.rms.fed.edit") }}
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      dialogVisible: false,
      robotId: "",
      editingKey: "",
      tableData: [],
      cacheData: [],
      options: [
        {
          value: "1",
          label: "lang.rms.fed.curingParam",
        },
        {
          value: "2",
          label: "lang.rms.fed.nonCuringParam",
        },
      ],
      paramValueTypeList: {
        0: this.$t("lang.rms.fed.robotParamValueTypeDigit"),
        1: this.$t("lang.rms.fed.robotParamValueTypeBool"),
        2: this.$t("lang.rms.fed.robotParamValueTypeString"),
        3: this.$t("lang.rms.fed.robotParamValueTypeJson"),
      },
      storeValue: "1",
    };
  },
  methods: {
    open(robotId) {
      this.robotId = robotId;
      this.dialogVisible = true;
      $req
        .post("/athena/robot/paramConfig/getRobotParams", {
          robotId: this.robotId,
        })
        .then(res => {
          if (res.code === 0) this.injectData(res.data);
        })
        .catch(() => {});
    },
    beforeClose() {
      this.tableData = [];
      this.cacheData = [];
      this.robotId = "";
      this.editingKey = "";
      this.storeValue = "1";
      this.dialogVisible = false;
    },

    handleChange(value, key, column) {
      console.log(value, key, column);
      const newData = [...this.tableData];
      const target = newData.filter(item => key === item.key)[0];
      console.log(target, value, column, target[column]);
      if (target) {
        target[column] = value;
        this.tableData = newData;
      }
    },
    edit(key) {
      console.log(11111, key);
      let newData = [...this.tableData];
      const target = newData.filter(item => key === item.key)[0];
      this.editingKey = key;
      if (target) {
        target.editable = true;
        this.tableData = newData;
      }
    },
    save(key) {
      const newData = [...this.tableData];
      const newCacheData = [...this.cacheData];
      const target = newData.filter(item => key === item.key)[0];
      const targetCache = newCacheData.filter(item => key === item.key)[0];
      if (target && targetCache) {
        delete target.editable;
        this.tableData = newData;
        Object.assign(targetCache, target);
        this.cacheData = newCacheData;
        let _paramList = [];
        for (let item of this.tableData) {
          let temp = {
            paramCode: item.paramCode,
            paramConfigDictId: item.paramConfigDictId,
            paramValue: item.paramValue,
          };
          _paramList.push(temp);
        }
        $req
          .post("/athena/robot/paramConfig/setRobotParams", {
            robotId: this.robotId,
            paramStore: Number(this.storeValue),
            paramList: _paramList,
          })
          .then(res => {
            if (res.code === 0) {
              this.$success(this.$t(res.msg));
            }
          });
      }
      this.editingKey = "";
    },
    injectData(data) {
      this.tableData = [];
      for (let item of data) {
        // 将参数描述翻译
        item.descrI18nCode = this.$t(item.descrI18nCode);
        this.tableData.push(
          Object.assign(item, {
            key: item.id + "&index",
            paramValue: item.paramValue.toString(),
          }),
        );
      }
      this.cacheData.length = 0;
      this.cacheData = [...this.tableData];
    },
  },
};
</script>

<style lang="less" scoped>
.el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70%;
  max-height: calc(100% - 30px);
}

.el-dialog__body {
  padding: 15px 20px;
  flex: 1;
  overflow: auto;
}

.editable-row-operations {
  margin-right: 8px;
  font-size: 12px;
}

.rowSpace {
  margin-bottom: 10px;
}
</style>
