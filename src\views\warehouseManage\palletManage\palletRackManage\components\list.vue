<template>
  <geek-main-structure class="user-list">
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    </div>
    <div class="table-content">
      <div class="table-content_head">
        <div class="table-title">
          {{ $t("lang.rms.palletRackManage.palletRackList") }}
        </div>
        <div class="handle-btn">
          <el-button type="primary" size="small" @click="onAdd">
            {{ $t("lang.rms.fed.add") }}
          </el-button>
        </div>
      </div>
      <customize-table :table-config="tableConfig">
        <template #isObstacle="{ column }">
          <el-table-column v-bind="column">
            <template #default="{ row }">
              {{ row.isObstacle ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}
            </template>
          </el-table-column>
        </template>
        <template v-if="!isRoleGuest" #operations="{ column }">
          <el-table-column v-bind="column">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="onEdit(scope.row)">
                {{ $t("lang.rms.fed.buttonEdit") }}
              </el-button>
              <el-button type="text" size="small" @click="onDelete(scope.row)">
                <p>{{ $t("auth.rms.mapManage.button.delete") }}</p>
              </el-button>
            </template>
          </el-table-column>
        </template>
      </customize-table>
      <div style="text-align: right">
        <geek-pagination
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          :total-page="totalPage"
          @currentPageChange="currentPageChange"
          @pageSizeChange="pageSizeChange"
        />
      </div>
    </div>
  </geek-main-structure>
</template>

<script>
import CustomizeTable from "../../../../components/customize-table";
export default {
  name: "PalletRackManage",
  components: {
    CustomizeTable,
  },
  data() {
    return {
      params: {},
      page: {
        pageSize: 10,
        currentPage: 1,
      },
      newPassword: "",
      recordCount: 0,
      totalPage: 0,
      popoverVisible: false,
      createUserFlag: false,
      editUserFlag: false,
      list: [],
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
    formConfig() {
      return {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          // palletLatticeCode: {
          //   label: this.$t("lang.rms.palletPositionManage.palletLatticeCode"),
          //   default: "",
          //   tag: "input",
          //   placeholder: this.$t("lang.rms.fed.pleaseEnterContent"),
          // },
          palletRackCode: {
            label: this.$t("lang.rms.palletPositionManage.palletRackCode"),
            default: "",
            tag: "input",
            placeholder: this.$t("lang.rms.fed.pleaseEnterContent"),
          },
        },
        rules: [],
        operations: [
          {
            label: this.$t("lang.rms.fed.query"),
            handler: "on-query",
            type: "primary",
          },
          {
            label: this.$t("lang.rms.fed.reset"),
            handler: "on-reset",
            type: "default",
          },
        ],
      };
    },
    tableConfig() {
      return {
        attrs: {
          // height: '100%'
        },
        isPagination: false,
        columns: [
          {
            label: this.$t("lang.rms.fed.listSerialNumber"),
            type: "index",
            width: "50px",
          },
          {
            label: this.$t("lang.rms.palletPositionManage.palletRackCode"),
            "show-overflow-tooltip": true,
            prop: "palletRackCode",
          },
          {
            label: this.$t("lang.rms.palletRackManage.totalFloors"),
            "show-overflow-tooltip": true,
            prop: "totalFloors",
          },
          {
            label: this.$t("lang.rms.palletRackManage.locationCellCode"),
            "show-overflow-tooltip": true,
            prop: "locationCellCode",
          },
          {
            label: this.$t("lang.rms.palletRackManage.angle"),
            "show-overflow-tooltip": true,
            prop: "angle",
          },
          {
            label: `${this.$t("lang.rms.web.monitor.cell.fieldPrefix.length")}`,
            "show-overflow-tooltip": true,
            prop: "length",
          },
          {
            label: this.$t("lang.rms.web.monitor.cell.fieldPrefix.width"),
            "show-overflow-tooltip": true,
            prop: "width",
          },
          {
            label: this.$t("lang.rms.palletRackManage.isObstacle"),
            "show-overflow-tooltip": true,
            slot: "isObstacle",
          },
          {
            label: this.$t("lang.rms.fed.listOperation"),
            width: "100px",
            slot: "operations",
          },
        ],
        data: this.list,
        pageSize: this.page.pageSize,
        total: this.recordCount,
      };
    },
  },
  created() {
    this.params = {
      pageSize: this.page.pageSize,
      currentPage: this.page.currentPage,
    };
    this.getPalletRackList(this.params);
  },
  methods: {
    // 列表接口请求
    getPalletRackList(params) {
      $req
        .get("/athena/palletRack/findList", {
          ...params,
          params: true,
        })
        .then(res => {
          const data = res.data;
          this.page.currentPage = data.currentPage || 1;
          this.list = data.recordList;
          this.totalPage = data.pageCount;
          this.recordCount = data.recordCount;
        });
    },
    // 新增
    onAdd({ row }) {
      this.$emit("updateCom", {
        currentCom: "PalletRackManageDetail",
        mode: "add",
        row,
      });
    },
    onEdit(row) {
      this.$emit("updateCom", {
        currentCom: "PalletRackManageDetail",
        mode: "edit",
        row,
      });
    },
    onDelete(row) {
      this.$confirm(this.$t("lang.rms.fed.confirmDelete"), {
        confirmButtonText: this.$t("lang.rms.fed.confirm"),
        cancelButtonText: this.$t("lang.rms.fed.cancel"),
        type: "warning",
      })
        .then(() => {
          const url = "/athena/palletRack/remove";
          $req.get(url, { palletRackCode: row.palletRackCode }).then(res => {
            if (res.code === 0) {
              this.$message({
                type: "success",
                message: this.$t("lang.rms.api.result.ok"),
              });
              this.params.currentPage = 1;
              this.getPalletRackList(this.params);
            }
          });
        })
        .catch(() => {})
        .finally(() => {
          const dom = document.querySelector(".v-modal");
          if (dom) {
            const bodyDom = document.querySelector("body");
            bodyDom.removeChild(dom);
          }
        });
    },
    // 分页
    currentPageChange(page) {
      this.page.currentPage = page;
      this.params = Object.assign(this.params, this.page);
      this.getPalletRackList(this.params);
    },
    // 改变每页显示条数
    pageSizeChange(size) {
      this.page.pageSize = size;
      this.params = Object.assign(this.params, this.page);
      this.getPalletRackList(this.params);
    },
    onQuery(val) {
      this.page.currentPage = 1;
      this.params = Object.assign(val, this.page);
      this.getPalletRackList(this.params);
    },
    onReset(val) {
      this.page.currentPage = 1;
      this.params = Object.assign({}, val, this.page);
      this.getPalletRackList(this.params);
    },
  },
};
</script>

<style lang="less">
.user-list {
  .table-content {
    padding-top: 10px;
  }
  .table-content_head {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .table-title {
    margin-bottom: 15px;

    &::before {
      content: "";
      display: inline-block;
      height: 21px;
      width: 4px;
      border-radius: 4px;
      background: #409eff;
      margin-right: 10px;
      vertical-align: text-bottom;
    }
  }
  .handle-btn {
    padding-bottom: 20px;
    text-align: right;

    .el-button {
      width: 120px;
    }
  }
}
.change-pwd {
  .rowSpace {
    margin: 0px 0px 16px 0px;
  }
  .vText {
    line-height: 32px;
  }
  .cSpace {
    text-align: center;
  }
  .customInput {
    width: 150px;
    height: 20px;
  }
}
</style>
