import "@packages/public-path";
import { createApp } from "vue";
import App from "@packages/App.vue";
import router from "@packages/router";
import { setupI18n } from "@packages/logics/i18n";
import { setupTour } from "@packages/logics/tour";
import { setupComponents } from "@packages/logics/components";
import { setupStore } from "@packages/logics/store";
import { setupIframeListener } from "@packages/logics/iframeListener";
import { setupElementPlus } from "@packages/logics/elementplus";

// @style
import "vue-tour/dist/vue-tour.css";
import "element-plus/dist/index.css";
import "@packages/style/index.scss";
import "@packages/assets/iconFont/iconfont.css";

let app: any = null;
function render(props: { container: Element | undefined }) {
  const { container } = props;
  /**
   * 顺序不要错, 一定要先注册store,
   * 因为后续可能会在初始化的时候在store中存一些数据
   */
  app = createApp(App);
  // 注册路由
  app.use(router);

  // 注册 sotre
  setupStore(app);

  // 注册国际化
  setupI18n(app);

  // 注册用户引导
  // setupTour(app);

  // 注册iframe方式
  setupIframeListener();

  // 注册全局组件
  setupComponents(app);

  // 注册element
  setupElementPlus(app);

  // qk兼容
  if (container) {
    app.mount(container.querySelector("#app") as Element);
  } else {
    app.mount("#app");
  }
}

// 独立运行时
if (!(window as any).__POWERED_BY_QIANKUN__) {
  render({ container: undefined });
}

/**
 * 下面是一些对qiankun的支持, 可忽略
 */
export async function bootstrap() {}

export async function mount(props: any) {
  render(props);
}

export async function unmount() {
  app.$destroy();
  app.$el.innerHTML = "";
  app = null;
}
