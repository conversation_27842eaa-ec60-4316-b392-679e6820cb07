<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2022-01-05 20:04:30
 * @Description:
-->
<template>
  <el-form
    ref="searchForm"
    :inline="true"
    class="demo-form-inline"
    label-position="top"
    :model="searchForm"
  >
    <!-- 机器人id -->
    <el-form-item :label="$t('lang.mb.robotManage.robotId')" prop="robotId">
      <el-input
        v-model.number="searchForm.robotId"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterRobotID')"
        @input.native="inputRobotId"
      />
    </el-form-item>
    <!-- 机器人别名 -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotAlias')" prop="hostCode">
      <el-input v-model.trim="searchForm.hostCode" :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterRobotAlias')" />
    </el-form-item>
    <!-- 机器人型号 -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotModel')" prop="robotModelId">
      <!-- <el-input v-model.trim="searchForm.robotModelEntity" placeholder="请输入机器人型号" /> -->
      <el-select
        v-model="searchForm.robotModelId"
        :placeholder="$t('lang.rms.fed.choose')"
        :style="{ width:'120px'}"
      >
        <el-option
          v-for="item in robotModelIdARR"
          :key="item.id"
          :label="item.label"
          :value="item.robotModelId"
        />
      </el-select>
    </el-form-item>
    <!-- 机器人管理状态 -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotManageStatus')" prop="manageStatus">
      <el-select
        v-model="searchForm.manageStatus"
        :placeholder="$t('lang.rms.fed.choose')"
        :style="{ width:'120px'}"
      >
        <el-option :label="$t('lang.rms.fed.whole')" value="all" />
        <el-option :label="$t('lang.rms.api.result.warehouse.unRegister')" value="UNREGISTERED" />
        <el-option :label="$t('lang.rms.api.result.warehouse.register')" value="REGISTERED" />
        <el-option :label="$t('lang.rms.api.result.warehouse.stop')" value="BLOCKED" />
      </el-select>
    </el-form-item>
    <!-- 机器人工作状态 -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotWorkStatus')" prop="workStatus">
      <el-select
        v-model="searchForm.workStatus"
        :placeholder="$t('lang.rms.fed.choose')"
        :style="{ width:'120px'}"
      >
        <el-option :label="$t('lang.rms.fed.whole')" value="all" />
        <el-option :label="$t('lang.rms.api.result.warehouse.normalPresence')" value="NORMAL" />
        <el-option :label="$t('lang.rms.api.result.warehouse.sleep')" value="SLEEPING" />
        <el-option :label="$t('lang.rms.api.result.warehouse.departure')" value="REMOVE_FROM_SYSTEM" />
      </el-select>
    </el-form-item>
    <!-- 锁定状态 -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.lockedStatus')" prop="controlStatus">
      <el-select
        v-model="searchForm.controlStatus"
        :placeholder="$t('lang.rms.fed.choose')"
        :style="{ width:'120px'}"
      >
        <el-option :label="$t('lang.rms.fed.whole')" value="all" />
        <el-option :label="$t('lang.rms.api.result.warehouse.normal')" value="NORMAL" />
        <el-option :label="$t('lang.rms.api.result.warehouse.robotStop')" value="STOP" />
        <el-option :label="$t('lang.rms.api.result.warehouse.robotStopAndStopTask')" value="SUSPEND" />
        <el-option :label="$t('lang.rms.api.result.warehouse.robotLock')" value="LOCK" />

      </el-select>
    </el-form-item>
    <!-- 链接状态 -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.connectionStatus')" prop="commStatus">
      <el-select
        v-model="searchForm.commStatus"
        :placeholder="$t('lang.rms.fed.choose')"
        :style="{ width:'120px'}"
      >
        <el-option :label="$t('lang.rms.fed.whole')" value="all" />
        <el-option :label="$t('lang.venus.common.dict.onlineStatus.online')" value="NORMAL" />
        <el-option :label="$t('lang.venus.common.dict.onlineStatus.dropLine')" value="OFFLINE" />
        <el-option :label="$t('lang.venus.common.dict.onlineStatus.reconecting')" value="DISCONNECTED" />
      </el-select>
    </el-form-item>
    <el-form-item class="align-bottom">
      <!-- 查询 -->
      <el-button type="primary" @click="onSubmit">{{ $t('lang.rms.fed.query') }}</el-button>
      <!-- 重置 -->
      <el-button type="primary" @click="resetForm">{{ $t('lang.rms.fed.reset') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import robotManageRequest from '@/api/robotManage'
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    // 这里存放数据
    return {
      searchForm: {
        robotId: '', hostCode: '', manageStatus: '', workStatus: '', controlStatus: '', commStatus: ''
      },
      robotModelIdARR: []
    }
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  mounted() {
    this.getArrRobotModalArr()
    this.onSubmit()
  },
  // 方法集合
  methods: {
    inputRobotId(e) {
      const value = e.target.value.replace(/[^\d.]/g, '')
      e.target.value = typeof this.searchForm.robotId === 'number' ? value : null
    },
    onSubmit() {
      const subdata = Object.assign({}, this.searchForm)
      subdata.manageStatus = this.searchForm.manageStatus === 'all' ? '' : this.searchForm.manageStatus
      subdata.workStatus = this.searchForm.workStatus === 'all' ? '' : this.searchForm.workStatus
      subdata.controlStatus = this.searchForm.controlStatus === 'all' ? '' : this.searchForm.controlStatus
      subdata.commStatus = this.searchForm.commStatus === 'all' ? '' : this.searchForm.commStatus

      this.$emit('onsubmit', { ...subdata })
    },
    resetForm() {
      this.$refs['searchForm'].resetFields()
      this.onSubmit()
    },
    getArrRobotModalArr() {
      robotManageRequest.getRobotModalPageList({}, { pageSize: 10000, currentPage: 1, total: 0 }).then(({ data }) => {
        this.robotModelIdARR = data.recordList.map(item => ({ label: item.product, robotModelId: item.robotModelId }))
      })
    }
  }
}
</script>
<style lang='scss' scoped>
.align-bottom {
  vertical-align:bottom;
}
</style>
