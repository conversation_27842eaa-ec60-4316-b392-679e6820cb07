import request from '../request'

// ~~~~~~~~~~~~容器管理~~~~~~~~~~~~~
// 容器实例列表
export function getContainerData(data = { pageSize: 20, currentPage: 1 }) {
  return request({ url: '/athena/container/findContainerByPage', method: 'post', data, params: { currentPage: data.currentPage, pageSize: data.pageSize }})
}

// 容器模型
export function getContainerModelData(data = { pageSize: 20, currentPage: 1 }) {
  return request({ url: '/athena/containerModel/findContainerModelByPage', method: 'post', data, params: { currentPage: data.currentPage, pageSize: data.pageSize }})
}

// 容器模型添加
export function addModl(data) {
  return request({ url: '/athena/containerModel/saveContainerModel', method: 'post', data })
}

// 容器模型布局查询接口
export function findLayoutByModelId(data) {
  return request({ url: '/athena/containerModel/findLayoutByModelId', method: 'post', data })
}

// 容器模型布局保存接口
export function saveContainerLayout(data) {
  return request({ url: '/athena/containerModel/saveContainerLayout', method: 'post', data })
}

// 容器模型id 搜索项
export async function getIdArrSearch() {
  return request({ url: '/athena/containerModel/containerModelId', method: 'get' })
}

// 容器模型名称 搜索项
export async function getModelArrSearch() {
  return request({ url: '/athena/containerModel/containerModelName', method: 'get' })
}

// 容器模型删除接口
export function deleteContainerModel(data) {
  return request({ url: '/athena/containerModel/deleteContainerModel', method: 'post', data })
}
// 容器模型编码 搜索项
export async function getCodelArrSearch() {
  return request({ url: '/athena/container/containerCode', method: 'get' })
}

