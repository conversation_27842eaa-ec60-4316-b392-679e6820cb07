import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Tree, Select } from "antd";
import { getMap2D, $eventBus } from "../../../singleton";

let filterData: any = {};
let initTreeArray: Array<any> = [];
function FilterSelect() {
  const { t } = useTranslation();
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [value, setValue] = useState(undefined);
  const [treeData, setTreeData] = useState([]);
  const refTree = useRef();

  useEffect(() => {
    $eventBus.on("wsFastSearchTop", data => {
      let stopAreas: any = [];
      let speedLimitAreas: any = [];
      let expandedKey: any = "";
      let fastData: any = data.map((root: any) => {
        if (!expandedKey) expandedKey = root.type;
        return {
          title: root.i18,
          value: root.type,
          children: root.data.map((level2: any) => {
            const level2Data = level2.data || [];
            const level2Type = level2.type;
            filterData[level2Type] = { rootType: root.type, type: level2Type, data: level2Data };

            let rootChildren: any = {
              title: level2.i18,
              value: level2Type,
              isLeaf: true,
            };

            if (root.type === "WAREHOUSE_AREA" && level2Data.length) {
              if (level2Type === "STOP") {
                level2Data.forEach((item: any) => {
                  stopAreas.push({
                    floorId: item.extraProperties?.floorId,
                    areaId: item.extraProperties?.areaId,
                    areaType: item.extraProperties?.areaType,
                    cellCodes: item.functionalArea,
                  });
                });
              }
              if (level2Type === "REAL_TIME_SPEED_LIMIT_AREA") {
                level2Data.forEach((item: any) => {
                  speedLimitAreas.push({
                    floorId: item.extraProperties?.floorId,
                    areaId: item.extraProperties?.areaId,
                    areaType: item.extraProperties?.areaType,
                    cellCodes: item.functionalArea,
                  });
                });
              }

              rootChildren.extraText = `(${level2Data.length})`;
              rootChildren.isLeaf = false;
              rootChildren.children = level2Data.map((level3: any) => {
                const areaId = level3?.extraProperties?.areaId;
                if (areaId) {
                  filterData[`${level2Type}_${areaId}`] = {
                    rootType: root.type,
                    type: level2Type,
                    data: level3,
                  };
                  return {
                    title: level2.type,
                    extraText: areaId,
                    value: `${level2Type}_${areaId}`,
                    isLeaf: true,
                  };
                }
              });
            }
            return rootChildren;
          }),
        };
      });

      if (expandedKey) setExpandedKeys([expandedKey]);

      if (stopAreas.length) {
        const map2D = getMap2D();
        map2D.mapRender.renderArea("STOP", stopAreas);
      }

      if (speedLimitAreas.length) {
        const map2D = getMap2D();
        map2D.mapRender.renderArea("REAL_TIME_SPEED_LIMIT_AREA", speedLimitAreas);
      }

      // fastTree = fastData;
      initTreeData(fastData);
    });
    return () => {
      $eventBus.off("wsFastSearchTop");
    };
  }, []);

  const treeSelect = (selectedKeys: any, e: any) => {
    let newValue = selectedKeys[0];
    if (newValue == undefined) newValue = e.node.key;

    let { rootType, type, data } = filterData[newValue];
    const map2D = getMap2D();

    if (newValue === value) {
      setValue(undefined);
      setSelectedKeys([]);
      map2D.mapRender.triggerFastFilter(rootType, { action: "remove" });
      return;
    }

    setValue(newValue);
    setSelectedKeys([newValue]);
    let funcCellCodes: Array<code> = [];
    let areasData: Array<any> = [];
    switch (rootType) {
      case "FUNCTIONAL_CELL":
        data.forEach((item: any) => {
          funcCellCodes = funcCellCodes.concat(item.functionalArea);
        });
        map2D.mapRender.triggerFastFilter(rootType, { cellCodes: funcCellCodes, action: "render" });
        // TODO 休息点
        // if (type === "RestPointFuncCell") {
        //   console.log("RestPointFuncCell 休息点");
        // }
        break;
      case "CELL_SIZE_TYPE":
        map2D.mapRender.triggerFastFilter(rootType, { sizeType: newValue, action: "render" });
        break;
      case "WAREHOUSE_AREA":
        if (Object.prototype.toString.call(data) !== "[object Array]") data = [data];
        data.forEach((item: any) => {
          let area: any = {
            floorId: item.extraProperties?.floorId,
            areaId: item.extraProperties?.areaId,
            areaType: item.extraProperties?.areaType,
          };
          if (item.extraProperties?.polygons) area.polygons = item.extraProperties.polygons;
          else area.cellCodes = item.functionalArea || [];
          areasData.push(area);
        });
        map2D.mapRender.triggerFastFilter(rootType, {
          areaType: type,
          areasData,
          action: "render",
        });
        break;
    }
  };

  const initTreeData = (fastTree: Array<any>) => {
    const filterTree = fastTree.map((root: any, index: number) => {
      return {
        title: t(root.title),
        key: root.value,
        selectable: false,
        children: root.children.map((level2: any) => {
          let level2Title = t(level2.title);
          if (level2.extraText) level2Title = `${level2Title} ${level2.extraText}`;

          let children2: any = [];
          if (level2.children) {
            children2 = level2.children.map((level3: any) => {
              let level3Title = t(level3.title);
              if (level3.extraText) level3Title = `${level3Title} ${level3.extraText}`;
              return {
                title: level3Title,
                key: level3.value,
                isLeaf: level3.isLeaf,
              };
            });
          }

          return {
            title: level2Title,
            key: level2.value,
            isLeaf: level2.isLeaf,
            children: children2,
          };
        }),
      };
    });

    setTreeData(filterTree);
    initTreeArray = filterTree;
  };

  const onSearch = (value: string) => {
    let expandedKey: Array<any> = [];
    if (!value) {
      setTreeData(initTreeArray);
      expandedKey = initTreeArray?.length ? [initTreeArray[0]?.key] : [];
      setExpandedKeys(expandedKey);
      return;
    }
    let newTree = onFilterTreeData(initTreeArray, value);
    setTreeData(newTree);
    expandedKey = newTree?.length ? [newTree[0]?.key] : [];
    setExpandedKeys(expandedKey);
  };

  const onFilterTreeData = (arr: Array<any>, val: any) => {
    let newarr: Array<any> = [];
    arr.forEach(item => {
      if (item.children && item.children.length) {
        let children = onFilterTreeData(item.children, val);
        // let title = t();
        let obj = {
          ...item,
          children,
        };
        if (children && children.length) {
          newarr.push(obj);
        } else if (item.title.includes(val)) {
          newarr.push({ ...item });
        }
      } else {
        if (item.title.includes(val)) {
          newarr.push(item);
        }
      }
    });

    return newarr;
  };

  const treeExpand = (expandedKeys: any, info: any) => {
    setExpandedKeys(expandedKeys);
  };

  return (
    <Select
      value={value}
      allowClear
      showSearch
      placement="bottomLeft"
      placeholder={t("lang.rms.fed.fastSearch")}
      style={{ width: "100%" }}
      className="map2d-fast-search"
      dropdownStyle={{ maxHeight: 280, overflow: "auto", paddingRight: 10 }}
      dropdownMatchSelectWidth={false}
      onClear={() => {
        treeSelect([], { node: { key: value } });
      }}
      onSearch={onSearch}
      dropdownRender={() => (
        <Tree
          ref={refTree}
          height={260}
          selectedKeys={selectedKeys}
          blockNode={true}
          virtual={true}
          className="map2d-fast-search-dropdown"
          treeData={treeData}
          defaultExpandedKeys={expandedKeys}
          expandedKeys={expandedKeys}
          onSelect={treeSelect}
          onExpand={treeExpand}
        />
      )}
    />
    // <TreeSelect
    //   allowClear
    //   listHeight={300}
    //   value={value}
    //   treeData={treeData}
    //   onChange={onChange}
    //   treeDefaultExpandedKeys={expandedKeys}
    //   treeNodeFilterProp="title"
    //   showSearch
    //   dropdownMatchSelectWidth={false}
    //   placeholder={t("lang.rms.fed.fastSearch")}
    //   placement="bottomLeft"
    //   className="map2d-fast-search"
    //   popupClassName="map2d-fast-search-dropdown"
    //   dropdownStyle={{ maxHeight: 400, overflow: "auto", paddingRight: 10 }}
    // />
  );
}

export default FilterSelect;
