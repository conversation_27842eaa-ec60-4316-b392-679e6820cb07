/**
 * 这里是EditMap初始化时的一些处理
 */
import {
  getMap,
  getFloorDetail,
  getShelfModel,
  getFunctionalLabels,
  getAllMapAreas,
  findDistinctSizeType,
  sequenceNextId,
  screenNoList,
} from "@packages/api/map";

import { MapFloorDto } from "@packages/api/map/type/getFloorDetail";
import { useTemplateStore } from "@packages/store/template";
import { useAttrStore } from "@packages/store/attr";
import { useAppStore } from "@packages/store/app";
import { EditRef } from "@packages/type/edit";
import Edit from "@src/edit/Edit.js";
import mapVNode from "@packages/hook/useMapVNode";
import { AREA_BLOCK } from "@packages/configure/dict/nodeType";
import { editBindEventBus, triggerEventListener, defOperationFn } from "./useEvent";

let editMapRef: EditRef;

let ref = new Proxy({ value: null } as { value: EditRef | null }, {
  get(target, propKey): EditRef {
    return editMapRef;
  },
});

/**
 * 使用 useEditMap 可以获取editMap组件实例
 * @returns editMap实例
 */
export const useEditMap = () => {
  return ref;
};

/**
 * 初始化editMap
 * @param mapFloorDto 接口数据
 * @returns editMap实例
 */
export const loadEditMapData = ({
  mapFloorDto,
  mapAreaList,
  bkData,
  resolution,
}: {
  resolution: number;
  mapFloorDto: MapFloorDto;
  mapAreaList: any;
  bkData: any;
}): EditRef => {
  const initDataObj: {
    CELL?: any[];
    LINE?: any[];
    AREA?: any[];
    STATION?: any[];
    CHARGER?: any[];
    MARKER?: any[];
    ELEVATOR?: any[];
    SAFE?: any[];
    BACKGROUND?: any;
  } = {};
  //设置resolution
  editMapRef.setResolution(resolution);

  const {
    mapNodeDtoList,
    mapSegmentDtoList,
    mapStationDtoList,
    mapChargerDtoList,
    mapMarkerList,
    mapElevatorDtoList,
    dmpDeviceDtoList,
  } = mapFloorDto;

  initDataObj["CELL"] = mapNodeDtoList || [];
  initDataObj["LINE"] = mapSegmentDtoList || [];
  initDataObj["AREA"] = (mapAreaList || []).map((item: any) => {
    if (item.areaType === AREA_BLOCK) {
      return { ...item, isRect: true };
    }

    return item;
  });
  initDataObj["STATION"] = mapStationDtoList || [];
  initDataObj["CHARGER"] = mapChargerDtoList || [];
  initDataObj["MARKER"] = mapMarkerList || [];
  //处理电梯的奇怪数据结构
  initDataObj["ELEVATOR"] = (mapElevatorDtoList || []).map((item: any) => {
    const { queueCellsCodeIdMap } = item;
    item.queueCellsCodeIdMap = queueCellsCodeIdMap ? queueCellsCodeIdMap[0] : null;
    return item;
  });
  initDataObj["SAFE"] = dmpDeviceDtoList || [];
  initDataObj["BACKGROUND"] = bkData || {};
  editMapRef.initData(initDataObj);

  if (mapNodeDtoList) {
    useAttrStore().setStoredCellCodes(mapNodeDtoList);
  }

  // 初始的一些操作 1.隐藏区域/2. 展示空负载
  editMapRef.hideLayer({ id: "AREA" });
  editMapRef.hideLayer({ id: "MARKER" });
  editMapRef.showDirByType("both");
  triggerEventListener("map:ready", {});
  return editMapRef;
};

/**
 * 初始化mapEdit组件
 * @returns editMap实例
 */
export const initEditMap = async (element: any): Promise<EditRef> => {
  editMapRef = new Edit({ dom: element });
  await editMapRef.initMap();
  ref.value = editMapRef;
  editMapRef.setGlobalEditMap(editMapRef);
  return editMapRef;
};

/**
 * 获取初始化页面时的数据集合
 * @param appStore pinia数据
 * @returns 返回一个promise
 */
export async function initMapSetup() {
  const appStore = useAppStore();
  const templateStore = useTemplateStore();

  const floorId = appStore.floorId || "";
  const mapId = appStore.mapId || "";
  const params = { floorId, mapId };
  const isTransformRequestResult = false;
  const [getMapResult, getFloorDetailResult, getAllMapAreasResult] = await Promise.all([
    getMap(params, { isTransformRequestResult }),
    getFloorDetail(params, { isTransformRequestResult }),
    getAllMapAreas(params),
  ]);

  if (
    getMapResult.code === 0 &&
    getFloorDetailResult.code === 0 &&
    getAllMapAreasResult.code === 0
  ) {
    const { resolution, locationX, locationY, scale, splitImage } = getMapResult.data;
    const { mapName } = getFloorDetailResult.data;
    const mapAreaList = getAllMapAreasResult.data;

    templateStore.setItem({
      floorId,
      mapId,
      scale,
      resolution,
      locationX,
      locationY,
      mapName,
      // splitImage
    });

    return {
      ...getMapResult.data,
      ...getFloorDetailResult.data,
      mapAreaList,
      bkData: {
        locationX,
        locationY,
        splitImage,
      },
      resolution,
    };
  }

  templateStore.setItem({
    floorId,
    mapId,
  });

  const curErrorResult = getMapResult.code === 0 ? getFloorDetailResult : getMapResult;
  return Promise.reject(curErrorResult.msg);
}

/**
 * 由于地图在initMapSetup函数中的接口请求完成之后才会加载,
 * 有一些可以后续加载的接口放置在这里
 */
export async function loadMapData() {
  const attrStore = useAttrStore();

  const [getShelfModelResult, getFunctionalLabelsResult, findDistinctSizeTypeResult] =
    await Promise.all([
      getShelfModel({ type: "shelf" }),
      getFunctionalLabels(),
      findDistinctSizeType(),
    ]);

  if (getShelfModelResult.code === 0) {
    attrStore.setHolderTypeDict(getShelfModelResult.data);
  }

  if (getFunctionalLabelsResult.code === 0) {
    attrStore.setFunctionalLabels(getFunctionalLabelsResult.data);
  }

  if (findDistinctSizeTypeResult.code === 0) {
    attrStore.setFindDistinctSizeType(findDistinctSizeTypeResult.data);
  }
}

export async function setAreaNodeDefData(areaNodeData: any, layerName: any) {
  const [sequenceNextIdResult, screenNoListResult] = await Promise.all([
    sequenceNextId({
      sequenceName: "AREA",
    }),
    screenNoList({
      mapAreaDto: areaNodeData,
      mapNodeDtoList: editMapRef.getAllData().CELL,
    }),
  ]);

  const areaId = sequenceNextIdResult.data;
  const cellCodes = screenNoListResult.data;
  const areaData = { ...areaNodeData, areaId: areaId, areaCode: areaId, cellCodes, desc: 0 };

  editMapRef.updateElements({
    id: layerName,
    data: [areaData],
    isSaveHistory: false,
  });

  const rightFromRef = mapVNode.useRightPanels();
  const formRef = rightFromRef.getFromNode();

  formRef?.setItemAll(areaData);
}
//重新请求区域覆盖的点位
export async function setCoverPoints(areaNodeData: any) {
  const res = await screenNoList({
    mapAreaDto: areaNodeData,
    mapNodeDtoList: editMapRef.getAllData().CELL,
  });
  const rightFromRef = mapVNode.useRightPanels();
  const formRef = rightFromRef.getFromNode();
  const cellCodes = res.data;
  const areaData = { ...areaNodeData, cellCodes };
  editMapRef.updateElements({
    id: "AREA",
    data: [areaData],
    isSaveHistory: false,
  });
  formRef?.setItemAll({ cellCodes: res.data });
}
/**
 * 重新加载地图资源
 * 先销毁
 * 在重新请求地图数据
 */
export async function reloadMapData() {
  console.log("update >>>>> 刷新地图");
  const attrStore = useAttrStore();
  const canvasViewRef = mapVNode.useEditRef();
  console.log("update >>>>> editMapRef:", editMapRef);
  if (editMapRef?.destroy) {
    editMapRef.destroy();
    await initEditMap(canvasViewRef);
    const data = await initMapSetup();
    loadEditMapData(data);
    editBindEventBus();
    defOperationFn(editMapRef, {
      isDefMode: true,
      isClearSelect: true,
    });
  }
  attrStore.setGlobalLoading(false);
}
