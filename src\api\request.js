import axios from "axios";
// import { MessageBox, Message } from 'element-ui'
import { Message } from "element-ui";
import store from "@/store";
// import { getToken } from "@/utils/auth";
// import qs from 'qs';
import i18n from "@/lang";

// create an axios instance
const service = axios.create({
  baseURL: "/", // url = base url + request url
  withCredentials: true, // send cookies when cross-domain requests
  timeout: 10000, // request timeout
});

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      // config.headers["X-Token"] = getToken();
    }
    const lang = localStorage.getItem("Geek_RMSLanguage");
    if (lang) {
      config.headers["Accept-Language"] = lang;
    }
    return config;
  },
  error => {
    // do something with request error
    if (error.response) {
      Message.error("网络异常");
    }
    return Promise.reject(error);
  },
);

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data;
    if (res.code !== 0 && !response.config.isStaticReq) {
      if (res.code === 2) {
        // 无登录状态
        $utils.Data.removeAllStorage();
        $utils.Tools.toLogin();
        // $app.$router.push({ path: "/login" });
      } else {
        const msg = res.msg;
        const hasChinese = $utils.Tools.hasChinese(msg);
        const errorMsg = hasChinese ? msg : $utils.Tools.transMsgLang(msg);
        $app.$error(errorMsg);
      }
    } else {
      // 统一处理页码返回0的问题
      if (res.data && res.data.currentPage === 0) {
        res.data.currentPage = 1;
      }
      res.config = response.config;
      return res;
    }
    // return response;
    // const res = response.data;
    // if (res.code !== 0) {
    //   // 有可能翻译，有可能存在{p:[0,1], c:"xxxx{0}xxxxx{1}"}
    //   // 有可能存在{ c:"xxxx{0}xxxxx{1}"}
    //   if (typeof res.z === 'string') {
    //     Message.error(i18n.t(res.msg) || '网络异常')
    //   } else {
    //     const mydata = JSON.parse(JSON.stringify(res.msg))
    //     Message.error(i18n.t(`c['${mydata.c}']`, mydata.p) || '网络异常')
    //   }

    //   /* // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
    //         if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
    //           // to re-login
    //           MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
    //             confirmButtonText: 'Re-Login',
    //             cancelButtonText: 'Cancel',
    //             type: 'warning'
    //           }).then(() => {
    //             store.dispatch('user/resetToken').then(() => {
    //               location.reload()
    //             })
    //           })
    //         }*/
    //   return Promise.reject(new Error(i18n.t(res.msg) || '网络异常'))
    // } else {
    //   // 统一处理页码返回0的问题
    //   if (res.data && res.data.currentPage === 0) {
    //     res.data.currentPage = 1
    //   }
    //   res.config = response.config
    //   return res
    // }
  },
  error => {
    if (error.message.includes("timeout")) {
      // 判断请求异常信息中是否含有超时timeout字符串
      Message({
        message: i18n.t("lang.rms.fed.timeout"),
        type: "error",
        duration: 5 * 1000,
      });
    }

    return Promise.reject(error);
  },
);

export default service;
