<template>
  <div>
    <SearchWrap>
      <SearchForm @onsubmit="onSubmit" />
    </SearchWrap>
    <div style="text-align: right; margin-bottom: 10px">
      <el-button type="primary" @click="addListItem"
        >{{ this.$t("lang.rms.fed.add") }}{{ this.$t("lang.rms.fed.shapedShelfModelName") }}</el-button
      >
    </div>

    <el-table :loading="loading" :data="recordList" style="width: 100%">
      <el-table-column :label="$t('lang.rms.web.container.containerModelId')" prop="id" />
      <el-table-column :label="$t('lang.rms.web.container.containerModelAlias')" prop="modelName" />
      <el-table-column :label="$t('lang.rms.web.container.containerType')" prop="modelCategory" />
      <!-- 货架类别 -->
      <el-table-column :label="$t('lang.rms.fed.classCode')" prop="modelType" />
      <el-table-column :label="$t('lang.rms.web.container.containerPhysicalProperties')" prop="lwh" />
      <!-- <el-table-column label="货位详情" prop="protocolModelEnt" /> -->
      <el-table-column :label="$t('lang.rms.web.container.supportMove')" prop="isMove" />
      <el-table-column :label="$t('lang.rms.web.container.fetchDirs')" prop="fetchDirsStr">
        <template slot-scope="scope">
          <span>
            {{ scope.row.fetchDirsStr ? scope.row.fetchDirsStr.toString() : "" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('lang.rms.containerManage.sendModelId.msg')" prop="sendModelId" />
      <el-table-column :label="$t('lang.rms.fed.operation')" width="180" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="editListItemInfo(scope.row, 'view')">
            {{ $t("lang.rms.fed.buttonView") }}
          </el-button>

          <el-button type="text" size="small" @click="editListItemInfo(scope.row, 'edit')">
            {{ $t("lang.rms.fed.edit") }}
          </el-button>
          <el-button type="text" size="small" @click="deleteListItemInfo(scope.row)">
            {{ $t("lang.rms.fed.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="text-align: right; margin-top: 30px">
      <el-pagination
        background
        layout="total,prev, pager, next, sizes, jumper"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="paginationParams.total"
        :page-size="paginationParams.pageSize"
        :current-page="paginationParams.currentPage"
        @current-change="paginationChange"
        @size-change="paginationPageChange"
      />
    </div>
  </div>
</template>

<script>
import SearchWrap from "../Components/SearchWrap";
import SearchForm from "../Components/SearchForm";
import { getContainerModelData, deleteContainerModel } from "@/api/containerManage/index";
import { mapGetters, mapMutations, mapActions } from "vuex";

const ableViewEditType = ["HETEROTYPE_SHELF"];
export default {
  name: "",
  components: {
    SearchWrap,
    SearchForm,
  },
  data() {
    return {
      recordList: [],
      loading: false,
      paginationParams: { pageSize: 10, currentPage: 1, total: 0 },
      searchData: {},
      ableViewEditType,
    };
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    ...mapMutations("containerModal", [
      "setSpecialShapedShelfModelView",
      "setSpecialShapedShelfModelViewData",
      "setSpecialShapedShelfModelViewType",
    ]),
    ...mapActions("containerModal", ["fetchMaxModelId", "fetchShelfCategory"]),
    // 查看/编辑
    editListItemInfo(row, type) {
      this.setSpecialShapedShelfModelViewData(row);
      this.setSpecialShapedShelfModelViewType(type);
      this.setSpecialShapedShelfModelView("editView");
    },
    addListItem() {
      this.setSpecialShapedShelfModelViewData(null);
      this.setSpecialShapedShelfModelViewType("add");
      this.setSpecialShapedShelfModelView("editView");
    },
    // 删除
    deleteListItemInfo(row) {
      this.$confirm(this.$t('lang.rms.fed.confirmDelete'), this.$t('lang.rms.fed.prompt'), {
        confirmButtonText: this.$t('lang.rms.fed.confirm'),
        cancelButtonText: this.$t('lang.common.cancel'),
        type: "warning",
      })
        .then(() => {
          deleteContainerModel({ id: row.id }).then(res => {
            if (res.code === 0) {
              this.$message({
                message: this.$t('lang.rms.fed.deleteSuccessfully'),
                type: "success",
              });
              this.getTableData({ currentPage: 1 });
            }
          });
        })
        .catch(() => null);
    },
    paginationChange(currentPage) {
      this.searchData.currentPage = currentPage;
      this.paginationParams.currentPage = currentPage;
      this.getTableData();
    },
    paginationPageChange(pageSize) {
      this.paginationParams.currentPage = 1;
      this.searchData.pageSize = pageSize;
      this.paginationParams.pageSize = pageSize;
      this.getTableData();
    },
    onSubmit(data) {
      this.searchData = Object.assign({}, data);
      this.searchData.currentPage = 1;
      this.paginationParams.currentPage = 1;
      this.searchData.pageSize = this.paginationParams.pageSize;

      this.getTableData();
    },
    async getTableData(data = {}) {
      this.loading = true;
      const result = await getContainerModelData({ ...this.searchData, modelCategory: "HETEROTYPE_SHELF", ...data });
      this.loading = false;
      this.paginationParams.total = result.data.recordCount;
      this.recordList = result.data.recordList.map(e => {
        e.lwh = `${e.length ?? 0}、${e.width ?? 0}、${e.height ?? 0}`;
        e.isMove = e.move === 0 ? this.$t('lang.rms.web.container.canNotMove') : e.move === 1 ? this.$t('lang.rms.web.container.canMove') : "";
        return e;
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
