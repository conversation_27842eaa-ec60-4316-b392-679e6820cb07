import {Graphics, Sprite, Texture} from "pixi.js";
import Line from './baseElement/Line'
import Polygon from "./baseElement/Polygon";
import CtrlPoint from './baseElement/CtrlPoint'
//选中态
import {areaStyle} from '../config'
const {INACTIVE_AREA,ACTIVE_AREA} = areaStyle
//导入全局控制器
import Control from "../control/Control";
import Event from "../event/Event";
import EventBus from "../eventBus/EventBus";
import LayerManager from '../layerManager/LayerManager'
import {cad2pixi, pixi2cad} from "../utils/utils";
import Selected from "../selected/Selected";
export default class Area {
  //创建
  static add(data) {
    const {controlPoints,id,isRect} = data
    if(!controlPoints ||controlPoints.length < 3) return null
    //将cad坐标转化为pixi坐标
    const pixiControlPoints = controlPoints.map(p => {
      return cad2pixi(p)
    })
    const $con = this._createEditPolygon(pixiControlPoints)
    $con.type = 'area'
    $con.id = id
    $con.isRect = isRect
    return $con
  }
  //更新
  static update($el,item){
    const $points = $el.children.filter(child => child.name === 'point')
    const $line = $el.getChildByName('line')
    const $polygon = $el.getChildByName('polygon')
    const {controlPoints,id} = item
    const paths = controlPoints.map((p,index) =>{
      const pos = cad2pixi(p)
      Object.assign($points[index],pos)
      // $points[index].visible = true
      return pos
    })
    $polygon.originalPaths = paths
    $line.clear()
    $polygon.clear()
    //闭合数据
    const closePaths = [...paths,paths[0]]
    // console.log(Selected.getSelected(id))
    const color = Selected.getSelected(id) ? ACTIVE_AREA : INACTIVE_AREA
    Line.render($line,closePaths,{color})
    Polygon.render($polygon,closePaths,{color})
  }
  //面渲染
  static _renderPolygon($el,paths){
    const $line = $el.getChildByName('line')
    const $polygon = $el.getChildByName('polygon')
    //闭合数据
    const closePaths = [...paths,paths[0]]
    $line.clear()
    $polygon.clear()
    Line.render($line,closePaths,{color: ACTIVE_AREA})
    Polygon.render($polygon,closePaths,{color: ACTIVE_AREA})
  }
  //创建编辑面
  static _createEditPolygon(paths) {
    // if(paths.length < 3) return;
    //闭合数据
    const closePaths = [...paths,paths[0]]
    const $con = new Graphics();
    $con.interactive = true
    $con.interactiveChildren = true
    $con.cursor = 'pointer'
    //渲染线
    const $line = new Graphics()
    $line.name = 'line'
    $line.zIndex = 2
    Line.render($line,closePaths,{color: INACTIVE_AREA})
    //渲染面
    const $polygon = new Graphics()
    Polygon.render($polygon,closePaths,{color: INACTIVE_AREA})
    //存储原始path坐标
    $polygon.originalPaths = paths
    $polygon.name = 'polygon'
    $polygon.interactive = true
    $polygon.zIndex = 1
    $con.addChild($line)
    $con.addChild($polygon)
    //拖拽面更新
    const dragAreaUpdate = () => {
      $polygon.dragging = false
      $polygon.clickPos = null
      Control.enableDrag(true)
      if($polygon.isUpdate){
        this._updateFinished($con)
        $polygon.isUpdate = false
      }
    }
    //拖拽点更新面
    const dragPointUpdate = ($p) => {
      Control.enableDrag(true)
      $p.alpha = 1;
      $p.dragging = false;
      if($p.isUpdate){
        this._updateFinished($con)
        $p.isUpdate = false
      }
    }
    //面拖拽逻辑
    $polygon
      .on('pointerdown',function(e) {
        if(Event.activeKey !== 'Alt') return this.dragging = false;
        Control.enableDrag(false)
        this.clickPos = e.data.getLocalPosition($con)
        this.dragging = true;
      })
      .on('pointerup',(e) => {
        dragAreaUpdate()
      })
      .on('pointerupoutside',(e) => {
        dragAreaUpdate()
      })
      .on('pointermove',(e) => {
        const {dragging,clickPos} = $polygon
        if(dragging){
          const {x:cx,y:cy} = clickPos
          const {x:mx,y:my} = e.data.getLocalPosition($con);
          //位移量
          const tx = mx - cx
          const ty = my - cy
          //离点击点的相对距离
          const movePos = {x:tx,y:ty}
          this._dragUpdatePolygon($polygon.parent,movePos)
          $polygon.isUpdate = true
        }
      })
    //渲染点
    paths.forEach((p,pIndex) => {
      const {x,y} = p
      const $point = CtrlPoint.render(x,y)
      $point.visible = false
      $point.name = 'point'
      //
      $point
        .on('pointerdown',(e) => {
          Control.enableDrag(false)
          $point.alpha = 0.5;
          $point.dragging = true;
        })
        .on('pointerup', (e) => {
          dragPointUpdate($point)
        })
        .on('pointerupoutside', () => {
          dragPointUpdate($point)
        })
        .on('pointermove',(e) => {
          if ($point.dragging) {
            const newPosition = e.data.getLocalPosition($con);
            //设置为更新态
            if($point.x !== newPosition.x || $point.y !== newPosition.y) {
              $point.isUpdate = true
            }
            $point.x = newPosition.x;
            $point.y = newPosition.y;
            if($con.isRect){
              this._updateEditRect($con,pIndex)
            }else{
              this._updateEditPolygon($con)
            }
          }
        })

      $point.zIndex = 3
      $con.addChild($point)
    })
    return $con
  }

  //拖拽更新
  static _dragUpdatePolygon($el,movePos){
    const $points = $el.children.filter(child => child.name === 'point')
    const $polygon = $el.getChildByName('polygon')
    //原始坐标
    const {originalPaths} = $polygon
    const {x:tx,y:ty} = movePos
    const paths = originalPaths.map((p,index) => {
      const {x,y} = p
      const newPos = {x:x+tx,y:y+ty}
      Object.assign($points[index],newPos)
      return newPos
    })
    this._renderPolygon($el,paths)
  }
  //更新完成
  static _updateFinished($el){
    const $points = $el.children.filter(child => child.name === 'point')
    const newPaths = $points.map(p => {
      return {x:p.x,y:p.y}
    })
    const $polygon = $el.getChildByName('polygon')
    $polygon.originalPaths = newPaths
    //将pixi转cad坐标
    const cadPaths = $points.map(p => {
      const {x,y} = p
      return pixi2cad({x,y})
    })
    LayerManager.updateElements({
      id:'AREA',
      data:[{
        id:$el.id,
        controlPoints: cadPaths
      }]
    })
    EventBus.$emit('AREA:UPDATED')
  }
  //更新多边形面
  static _updateEditPolygon($el) {
    const $points = $el.children.filter(child => child.name === 'point')
    const $polygon = $el.getChildByName('polygon')
    const paths = $points.map(p => {
      const {x,y} = p
      return {x,y}
    })
    this._renderPolygon($el,paths)
    //存储原始点位数据
    $polygon.originalPaths = paths
  }
  //更新矩形面
  static _updateEditRect($el,pIndex) {
    //获取相邻点的index
    const getAdjacentPointIndex = (pIndex) => {
      if (pIndex === 0) {
        return [1, 3]
      } else if (pIndex === 1) {
        return [0, 2]
      } else if (pIndex === 2) {
        return [3, 1]
      } else if (pIndex === 3) {
        return [2, 0]
      }
    }
    const $points = $el.children.filter(child => child.name === 'point')
    const $polygon = $el.getChildByName('polygon')
    const adjacentIndex = getAdjacentPointIndex(pIndex)
    const {x:mx,y:my} = $points[pIndex]
    $points[adjacentIndex[0]].y = my
    $points[adjacentIndex[1]].x = mx
    const paths = $points.map(p => {
      const {x,y} = p
      return {x,y}
    })
    this._renderPolygon($el,paths)
    //存储原始点位数据
    $polygon.originalPaths = paths
  }
}
