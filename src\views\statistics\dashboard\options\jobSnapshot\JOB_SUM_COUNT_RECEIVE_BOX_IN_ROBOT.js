export default {
  x: 32, 
  y: 63,
  width: 16, 
  height: 10,

  // 地图数据信息
  chart: {
    type: 'line', // 图表类型

    // 地图数据来源
    request: {
      url: '/athena/stats/query/job/snapshot',  // 请求接口
      filters: ['date', 'cycle'], // 筛选条件
      defFilters: {
        date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
        cycle : "5",
      },
      timer: 5000,  // 轮询时间, 如果是0, 则不轮询
    },

    // 地图数据处理
    dataHandler: {  // 数据处理
      handler: 'getJobSnapshot',
      params: {
        type: 'line',
        paramsName: 'JOB_SUM_COUNT_RECEIVE_BOX_IN_ROBOT',
        title: '任务下发时候货箱在机器人身上'
      },
    },
  }
}