<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2022-01-05 20:04:30
 * @Description:
-->
<template>
  <el-form
    ref="searchForm"
    :inline="true"
    class="demo-form-inline"
    label-position="top"
    :model="searchForm"
  >
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotModel')" prop="product">
      <el-input
        v-model.trim="searchForm.product"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterRobotModel')"
      />
    </el-form-item>
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotModelAlias')" prop="displayName">
      <el-input
        v-model.trim="searchForm.displayName"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterRobotModeAlias')"
      />
    </el-form-item>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.ontologyModel')"
      prop="chassisModelEnt.name"
    >
      <el-input
        v-model.number="searchForm.chassisModelEnt.name"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterOntologyModel')"
      />
    </el-form-item>
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.businessModel')"
      prop="businessModelEnt.name"
    >
      <el-input
        v-model.trim="searchForm.businessModelEnt.name"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterBusinessCharacteristics')"
      />
    </el-form-item>
    <!-- <el-form-item :label="$t('lang.rms.fed.protocolModelEnt')" prop="protocolModelEnt.name">
      <el-input
        v-model.number="searchForm.protocolModelEnt.name"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterProtocolModel')"
      />
    </el-form-item> -->
    <!--    <el-form-item label="机构模型id" prop="mechanismModelId">-->
    <!--      <el-input v-model="searchForm.mechanismModelId" placeholder="请输入机构模型id" />-->
    <!--    </el-form-item>-->
    <el-form-item class="align-bottom">
      <el-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.query") }}</el-button>
      <el-button type="primary" @click="resetForm">{{ $t("lang.rms.fed.reset") }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    // 这里存放数据
    return {
      protocolList: [],
      searchForm: {
        product: "",
        displayName: "",
        chassisModelEnt: {
          name: null,
        },
        businessModelEnt: {
          name: null,
        },
        protocolModelEnt: {
          name: null,
        },
        // mechanismModelId: ''
      },
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  mounted() {
    // this.getProtocolList()
    this.onSubmit();
  },
  // 方法集合
  methods: {
    onSubmit() {
      this.$emit("onsubmit", { ...this.searchForm });
    },
    resetForm() {
      this.$refs["searchForm"].resetFields();
      this.onSubmit();
    },
    async getProtocolList() {
      const { code, data } = await $req.get("/athena/robot/manage/findRobotProtocols");
      this.protocolList = code ? [] : data;
    },
  },
};
</script>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}
</style>
<style lang="scss" scoped>
.align-bottom {
  vertical-align: bottom;
}
</style>
