/* ! <AUTHOR> at 2022/08/29 */
import { message } from "antd";
import { i18n } from "../lang/i18n";
import service from "./axios_service";

const utils: typeof _$utils = {
  __isMapDomMounted: false,
  getUrlParameter(name: string): string {
    let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    let r = window.location.search.substring(1).match(reg);
    if (r != null) return decodeURIComponent(r[2]);
    return null;
  },

  getLocalLang(): string {
    let lang = localStorage.getItem("curRMSLanguage");
    if (lang) return lang;
    return localStorage.getItem("curLanguage") || "zh_cn";
  },

  getRMSPermission(): boolean | "test" {
    const str = localStorage.getItem("Geek_RMSPermission");
    if (str == null) {
      return "test";
    }
    return str === "true";
  },
  // userInfo相关
  getRoleInfo() {
    let roleInfo = localStorage.getItem("Geek_roleInfo");
    if (this.getRMSPermission()) {
      return roleInfo;
    } else {
      return roleInfo || "guest";
    }
  },

  getRMSConfig(): any {
    let conf = localStorage.getItem("Geek_RMSConfig");
    if (conf) return JSON.parse(conf);
    else return conf;
  },

  getRMSAuthBtnList(): any {
    let conf = localStorage.getItem("Geek_RMSAuthBtnList");
    if (conf) return JSON.parse(conf);
    else return conf;
  },
  getRMSAuthTabList(): any {
    let conf = localStorage.getItem("Geek_RMSAuthTabList");
    if (conf) return JSON.parse(conf);
    else return conf;
  },

  getRMSFEDConfig(key) {
    let conf = localStorage.getItem("Geek_RMSFEDConfig");

    if (!conf) return null;

    let confJSON = JSON.parse(conf);
    if (key) return confJSON[key];
    else return confJSON;
  },

  setRMSFEDConfig(key, data) {
    if (!key) return;
    let conf = this.getRMSFEDConfig() || {};
    conf[key] = data;
    localStorage.setItem("Geek_RMSFEDConfig", JSON.stringify(conf));
  },

  transMsgLang(key: any): any {
    if (!key) return "";
    let json;
    try {
      json = JSON.parse(key);
      const type = Object.prototype.toString.call(json);
      if (type === "[object Object]") {
        if (json.p) {
          return i18n.t(
            json.c,
            json.p.map((x: any) => this.transMsgLang(x)),
          );
        } else {
          return i18n.t(json.c);
        }
      } else {
        return i18n.t(json);
      }
    } catch (e) {
      const arr = key.split(",");
      const code = arr.shift();
      if (arr.length > 0) {
        return i18n.t(code, { ...arr });
      }
      return i18n.t(code);
    }
  },

  getDataType(data: any): string {
    return Object.prototype.toString.call(data);
  },

  formatDate(date: any, fmt: string) {
    if (!date) return "";
    if (Object.prototype.toString.call(date) !== "[object Date]") {
      date = new Date(date);
    }
    let o: any = {
      "M+": date.getMonth() + 1, // 月份
      "d+": date.getDate(), // 日
      "h+": date.getHours(), // 小时
      "m+": date.getMinutes(), // 分
      "s+": date.getSeconds(), // 秒
      "q+": Math.floor((date.getMonth() + 3) / 3), // 季度
      S: date.getMilliseconds(), // 毫秒
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substring(4 - RegExp.$1.length));
    }
    for (let k in o) {
      if (new RegExp("(" + k + ")").test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length === 1 ? o[k] : ("00" + o[k]).substring(("" + o[k]).length),
        );
      }
    }
    return fmt;
  },

  sendParentIframe(data: any) {
    window.parent.postMessage(data, window.location.origin);
  },

  reqLangMsg(localLang: string): Promise<any> {
    const baseLangCodeUrl = "/athena/api/coreresource/i18n/getLangItems/v1?languageCode=";
    return new Promise((resolve, reject) => {
      service({ method: "get", url: baseLangCodeUrl + localLang })
        .then(xhr => {
          return xhr.data;
        })
        .then(res => {
          if (res.code === 0) resolve(res);
          else reject(res);
        })
        .catch(error => {
          console.error(error);
          // reject(error);
        });
    });
  },

  reqGet(url: string, params: any, headers: any = null): Promise<any> {
    let options: any = { method: "get", url };
    if (params) options.params = params;
    if (headers) options.headers = headers;

    return new Promise((resolve, reject) => {
      service(options)
        .then(xhr => {
          return xhr.data;
        })
        .then(res => {
          if (res.code === 0) resolve(res);
          else reject(res);
        })
        .catch(error => {
          console.error(error);
          reject(error);
        });
    });
  },

  reqPost(url: string, data: any = {}, headers: any = null): Promise<any> {
    let options: any = { method: "post", url };
    if (data) options.data = data;
    if (headers) options.headers = headers;

    return new Promise((resolve, reject) => {
      service(options)
        .then(xhr => {
          return xhr.data;
        })
        .then(res => {
          if (res.code === 0) resolve(res);
          else reject(res);
        })
        .catch(error => {
          console.error(error);
          reject(error);
        });
    });
  },

  wsCmdResponse(body: any) {
    const code = body && body.hasOwnProperty("code") ? body.code : -1;
    const msg = this.transMsgLang(body?.msg || "");
    if (code === 0) message.success(msg);
    else message.error(msg);
  },

  debounce(func: (...args: any[]) => void, wait: number, immediate: boolean) {
    let timeout: any, result: any;

    let fn: any = function (this: any) {
      let context = this;
      let args: any = arguments;

      if (timeout) clearTimeout(timeout);
      if (immediate) {
        // 如果已经执行过，不再执行
        let callNow = !timeout;
        timeout = setTimeout(function () {
          timeout = null;
        }, wait);
        if (callNow) result = func.apply(context, args);
      } else {
        timeout = setTimeout(function () {
          func.apply(context, args);
        }, wait);
      }
      return result;
    };

    fn.cancel = function () {
      clearTimeout(timeout);
      timeout = null;
    };

    return fn;
  },
};
if (!__rms_env_conf) {
  window.__rms_env_conf = {
    isPro: true,
  };
}

window._$utils = utils;
