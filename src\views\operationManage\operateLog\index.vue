<template>
  <geek-main-structure class="exception-hand-box">
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    </div>
    <div class="table-content">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="source" :label="$t('lang.rms.fed.fetchType')" width="100" />
        <el-table-column
          prop="module"
          :label="$t('lang.rms.fed.operateModule')"
          :formatter="formatModule"
          width="100"
        />
        <el-table-column prop="operator" :label="$t('lang.rms.fed.operator')" width="100" />
        <el-table-column prop="createTime" :label="$t('lang.rms.fed.operateTime')" width="160" />
        <el-table-column prop="clientIp" :label="$t('lang.rms.fed.customIP')" width="120" />
        <el-table-column prop="operatorType" :label="$t('lang.rms.fed.fetchApi')" width="180" />
        <el-table-column prop="request" :label="$t('lang.rms.fed.reqParams')" width="180">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.request" placement="left">
              <template #content>
                <pre><code class="json-core" v-html="formatterJson(scope.row.request)"></code></pre>
              </template>
              <el-button type="text ell w100">{{ scope.row.request }}</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="response" :label="$t('lang.rms.fed.resParams')">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.response" placement="left">
              <template #content>
                <pre><code class="json-core" v-html="formatterJson(scope.row.response)"></code></pre>
              </template>
              <el-button type="text ell w100">{{ scope.row.response }}</el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: right">
        <geek-pagination
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          :total-page="pageCount"
          @currentPageChange="currentPageChange"
          @pageSizeChange="pageSizeChange"
        />
      </div>
    </div>
  </geek-main-structure>
</template>

<script>
export default {
  data() {
    return {
      // 搜索条件
      form: {
        source: "",
        clientIp: "",
        operator: "",
        dataRange: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          source: {
            label: "lang.rms.fed.fetchType",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          clientIp: {
            label: "lang.rms.fed.customIP",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          operator: {
            label: "lang.rms.fed.operator",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          dataRange: {
            label: "lang.rms.fed.operateTime",
            default: "",
            valueFormat: "yyyy-MM-dd HH:ss:mm",
            type: "datetimerange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": "lang.rms.fed.startTime",
            "end-placeholder": "lang.rms.fed.endTime",
          },
        },
        rules: [],
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      page: {
        currentPage: 1,
        pageSize: 10,
      },
      tableData: [], // 设置table的数据
      pageCount: 0, // 当前数据总数
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    getTableList() {
      const { source, clientIp, operator, dataRange } = this.form;
      let temporaryTime = dataRange ? dataRange.map(item => new Date(item).getTime()) : "";
      const data = {
        source,
        clientIp,
        operator,
        startTime: temporaryTime && temporaryTime[0] ? temporaryTime[0] : "",
        endTime: temporaryTime && temporaryTime[1] ? temporaryTime[1] : "",
      };
      const pageData = "?currentPage=" + this.page.currentPage + "&pageSize=" + this.page.pageSize;
      $req.post("/athena/operatorlog/findAll" + pageData, data).then(res => {
        let result = res.data;
        if (result != null) {
          this.tableData = result.recordList;
          this.page.currentPage = result.currentPage || 1;
          this.pageCount = result.pageCount;
        }
      });
    },
    // 分页
    currentPageChange(val) {
      this.page.currentPage = val;
      this.getTableList();
    },
    // 改变每页显示条数
    pageSizeChange(val) {
      this.page.pageSize = val;
      this.getTableList();
    },
    onQuery(val) {
      this.page.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.page.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    formatModule(row, column) {
      return this.$t(row[column["property"]]);
    },
    formatterJson(data) {
      let obj = data;
      try {
        // 防止不标准格式JSON
        obj = JSON.parse(data);
      } catch (e) {
        console.log(e);
      }
      return Array.isArray(obj) ? data : JSON.stringify(obj, null, 2);
    },
  },
};
</script>

<style lang="less" scoped>
.form-content {
  border-bottom: 5px solid #eee;
  padding-bottom: 10px;
}

.table-content {
  padding-top: 15px;

  .btn-opt {
    padding: 3px 5px;
    min-height: 10px;
  }
}
</style>
