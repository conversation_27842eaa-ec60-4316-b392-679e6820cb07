<template>
  <geek-main-structure class="pack-query">
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-return="onReturn" />
    </div>
    <div class="table-content">
      <el-table :data="tableData" style="width: 100%" @selection-change="handleSelection">
        <el-table-column
          type="selection"
          :selectable="row => row.boxStatus === 'LOADED'"
        ></el-table-column>
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="boxCode" :label="$t('lang.rms.box.boxCode')" width="100" />
        <el-table-column :label="$t('lang.rms.box.boxStatus')" width="80">
          <template slot-scope="scope">{{ $t(scope.row.boxStatusCode) }}</template>
        </el-table-column>
        <el-table-column prop="robotId" :label="$t('lang.rms.fed.robot') + 'ID'" width="100" />
        <el-table-column prop="robotLayer" :label="$t('lang.rms.box.robotLayer')" width="80" />
        <el-table-column prop="placeLatticeCode" :label="$t('lang.rms.box.placeLatticeCode')" />
        <!-- 摆放位置 老家位置-->
        <el-table-column :label="$t('lang.rms.box.boxPlaceRackCode')" width="180">
          <template slot-scope="scope">
            <span v-if="scope.row.location">
              X:{{ scope.row.location.x }},Y:{{ scope.row.location.y }}
            </span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('lang.rms.fed.operation')" width="160">
          <template slot-scope="scope">
            <div v-if="scope.row.boxStatus === 'POSITION_CONFIRMING'">
              <el-button type="primary" size="mini" class="btn-opt" @click="itemSave(scope.row)">
                {{ $t("lang.rms.fed.confirm") }}
              </el-button>
              <el-button type="primary" size="mini" class="btn-opt" @click="itemChange(scope.row)">
                {{ $t("lang.rms.box.changeBtn") }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: right">
        <geek-pagination
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          :total-page="pageCount"
          @currentPageChange="currentPageChange"
          @pageSizeChange="pageSizeChange"
        />
      </div>
    </div>

    <edit-dialog ref="editDialog" @updateTableList="getTableList" />
  </geek-main-structure>
</template>

<script>
// lang.rms.fed.containerGoReturnArea 选择货箱归还区域
import EditDialog from "./components/editDialog";

export default {
  name: "PackQueryIndex",
  components: { EditDialog },
  data() {
    return {
      // 搜索条件
      form: {
        robotId: "",
        boxStatus: "",
        boxCode: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          robotId: {
            label: "lang.rms.fed.inputRobotId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          boxStatus: {
            label: "lang.rms.box.boxStatus",
            default: null,
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "",
                label: "lang.rms.fed.wholeStatus",
              },
              {
                value: 0,
                label: "lang.rms.fed.no",
              },
              {
                value: 1,
                label: "lang.rms.fed.yes",
              },
            ],
          },
          boxCode: {
            label: "lang.rms.box.boxCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
        },
        rules: [],
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.box.returnAllLoadedBox",
            handler: "on-return",
            type: "success",
          },
        ],
      },
      page: {
        currentPage: 1,
        pageSize: 10,
      },
      tableData: [], // 设置table的数据
      pageCount: 0, // 当前数据总数
      selectList: null, // 可进行一键还箱
    };
  },
  computed: {
    returnArr() {
      let arr = [];
      this.tableData.forEach(item => {
        if (item.boxStatus === "LOADED") {
          arr.push(item.boxCode);
        }
      });
      return arr;
    },
  },
  watch: {
    returnArr(v) {
      let formConfig = this.formConfig;
      for (let key in formConfig.configs) {
        formConfig.configs[key].default = this.form[key];
      }
      formConfig.operations[1].disabled = v.length === 0;

      this.formConfig = Object.assign({}, formConfig);
    },
  },
  activated() {
    this.getTableList();
    this.getBoxStatus();
  },
  methods: {
    itemSave(row) {
      $req.post("/athena/box/confirmBoxStatus", { boxCode: row.boxCode }).then(res => {
        if (res.code === 0) {
          this.$success(this.$t("lang.common.success"));
          this.getTableList();
        }
      });
    },
    itemChange(row) {
      this.$refs.editDialog.open(row);
    },
    handleSelection(selection) {
      this.selectList = selection.map(i => i.boxCode);
    },
    // 归还
    async onReturn() {
      if (this.selectList && this.selectList.length) {
        const title = this.$t("lang.rms.fed.region");
        const tips = this.$t("lang.rms.fed.containerGoReturnArea");
        const that = this;
        this.$prompt(title, tips, {
          closeOnClickModal: false,
          inputValidator(val) {
            if (!val) return true;
            if (!/^[+]{0,1}(\d+)$/.test(val)) return that.$t("lang.rms.fed.pleaseEnterAnNumber");
            return true;
          },
          async beforeClose(action, instance, done) {
            if (action !== "confirm") return done();
            instance.confirmButtonLoading = true;
            const areaId = instance.inputValue || "";
            try {
              const { code, msg } = await $req.post("/athena/box/returnAllLoadedBox", {
                boxCodes: that.selectList,
                areaId,
              });
              instance.confirmButtonLoading = false;
              if (!code) {
                that.$message.success(that.$t("lang.common.success"));
                done();
                that.getTableList();
              }
            } catch (e) {
              instance.confirmButtonLoading = false;
            }
          },
        });
      }
      // $req.post("/athena/box/returnAllLoadedBox", { boxCodes: this.returnArr }).then(res => {
      //   if (res.code === 0) {
      //     this.$success(this.$t("lang.common.success"));
      //     this.getTableList();
      //   }
      // });
    },
    // 分页
    currentPageChange(val) {
      this.page.currentPage = val;
      this.getTableList();
    },
    // 改变每页显示条数
    pageSizeChange(val) {
      this.page.pageSize = val;
      this.getTableList();
    },
    onQuery(val) {
      this.page.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.page.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getBoxStatus() {
      $req.get("/athena/box/listBoxStatus").then(res => {
        let list = res.data || [];
        this.formConfig.configs.boxStatus.options = list.map(item => {
          return {
            value: item.status,
            label: this.$t(item.statusCode),
          };
        });
      });
    },
    getTableList() {
      const params = {
        robotId: this.form.robotId || null,
        boxStatus: this.form.boxStatus || null,
        boxCode: this.form.boxCode || null,
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
      };
      $req.get("/athena/box/list", params).then(res => {
        let result = res.data;
        if (result != null) {
          this.tableData = result.recordList;
          this.page.currentPage = result.currentPage || 1;
          this.pageCount = result.pageCount;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.form-content {
  border-bottom: 5px solid #eee;
  padding-bottom: 10px;
}

.table-content {
  padding-top: 15px;

  .btn-opt {
    padding: 3px 5px;
    min-height: 10px;
  }
}
</style>
