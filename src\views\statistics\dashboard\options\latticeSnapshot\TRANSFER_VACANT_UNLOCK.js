export default {
  x: 24, 
  y: 83,
  width: 24, 
  height: 10,

  // 地图数据信息
  chart: {
    type: 'line', // 图表类型

    // 地图数据来源
    request: {
      url: '/athena/stats/query/lattice/snapshot',  // 请求接口
      filters: ['date', 'cycle'], // 筛选条件
      defFilters: {
        date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
        cycle : "5",
      },
      timer: 5000,  // 轮询时间, 如果是0, 则不轮询
    },

    // 地图数据处理
    dataHandler: {  // 数据处理
      handler: 'getLatticeSnapshot',
      params: {
        type: 'line',
        paramsName: 'TRANSFER_VACANT_UNLOCK',
        title: '缓存货位空闲数量'
      },
    },
  }
}