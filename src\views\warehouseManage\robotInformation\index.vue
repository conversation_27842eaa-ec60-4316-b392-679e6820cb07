<!--机器人信息页面-->
<template>
  <geek-main-structure id="J_robotInfoContent" class="robot-information">
    <section v-if="!isOnlyContent" id="J_robotInfoFormContent" class="form-content">
      <el-form label-position="top" :inline="true" label-width="80px">
        <el-form-item :label="$t('lang.rms.fed.robot') + 'ID'">
          <geek-fuzzy-search
            :id="form.robotIds"
            query-type="Qrobot"
            @fuzzySearchBub="fuzzySearchBub"
          />
        </el-form-item>
        <el-form-item :label="`${$t('lang.rms.fed.workstation')}ID`">
          <geek-fuzzy-search
            :id="form.workStationId"
            query-type="Qstation"
            @fuzzySearchBub="fuzzySearchBub"
          />
        </el-form-item>
        <el-form-item :label="$t('lang.rms.web.monitor.robot.errorLevel')">
          <el-select v-model="form.errorInfo">
            <el-option
              v-for="item in errorList"
              :key="item.value"
              :label="$t(item.label)"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.robotPower')" style="padding: 0 16px">
          <el-slider v-model="form.power" range :max="100" style="width: 120px" />
        </el-form-item>
        <el-form-item
          :label="$t('lang.rms.fed.taskType')"
          :placeholder="$t('lang.rms.fed.pleaseChoose')"
        >
          <el-select v-model="form.taskType" filterable>
            <el-option
              v-for="(item, index) in taskTypeList"
              :key="index"
              :label="$t(item)"
              :value="index"
            />
          </el-select>
        </el-form-item>

        <el-form-item class="operation-button">
          <el-button type="primary" @click="onQuery">
            {{ $t("lang.rms.fed.query") }}
          </el-button>
          <el-button @click="onReset">{{ $t("lang.rms.fed.reset") }}</el-button>
        </el-form-item>
      </el-form>
    </section>

    <section class="table-content">
      <div id="J_robotInfoTableHeader">
        <h3 class="table-content-header">
          <span>{{ $t("lang.rms.fed.robotInformation") }}</span>

          <span class="fr">
            {{ $t("lang.rms.fed.totalNumberOfCurrentRobots") }}：{{ stat.totalCount }};
            {{ $t("lang.rms.fed.abnormal") }} {{ $t("lang.rms.fed.function.groupValue") }}：{{
              stat.exceptionCount
            }}； {{ $t("lang.rms.fed.atWork") }}：{{ stat.workingCount }}；
            {{ $t("lang.rms.fed.charging") }}：{{ stat.chargingCount }}；
            {{ $t("lang.rms.fed.systemRemoval") }}：{{ stat.removedCount }}；
          </span>
          <span class="header_dropdown fr">
            <el-dropdown split-button>
              <span class="el-dropdown-link">
                {{ $t("lang.rms.fet.tableHeadCheck") }}
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-tree
                  ref="headerTree"
                  :data="treeDataHeaderList"
                  node-key="id"
                  default-expand-all
                  draggable
                  show-checkbox
                  :allow-drop="allowDrop"
                  class="tree-box"
                  :props="props"
                  @check-change="handleCheckChange"
                />
              </el-dropdown-menu>
            </el-dropdown>
          </span>
        </h3>

        <!-- tips -->
        <div class="tip">
          <i class="el-icon-warning-outline tip-icon" />
          {{ $t("lang.rms.fed.horizontalScrollTips") }}
        </div>
      </div>

      <el-slider
        v-model="sliderValue"
        class="scroll-slider"
        vertical
        :step="sliderStep"
        :height="`${tableHeight}px`"
        :show-tooltip="false"
        :min="0"
        :max="maxLen"
        :debounce="0"
        @input="sliderChange"
      />
      <el-table
        ref="robotList"
        :data="robotList"
        style="width: 100%"
        :height="tableHeight"
        :row-class-name="robotTypeClassName"
      >
        <el-table-column
          v-for="item in robotTableHeaderList"
          :key="item.key"
          :fixed="item.key === 'id' ? 'left' : false"
          :prop="item.key"
          :width="item.width"
          :label="$t(item.value)"
          :sortable="item.sortable"
        >
          <template slot-scope="scope">{{ nullChange(scope.row[item.key]) }}</template>
        </el-table-column>
        <!-- <el-table-column
          :label="$t('lang.rms.fed.operation')"
          :width="isRoleGuest ? 150 : 230"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button v-if="!isRoleGuest" @click="handleEdit(scope.row.id)">
              {{ $t("lang.rms.fed.paramConfig") }}
            </el-button>
            <el-button size="small" @click="itemRobotRecord(scope.row)">
              {{ $t("lang.rms.fed.robotParamRecord") }}
            </el-button>
          </template>
        </el-table-column> -->
      </el-table>
    </section>

    <params-config-dialog ref="paramsConfigDialog" />
    <robot-param-records-dialog ref="recordDialog" />
  </geek-main-structure>
</template>

<script>
import ParamsConfigDialog from "./components/params-config-dialog";
import robotParamRecordsDialog from "./components/dialogRobotParamRecords";

let allRobotList = [];
let resizeDebounce = null;
let onkeydown = null;
let onkeyup = null;
let $domObj = null;
let $table = null;
let size = -1;
export default {
  name: "RobotInformation",
  components: {
    ParamsConfigDialog,
    robotParamRecordsDialog,
  },
  data() {
    return {
      isOnlyContent: !!this.$route.query.onlycontent,
      form: {
        robotIds: "",
        workStationId: "",
        errorInfo: "",
        power: [0, 100],
        taskType: "",
      },
      errorList: [
        {
          value: "",
          label: "lang.rms.fed.whole",
        },
        {
          value: "WARNING",
          label: "lang.rms.fed.warning",
        },
        {
          value: "ERROR",
          label: "lang.rms.fed.error",
        },
      ],
      taskTypeList: [],
      stat: {},

      robotList: [],
      listTimmer: null,
      isSearching: false,

      robotTableHeaderList: [],
      treeDataHeaderList: [], // 默认表头树
      defaultCheckedNodes: [], // 默认选中的表头
      props: {
        label: data => {
          return this.$t(data.label);
        },
      },

      sliderValue: 0,
      sliderStep: 2,
      tableHeight: 0,
      tableScroll: 0,
      maxLen: 0,
      isShift: false,
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      if (roleInfo === "guest") return true;
      else return false;
    },
  },
  activated() {
    resizeDebounce = this._tableResize(); // 为了计算高度 一切为了性能 哎
    onkeydown = e => {
      if (e.keyCode === 16) this.isShift = true;
    };
    onkeyup = e => {
      if (e.keyCode === 16) this.isShift = false;
    };
    this.getTaskTypeList();
    this.getTableHeader();

    window.addEventListener("resize", resizeDebounce, false);
    window.addEventListener("keydown", onkeydown, false);
    window.addEventListener("keyup", onkeyup, false);
    $table = this.$refs.robotList.$el;
    $table.addEventListener("mousewheel", this.handleTableScroll, false);
  },
  deactivated() {
    window.removeEventListener("resize", resizeDebounce, false);
    window.removeEventListener("keydown", onkeydown, false);
    window.removeEventListener("keyup", onkeyup, false);
    $table.removeEventListener("mousewheel", this.handleTableScroll, false);
    if (this.listTimmer) clearTimeout(this.listTimmer);
    this.listTimmer = null;
    this.robotList = [];
    this.robotTableHeaderList = [];
    this.treeDataHeaderList = []; // 默认表头树
    this.defaultCheckedNode = []; // 默认选中的表头
    this.tableHeight = 0;

    allRobotList = [];
    resizeDebounce = null;
    $domObj = null;
    $table = null;
    size = -1;
  },
  methods: {
    sliderChange(value) {
      const tableScroll = this.maxLen - value;
      if (tableScroll < 0) {
        this.maxLen = 0;
        tableScroll = 0;
      }
      this.tableScroll = tableScroll;
      this.robotList = allRobotList.slice(tableScroll, tableScroll + size) || [];
    },
    handleTableScroll(event) {
      if (this.isShift) return;
      let wheel = event.deltaY;
      // 滚动方向
      const down = wheel > 0;
      const max = this.maxLen;
      let num;
      if (down) {
        const next = this.tableScroll + this.sliderStep;
        num = next >= max ? max : next;
      } else {
        const pre = this.tableScroll - this.sliderStep;
        num = pre < 0 ? 0 : pre;
      }
      this.tableScroll = num;
      this.sliderValue = max - num;
      this.robotList = allRobotList.slice(num, num + size) || [];
    },
    handleEdit(robotId) {
      this.$refs.paramsConfigDialog.open(robotId);
    },
    onQuery() {
      if (this.isSearching) return;
      if (this.listTimmer) clearTimeout(this.listTimmer);
      this.listTimmer = null;
      this.getRobotList();
    },
    onReset() {
      this.form = {
        robotIds: "",
        workStationId: "",
        errorInfo: "",
        power: [0, 100],
        taskType: "",
      };
    },
    getRobotList() {
      const _this = this;
      _this.isSearching = true;
      const data = {
        robotIds: _this.form.robotIds,
        workStationId: _this.form.workStationId,
        errorInfo: _this.form.errorInfo,
        power: _this.form.power[0] + "," + this.form.power[1],
        taskType: _this.form.taskType,
      };
      $req
        .post("/athena/robotStatus/queryAll", data)
        .then(res => {
          const resData = res.data || {};
          const robots = resData.robots || [];
          allRobotList = robots;
          _this.stat = resData.stat || {};

          if (size !== -1) _this._calTableScroll();

          _this.isSearching = false;
          if (_this.listTimmer) {
            clearTimeout(_this.listTimmer);
          } else {
            if (resizeDebounce) {
              resizeDebounce();
            }
          }
          _this.listTimmer = setTimeout(() => {
            _this.getRobotList();
          }, 3000);
        })
        .catch(e => (_this.isSearching = false));
    },
    robotTypeClassName({ row }) {
      if (row.errorType.length > 0) {
        return "info-row errorLine";
      }
      return "info-row";
    },
    nullChange(value) {
      const self = this;
      if (!value) return "-";
      if ($utils.Type.isArray(value)) {
        if (value.length > 0) {
          let txt = "";
          for (let i = 0; i < value.length; i++) {
            const item = value[i];
            txt = txt + (i + 1) + ". " + self.$t(item) + "\n";
          }
          return txt;
        } else {
          return "-";
        }
      } else if ($utils.Type.isObject(value)) {
        value = JSON.stringify(value);
      }
      return self.$t(value);
    },
    // 接收子组件传递过来的数据
    fuzzySearchBub(params) {
      if (params.type === "Qrobot") {
        this.form.robotIds = params.pointer;
      } else {
        this.form.workStationId = params.pointer;
      }
    },
    getTableHeader() {
      const _this = this;
      _this.treeDataHeaderList = [];
      $req.post("/athena/robotStatus/queryHeader").then(res => {
        const data = res.data;
        let robotMessageHeader = $utils.Data.getRMSFEDConfig("robotMessageHeader");

        let arr = [];
        data.forEach((item, index) => {
          const key = item.key;
          item["id"] = key;
          item["label"] = item.value;
          // sort判断
          switch (key) {
            case "id":
            case "powerPercent":
            case "ip":
              item.sortable = true;
              break;
            default:
              item.sortable = false;
              break;
          }
          // width 定义
          switch (item.key) {
            case "id":
            case "product":
              item.width = "90";
              break;
            case "powerPercent":
              item.width = "120";
              break;
            case "errorType":
            case "errorSolution":
              item.width = "300";
              break;
            default:
              item.width = "150";
              break;
          }
          arr.push(item);
        });
        _this.treeDataHeaderList = arr;

        if (robotMessageHeader) {
          _this.robotTableHeaderList = robotMessageHeader;
          _this.defaultCheckedNodes = robotMessageHeader;
        } else {
          _this.robotTableHeaderList = arr;
          _this.defaultCheckedNodes = arr;
        }
        _this.$nextTick(() => {
          _this.$refs.headerTree.setCheckedNodes(_this.defaultCheckedNodes);
          $utils.Data.setRMSFEDConfig(_this.robotTableHeaderList, "robotMessageHeader");
          _this.getRobotList();
        });
      });
    },
    getTaskTypeList() {
      $req.post("/athena/robotStatus/queryTaskList").then(res => {
        if (res.code !== 0) return;
        this.taskTypeList = res.data;
      });
    },
    // 机器人参数配置
    itemRobotRecord(row) {
      this.$refs.recordDialog.open(row);
    },

    // 配合表头选择
    allowDrop(draggingNode, dropNode, type) {
      if (draggingNode.data.level === dropNode.data.level) {
        // fatherId 是父节点id
        if (draggingNode.data.fatherId === dropNode.data.fatherId) {
          return type === "prev" || type === "next";
        } else {
          return false;
        }
      } else {
        // 不同级进行处理
        return false;
      }
    },

    handleCheckChange(data, checked, indeterminate) {
      this.robotTableHeaderList = null;
      this.robotTableHeaderList = this.$refs.headerTree.getCheckedNodes();
      $utils.Data.setRMSFEDConfig(this.robotTableHeaderList, "robotMessageHeader");
    },

    _calTableScroll() {
      let maxLen = allRobotList.length - size;
      if (maxLen < 0) maxLen = 0;
      if (maxLen !== this.maxLen) this.maxLen = maxLen;

      const tableScroll = this.tableScroll;
      this.sliderValue = maxLen - tableScroll;
      this.robotList = allRobotList.slice(tableScroll, tableScroll + size) || [];
    },

    _tableResize() {
      return $utils.Tools.debounce(() => {
        if (!$domObj) {
          const $content = document.querySelector("#J_robotInfoContent");
          const $form = $content.querySelector("#J_robotInfoFormContent");
          const $header = $content.querySelector("#J_robotInfoTableHeader");
          $domObj = { $content, $form, $header };
        }
        const $dom = $domObj;
        let topH = 12;
        const sumTop = $dom.$content.clientHeight - 24; // 24为上下的padding
        if ($dom.$form) topH += $dom.$form.offsetHeight;
        if ($dom.$header) topH += $dom.$header.offsetHeight;

        const tableHeight = sumTop - topH;
        this.tableHeight = tableHeight;
        const calSize = Math.floor(tableHeight / 52 - 1);
        if (calSize !== size) {
          size = calSize;
          this._calTableScroll();
        }
      }, 300);
    },
  },
};
</script>

<style lang="less" scoped>
.form-content {
  padding-bottom: 5px;
  border-bottom: 5px solid #eee;

  :deep(.el-form-item) {
    margin-bottom: 6px;
  }

  :deep(.el-form-item__label) {
    padding-bottom: 0;
    font-size: 13px;
    font-weight: 800;
  }

  .operation-button {
    vertical-align: bottom;
    padding-bottom: 6px;
  }
}

:deep(.tree-box) {
  max-height: 320px;
  overflow-y: auto;
}

.table-content-header {
  font-size: 16px;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0 8px;
  .g-box-shadow-no-top();

  span {
    font-weight: 800;

    &.fr {
      font-size: 14px;
      font-weight: 600;
    }
  }
}
.info-row {
  height: 50px;
  .cell {
    .g-text-overflow();
  }
}
:deep(.el-table) {
  td.el-table__cell {
    padding: 0;
    height: 50px;
    div {
      height: 50px;
      vertical-align: middle;
      line-height: 1.3;
    }
  }
  .errorLine {
    color: red;
  }
  .el-table__empty-block {
    width: 100% !important;
  }
}

.scroll-slider {
  position: absolute;
  right: 0;
  bottom: 24px;
  z-index: 999;
  :deep(.el-slider__runway) {
    background: #a1a3a9;
    .el-slider__bar {
      background-color: #e4e7ed;
    }
    .el-slider__button {
      border-radius: 5px;
      width: 12px;
      background: #898686;
      border-color: #ddd;
    }
  }
}

.header_dropdown {
  margin-right: 10px;
  top: -5px;
  display: block;
  position: relative;

  span {
    font-weight: normal !important;
  }
}

.tree-box {
  padding: 6px 10px;

  :deep(.el-tree-node__expand-icon.is-leaf) {
    display: none !important;
  }
}

.tip {
  font-size: 13px;
  line-height: 1.3;
  padding: 8px 8px;
  color: #606266;
  border-radius: 4px;
  .tip-icon {
    margin-right: 5px;
  }
}
</style>
