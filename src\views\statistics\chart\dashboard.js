import { resolveChartData } from "./myChart";
const dashboardHandler = {};

/**
 * 这里进行一些缓存操作, 以减少请求次数
 * @param {*} options
 * @param { string } options.url 请求地址
 * @param { string } options.params 请求参数
 * @param { string } options.data 请求数据
 * @param { number } options.time 请求时间
 * @param { boolean } options.wait 是否等待请求中
 * @param { function } options.waitHandelList 等待请求中的回调函数
 */
let dashRequestTemporaryStorage = [];
export const parseEchartOption = (options) => {
  // 如果options.series中数据大于30条, 增加dataZoom
  if (options.series && options.series.length) {
    const seriesLen = options.series[0].data.length;
    if (seriesLen > 30) {
      options.dataZoom = [
        {
          type: "slider",
          xAxisIndex: 0,
          filterMode: "none",
        },
        {
          type: "inside",
          xAxisIndex: 0,
          filterMode: "none",
        },
      ];
    }
  }

  // 如果没有tooltip, 增加tooltip
  if (!options.tooltip) {
    options.tooltip = {
      trigger: 'axis',
      confine: true,
      extraCssText: 'overflow-y:auto;max-height:500px;pointer-events:auto !important;z-index: 2',
    }
  }

  return options;
}

/**
 * 请求是否读缓存, 为了减少请求次数
 * @param {*} url 
 * @param {*} params 
 * @returns 
 */
export const requestdata = async (url, params) => {
  // 清除10秒外的请求缓存
  const curTime = + new Date;
  dashRequestTemporaryStorage = dashRequestTemporaryStorage.filter(item => curTime - item.time < 10000);

  const paramsTxt = JSON.stringify(params);
  const filterItem = dashRequestTemporaryStorage.find(item => {
    return url === item.url && paramsTxt === item.params;
  });

  if (filterItem) {
    return await new Promise((resolve) => {
      if (filterItem.wait) {
        filterItem.waitHandelList.push(resolve);
      } else {
        resolve(filterItem.data);
      }
    });
  }

  const tsData = { url, params: paramsTxt, data: null, time: (+ new Date), wait: true, waitHandelList: [] }
  dashRequestTemporaryStorage.push(tsData);

  // 先搜索
  const result = await $req.post(url, params, { intercept: false });
  // 缓存
  tsData.wait = false;
  tsData.data = result;
  tsData.waitHandelList.forEach(item => item(result));
  return result;
}

dashboardHandler.getCountMap = async (data, options) => {
  const dataItem = data.countMap || {};
  switch (options.type) {
    case 'cred':
      return {
        ...options,
        id: options.paramsName,
        title: options.title || '',
        number: dataItem[options.paramsName] || 0,
      }

    default:
      return {}
  }
}

dashboardHandler.getStationReceiveByTotal = async (data, options) => {
  const totalDataItem = data[options.paramsName] || {};
  const xAxisData = Object.keys(totalDataItem);
  const seriesData = xAxisData.map(x => totalDataItem[x]);
  switch (options.type) {
    case 'bar':
      return parseEchartOption({
        title: { text: options.title || '' },
        xAxis: { type: 'category', data: xAxisData },
        yAxis: { type: 'value' },
        series: [{ data: seriesData, type: 'bar' }]
      })
    case 'cred':
      let num = 0;
      Object.keys(totalDataItem).forEach(itemKey => {
        num += totalDataItem[itemKey]
      })
      return {
        ...options,
        id: options.paramsName,
        title: options.title || '',
        number: num || 0,
      }
    default:
      return {}
  }
}
dashboardHandler.getStationReceiveBySplit = async (data, options) => {
  const totalDataItem = data[options.paramsName] || {};
  const xAxisData = Object.keys(totalDataItem);
  const serverNamesSet = new Set();

  xAxisData.forEach(item => {
    const dataItem = totalDataItem[item];
    Object.keys(dataItem).forEach(item => serverNamesSet.add(item));
  })

  serverNamesSet.delete('all_info');
  const serverNameList = [...serverNamesSet];
  serverNameList.sort((a, b) => a - b);

  const seriesData = serverNameList.map(serverName => {
    return {
      name: serverName,
      type: 'bar',
      stack: 'stack',
      data: xAxisData.map(x => totalDataItem[x][serverName] || 0)
    }
  });

  switch (options.type) {
    case 'stackBar':
      return parseEchartOption({
        title: { text: options.title || '' },
        xAxis: { type: 'category', data: xAxisData },
        yAxis: { type: 'value' },
        series: seriesData
      })
    default:
      return {}
  }
}

dashboardHandler.getJobCollect = async (data, options) => {
  const totalDataItem = data[options.paramsName] || {};
  const xAxisData = Object.keys(totalDataItem);
  xAxisData.sort((a, b) => a - b);
  const seriesData = xAxisData.map(x => totalDataItem[x]);

  switch (options.type) {
    case 'bar':
      return parseEchartOption({
        title: { text: options.title || '' },
        xAxis: { type: 'category', data: xAxisData },
        yAxis: { type: 'value' },
        series: [{ data: seriesData, type: 'bar' }]
      })
    default:
      return {}
  }
}

dashboardHandler.getJobSnapshot = async (data, options) => {
  const totalDataItem = data?.jobSnapshotList || {};
  // 解析的算法
  const chartDataMap = resolveChartData(totalDataItem);
  const chartDataItem = chartDataMap.seriesObj[options.paramsName] || {};
  switch (options.type) {
    case 'line':
      return parseEchartOption({
        title: { text: options.title || '' },
        xAxis: { type: 'category', data: chartDataMap.xAxisData },
        yAxis: { type: 'value' },
        series: [{ data: chartDataItem.data || [], type: 'line' }]
      })
    default:
      return {}
  }
}

dashboardHandler.getRobotSnapshot = async (data, options) => {
  const totalDataItem = data?.robotSnapshotList || {};

  // 解析的算法
  const chartDataMap = resolveChartData(totalDataItem);
  const chartDataItem = chartDataMap.seriesObj[options.paramsName] || {};
  switch (options.type) {
    case 'line':
      return parseEchartOption({
        title: { text: options.title || '' },
        xAxis: { type: 'category', data: chartDataMap.xAxisData },
        yAxis: { type: 'value' },
        series: [{ data: chartDataItem.data || [], type: 'line' }]
      })
    default:
      return {}
  }
}

dashboardHandler.getLatticeSnapshot = async (data, options) => {
  const totalDataItem = data?.latticeSnapshotList || {};

  // 解析的算法
  const chartDataMap = resolveChartData(totalDataItem);
  const chartDataItem = chartDataMap.seriesObj[options.paramsName] || {};
  switch (options.type) {
    case 'line':
      return parseEchartOption({
        title: { text: options.title || '' },
        xAxis: { type: 'category', data: chartDataMap.xAxisData },
        yAxis: { type: 'value' },
        series: [{ data: chartDataItem.data || [], type: 'line' }]
      })
    default:
      return {}
  }
}

dashboardHandler.getRobotFaultSumByDay = async (data, options) => {
  const servers = [];
  Object.keys(data.robotType2Data || {}).forEach(robotType => {
    const dataItem = data.robotType2Data[robotType];
    servers.push({
      name: robotType,
      type: 'line',
      data: dataItem
    })
  })

  switch (options.type) {
    case 'line':
      return parseEchartOption({
        title: { text: options.title || '' },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category', data: (data.xAxis || []).map(item => {
            return $utils.Tools.formatDate(item, "yyyy-MM-dd hh:mm:ss")
          })
        },
        yAxis: { type: 'value' },
        series: servers
      })
    default:
      return {}
  }
}

dashboardHandler.getMonitorStatFaultDetailByDay = async (data, options) => {
  const dataItem = data[options.paramsName] || [];
  let tableCloumns = [];

  switch (options.paramsName) {
    default:
      tableCloumns = [
        { label: '单元格编号', prop: 'code' },
        { label: '单元格类型', prop: 'codeType' },
        { label: '总数', prop: 'sumData', width: '70px' },
        { label: '异常数', prop: 'faultData', width: '80px' },
        { label: '异常比例', prop: 'faultProportion', width: '80px' },
        { label: '时间戳', prop: 'statDateStamp', type: 'time' },
      ];
      break;
  }


  return {
    tableCloumns,
    tableData: dataItem,
  }
}

dashboardHandler.taskCompletionRate = async (data, options) => {
  return {
    ...options,
    id: options.paramsName,
    title: options.title || '',
    number: data.countMap.doneJobPercent * 100,
  }
}

dashboardHandler.getAllRobotNumberSnapshot = async (data, options) => {
  const opt = {
    robotCount: data.countMap.robotCount || 0,
    robotWorkingCount: data.countMap.robotWorkCount || 0
  };

  const totalDataItem = data?.robotSnapshotList || {};
  totalDataItem.filter(item => {
    return item.haveData;
  }).forEach(item => {
    opt.robotCount += item.robotCount;
    opt.robotWorkingCount += item.robotWorkingCount;
  });

  return {
    ...options,
    id: options.paramsName,
    title: options.title || '',
    number: opt[options.paramsName] || 0,
  }
}

dashboardHandler.getAllTaskRobotPrSnapshot = async (data, options) => {
  const { robotCount, robotWorkCount } = data.countMap || {};
  
  const pr =  (robotWorkCount / robotCount).toFixed(3);

  return {
    title: { text: options.title || '' },
    grid: { top: 0, bottom: 0, left: 0, right: 0 },
    series: [{
      type: 'liquidFill',
      data: [pr, pr],
      radius: '75%'
    }]
  }
}

dashboardHandler.utilizationTrends = async (data, options) => {
  const xAxisData = [];
  const seriesRobotCount = [];
  const seriesRobotWorkingCount = [];
  const totalDataItem = data?.robotSnapshotList || {};
  totalDataItem.filter(item => {
    return item.haveData;
  }).forEach(item => {
    xAxisData.push($utils.Tools.formatDate(item.snapshotTime, "hh:mm:ss"));
    seriesRobotCount.push(item.robotCount);
    seriesRobotWorkingCount.push(item.robotWorkingCount);
  });

  return {
    title: { text: options.title || '' },
    xAxis: { type: 'category', data: xAxisData },
    yAxis: { type: 'value' },
    legend: {
      data: ['工作中机器人总数', '机器人总数']
    },
    series: [
      { data: seriesRobotWorkingCount, type: 'line', stack: 'Total', name: '工作中机器人总数', },
      { data: seriesRobotCount, type: 'line', stack: 'Total', name: '机器人总数', },
    ]
  }
}

export default dashboardHandler;

