<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2022-01-05 20:04:30
 * @Description:
-->
<template>
  <el-form
    ref="searchForm"
    :inline="true"
    class="demo-form-inline"
    label-position="top"
    :model="searchForm"
  >
    <el-form-item :label="$t('lang.rms.api.result.warehouse.mechanism.spuName')" prop="name">
      <el-input
        v-model.trim="searchForm.name"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterInstitutionSPU')"
      />
    </el-form-item>
    <!-- <el-form-item :label="$t('lang.rms.api.result.warehouse.orgCode')" prop="mechanismCode">
      <el-input
        v-model.trim="searchForm.mechanismCode"
        :placeholder="$t('lang.rms.api.result.warehouse.pleseEnterOrganizationCode')"
      />
    </el-form-item> -->
    <el-form-item class="align-bottom">
      <el-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.query") }}</el-button>
      <el-button type="primary" @click="resetForm">{{ $t("lang.rms.fed.reset") }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import { mapState } from "vuex";

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    // 这里存放数据
    return {
      searchForm: {
        name: "",
        // mechanismCode: "",
      },
    };
  },
  // 监听属性 类似于data概念
  computed: {
    ...mapState(["dictionary"]),
  },
  // 监控data中的数据变化
  watch: {},
  mounted() {
    this.onSubmit();
  },
  // 方法集合
  methods: {
    onSubmit() {
      this.$emit("onsubmit", { ...this.searchForm });
    },
    resetForm() {
      this.$refs["searchForm"].resetFields();
      this.onSubmit();
    },
  },
};
</script>
<style lang="scss" scoped>
.align-bottom {
  vertical-align: bottom;
}
</style>
