@charset "utf-8";

.geek-form-table-con {
  .form-content {
    border-bottom: 5px solid #eee;
    padding-bottom: 10px;
    .btn-warp {
      line-height: 98px;
    }
  }

  .table-content {
    padding-top: 15px;

    .btn-opt {
      padding: 3px 5px;
      min-height: 10px;
    }
  }
}

.geek-pagination {
  padding: 20px 0;
}

.geek-tabs-menu {
  display: inline-block;

  > .el-menu-item {
    margin: 0 10px;
    padding: 0 8px;
    height: 45px;
    line-height: 45px;
    color: #303133;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    &.is-active,
    &.is-active:focus {
      color: #409eff;
    }
  }
}
