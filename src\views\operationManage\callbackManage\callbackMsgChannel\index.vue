<template>
  <div class="app-container">
    <callbackMsgChannelTagDialog
      v-if="channelTagDialog"
      :channel-tag-dialog.sync="channelTagDialog"
      :item-channel-data="itemData"
      @save="save"
      @update:channelTagDialog="getTableList()"
    />

    <el-card>
      <el-form label-position="top" label-width="80px" :model="searchData">
        <el-row :gutter="20">
          <!-- 通道ID -->
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.channelId')">
              <el-input v-model="searchData.channelId" class="w_100x" />
            </el-form-item>
          </el-col>
          <!--通道类型-->
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.channelType')">
              <el-input v-model="searchData.channelType" class="w_100x" />
            </el-form-item>
          </el-col>
          <el-col :span="8" class="btnwarp">
            <el-button type="primary" @click="onSearch()">{{ $t("lang.rms.fed.query") }}</el-button>
            <el-button @click="resetSearchData">{{ $t("lang.rms.fed.reset") }}</el-button>
            <el-button @click="itemClick({}, false)">{{ $t("lang.rms.fed.add") }}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-dialog
      :title="$t(dialogTitle)"
      :visible.sync="channelEditDialog"
      :before-close="closeChannelEditDialog"
      center
      :append-to-body="true"
      :close-on-click-modal="false"
      width="60%"
    >
      <el-form
        ref="ruleForm"
        label-position="top"
        label-width="80px"
        :model="itemData"
        :rules="rules"
        class="padding_20"
      >
        <el-col :span="7">
          <el-form-item
            :label="$t('lang.rms.fed.channelId')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
            prop="channelId"
          >
            <el-input v-if="isEdit" v-model="itemData.channelId" type="text" readonly="readonly" />
            <el-input v-else v-model="itemData.channelId" type="text" />
          </el-form-item>
        </el-col>
        <el-col :span="7" class="ml-10">
          <el-form-item
            :label="$t('lang.rms.fed.channelType')"
            :rules="[{ required: true, message: $t('lang.rms.fed.choose') }]"
            prop="channelType"
          >
            <el-select
              v-model="itemData.channelType"
              :placeholder="$t('lang.rms.fed.pleaseChoose')"
            >
              <el-option
                v-for="(item, key) in channelTypeList"
                :key="key"
                :label="item"
                :value="key"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7" class="ml-10">
          <el-form-item
            :label="$t('lang.rms.fed.enable')"
            :rules="[{ required: true, message: $t('lang.rms.fed.choose') }]"
            prop="enable"
          >
            <el-select v-model="itemData.enable" :placeholder="$t('lang.rms.fed.pleaseChoose')">
              <el-option
                v-for="(item, key) in enableList"
                :key="key"
                :label="$t(item)"
                :value="key"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item
            :label="$t('lang.rms.fed.callbackMaxRetryTimes')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
            prop="maxRetryTimes"
          >
            <el-input v-model="itemData.maxRetryTimes" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="7" class="ml-10">
          <el-form-item
            :label="$t('lang.rms.fed.callbackMaxRetryTimeout')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
            prop="maxRetryTimeout"
          >
            <el-input v-model="itemData.maxRetryTimeout" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="7" class="ml-10">
          <el-form-item
            :label="$t('lang.rms.fed.channelUrl')"
            prop="channelUrl"
            :rules="channelUrlRule"
          >
            <el-input v-model="itemData.channelUrl" type="text" />
          </el-form-item>
        </el-col>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeChannelEditDialog">{{ $t("lang.common.cancel") }}</el-button>
        <el-button type="primary" :loading="loadingSave" @click="save">{{
          $t("lang.rms.fed.save")
        }}</el-button>
      </span>
    </el-dialog>
    <el-card class="mt-20">
      <!-- 列表信息 -->
      <el-table :data="tableData" style="width: 100%">
        <el-table-column
          :label="$t('lang.rms.fed.lineNumber')"
          width="50"
          align="center"
          :formatter="formatIndex"
        />
        <!--通道ID-->
        <el-table-column prop="channelId" :label="$t('lang.rms.fed.channelId')" align="center" />
        <!--通道类型-->
        <el-table-column
          prop="channelType"
          :label="$t('lang.rms.fed.channelType')"
          align="center"
        />

        <el-table-column prop="enable" :label="$t('lang.rms.fed.enable')" align="center">
          <template slot-scope="scope">{{ $t(enableList[scope.row.enable]) }}</template>
        </el-table-column>
        <!-- 最大尝试次数 -->
        <el-table-column
          prop="maxRetryTimes"
          :label="$t('lang.rms.fed.callbackMaxRetryTimes')"
          align="center"
        />
        <!-- 最大超时时间 -->
        <el-table-column
          prop="maxRetryTimeout"
          :label="$t('lang.rms.fed.callbackMaxRetryTimeout')"
          align="center"
        />

        <el-table-column v-if="!isRoleGuest" :label="$t('lang.rms.fed.operation')" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="itemClick(scope.row, true)">
              {{ $t("lang.rms.fed.edit") }}
            </el-button>
            <el-button type="text" size="small" @click="configTag(scope.row)">
              {{ $t("lang.rms.fed.callbackConfigChannelTag") }}
            </el-button>
            <el-button type="text" size="small" @click="deleteData(scope.row)">
              {{ $t("lang.rms.fed.delete") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <geek-pagination
        :current-page="pageOption.pagecurrent"
        :page-size="pageOption.pagesize"
        :total-page="taskCount"
        @currentPageChange="currentPageChange"
        @pageSizeChange="pageSizeChange"
      />
    </el-card>
  </div>
</template>
<script>
import callbackMsgChannelTagDialog from "./callbackMsgChannelTagDialog";

export default {
  name: "CallbackChannel",
  components: {
    callbackMsgChannelTagDialog,
  },
  data() {
    return {
      // 搜索内容
      searchData: {
        channelId: "",
        channelType: "",
      },
      tableData: [], // 设置table的数据
      taskCount: 0, // 当前数据总数
      pageOption: {
        pagecurrent: 1, // 当前页数
        pagesize: 10, // 每页展示数
      },
      itemData: {},
      enableList: {
        1: "lang.rms.fed.enable",
        0: "lang.rms.fed.disable",
      },
      channelTypeList: {
        HTTP: "HTTP",
        WEBSOCKET: "WEBSOCKET",
        SOCKET: "SOCKET",
        RPC: "RPC",
        DMP: "DMP",
      },
      isEdit: false,
      channelEditDialog: false,
      channelTagDialog: false,
      rules: {
        channelId: [{ required: true, message: this.$t("lang.rms.fed.pleaseEnter") }],
      },
      loadingSave: false,
    };
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? "lang.rms.fed.edit" : "lang.rms.fed.add";
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
    channelUrlRule() {
      const HTTP = "HTTP";
      return this.itemData.channelType === HTTP
        ? [{ required: true, message: this.$t("lang.rms.fed.pleaseEnter") }]
        : [];
    },
  },
  created() {
    this.getTableList();
  },
  methods: {
    /* 格式化内容 */
    formatterTags(row, column, cellValue) {
      if (cellValue) {
        return cellValue.join(",");
      } else {
        return cellValue;
      }
    },
    formatImmediate(row, column, cellValue) {
      const { immediateList } = this;
      for (let index = immediateList.length - 1; index >= 0; index -= 1) {
        if (immediateList[index].key === cellValue + "") {
          return immediateList[index].value;
        }
      }
      return cellValue;
    },
    formatIndex(row, column, cellValue, index) {
      return ((this.pageOption.pagecurrent - 1) * this.pageOption.pagesize || 0) + index + 1;
    },
    currentPageChange(currentPage) {
      this.pageOption.pagecurrent = currentPage;
      this.getTableList();
    },
    pageSizeChange(pageSize) {
      this.pageOption.pagecurrent = 1;
      this.pageOption.pagesize = pageSize;
      this.getTableList();
    },
    // 编辑
    itemClick(data, isEdit) {
      this.isEdit = isEdit;
      this.channelEditDialog = true;
      this.channelTagDialog = false;
      // 编辑前先重置属性框
      this.$nextTick(() => {
        this.$refs["ruleForm"].resetFields();
        this.itemData = JSON.parse(JSON.stringify(data));
        if (isEdit) {
          this.itemData.enable = data.enable + "";
        }
      });
    },
    // 配置标签
    configTag(data) {
      this.itemData = JSON.parse(JSON.stringify(data));
      this.channelTagDialog = true;
      this.channelEditDialog = false;
    },
    deleteData(data) {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete")).then(() => {
        const msgChannelIds = [];
        msgChannelIds.push(data.id);
        const para = { msgChannelIds };
        $req.post("/athena/apiCallback/deleteMsgChannel", para).then(json => {
          if (json.code === 0) {
            this.$message.success(this.$t(json.msg));
            this.getTableList();
          }
        });
      });
    },
    // 重置搜索参数
    resetSearchData() {
      for (const key in this.searchData) {
        this.searchData[key] = "";
        // if (this.searchData.hasOwnProperty(key)) {
        //   this.searchData[key] = ''
        // }
      }
      this.onSearch();
    },
    // 查询
    onSearch() {
      this.pageOption.pagecurrent = 1;
      this.getTableList();
    },
    getTableList() {
      const { searchData } = this;
      const data = { language: $utils.Data.getLocalLang() };
      const { channelId, channelType } = searchData;
      const pageData =
        "?currentPage=" + this.pageOption.pagecurrent + "&pageSize=" + this.pageOption.pagesize;
      if (channelId) {
        data.channelId = channelId;
      }
      if (channelType) {
        data.channelType = channelType;
      }
      $req.post("/athena/apiCallback/msgChannelPageList" + pageData, data).then((data = {}) => {
        const { currentPage = 0, pageSize = 0, pageCount = 0, recordList = [] } = data.data || {};
        this.tableData = recordList.map(item => {
          const descr = this.$t(item.descr);
          return { ...item, descr };
        });
        this.pageOption.pagesize = pageSize;
        this.pageOption.pagecurrent = currentPage;
        this.taskCount = pageCount;
      });
    },
    // 保存
    save() {
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          this.loadingSave = true;
          const data = this.itemData;
          data.msgChannelId = this.itemData.id;
          // 保存通道配置
          $req
            .post("/athena/apiCallback/saveMsgChannel", data)
            .then(json => {
              if (json.code === 0) {
                this.$message.success(this.$t(json.msg));
                this.channelEditDialog = false;
                this.getTableList();
                this.loadingSave = false;
              } else {
                this.loadingSave = false;
              }
            })
            .catch((this.loadingSave = false));
        }
      });
    },
    closeChannelEditDialog() {
      this.channelEditDialog = false;
      this.itemData = {};
    },
  },
};
</script>
<style scoped>
.mt-20 {
  margin-top: 20px;
}

.ml-10 {
  margin-left: 10px;
}

.w_100x {
  width: 100%;
}

.btnwarp {
  padding: 43px 0 0;
}
</style>
