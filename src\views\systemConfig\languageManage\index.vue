<template>
  <geek-main-structure class="language-list-manage">
    <geek-tabs-nav
      class="language-nav"
      :nav-list="newNavList"
      :active-nav-id="activeName"
      @select="tabsNavChange"
    />

    <keep-alive>
      <component
        :is="currentDialog"
        :language-option="languageOption"
        :table-data="tableData"
        @getTableList="getTableList"
      />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
/**
 * lang.rms.fed.languageAdded:"已添加语言"
 * lang.rms.fed.addNewLanguage:"添加新语言"
 */
import LanguageList from "./components/languageList";
import LanguageAdd from "./components/languageAdd";

export default {
  name: "LanguageManage",
  components: { LanguageList, LanguageAdd },
  data() {
    return {
      defaultActive: "tabs-nav-1",
      tabNamePerssion: {
        "tabs-nav-1": this.getTabPermission(
          "TabI18nControllerManageHasPage",
          "i18nControllerManage",
        ),
        "tabs-nav-2": this.getTabPermission(
          "TabI18nControllerManageNewPage",
          "i18nControllerManage",
        ),
      },
      navList: [
        {
          id: "tabs-nav-1",
          text: "lang.rms.fed.languageAdded",
        },
        {
          id: "tabs-nav-2",
          text: "lang.rms.fed.addNewLanguage",
        },
      ],
      currentDialog: "LanguageList",
      languageOption: [],
      tableData: [],
    };
  },
  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
    newNavList() {
      const result = [];
      console.log(this.navList);
      for (let i = 0; i < this.navList.length; i++) {
        console.log(i, this.navList);
        const element = this.navList[i];

        element["hide"] = !this.tabNamePerssion[element.id];
        result.push(element);
      }

      return result;
    },
  },
  mounted() {
    // this.getTableList();

    this.defaultActive = $utils.Tools.getRouteQueryTabName(
      this.defaultActive,
      this.tabNamePerssion,
    );
  },

  methods: {
    tabsNavChange(id) {
      if (this.activeNavId === id) return;
      this.activeNavId = id;
      switch (id) {
        case "tabs-nav-1":
          this.currentDialog = "LanguageList";
          break;
        case "tabs-nav-2":
          this.currentDialog = "LanguageAdd";
          break;
      }
    },
    getTableList() {
      $req.get("/athena/api/coreresource/i18n/findAllLanguages").then(res => {
        let list = res.data || [];
        // if (list != null) {
        // let list = result.allLanguages;
        list = list.map(item => {
          item.languageCode = item.code;
          item.languageName = item.name;
          return item;
        });
        this.tableData = list.filter(i => i.isDeleted === 0);
        this.languageOption = list;
        // }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.language-nav {
  margin-bottom: 12px;
}
</style>
