<template>
  <div>
    <SearchWrap>
      <SearchForm :size-type-arr="sizeTypeArr" @onsubmit="onSubmit" />
    </SearchWrap>
    <div style="text-align: right">
      <CreateDialog
        :prop-show="showCreatDialog"
        :edit-prop="editCreatDialog"
        :button-text="$t('lang.rms.api.result.warehouse.robotBusinessModel')"
        :title-text="$t(creatDialogTitle)"
        @showTrueFalse="handleOpenDialog"
        @createconfirm="createConfirm"
        @createcancel="createCancel"
      >
        <CreatForm
          ref="createForm"
          :slop-props="slopProps"
          :edit-prop="editCreatDialog"
          :size-type-arr="sizeTypeArr"
          :bizTypesArr="bizTypesArr"
        />
      </CreateDialog>
    </div>
    <el-table :loading="loading" :data="recordList" style="width: 100%">
      <!-- <el-table-column
        :label="$t('lang.rms.api.result.warehouse.businessFeatureNo')"
        prop="businessCode"
        min-width="160"
      /> -->
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.businessFeatureName')"
        prop="name"
        min-width="160"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.supportedTaskTypes')"
        min-width="400"
      >
        <template slot-scope="scope">
          <el-tag
            v-for="item in scope.row.taskTypes"
            :key="item"
            type="info"
            style="margin-right: 10px; margin-bottom: 2px"
            >{{ item }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="sizeType" min-width="300">
        <template slot-scope="scope">
          <el-tag
            v-for="item in scope.row.sizeTypes"
            :key="item"
            type="info"
            style="margin-right: 10px"
            >{{ item }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.containerType')"
        prop="containerTypes"
        min-width="200"
      >
        <template slot-scope="scope">
          <el-tag
            v-for="item in scope.row.containerTypes"
            :key="item"
            type="info"
            style="margin-right: 10px"
            >{{ item }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('lang.rms.fed.robotParamConfig.chargingStationType')"
        prop="chargerTypes"
        min-width="200"
      >
        <template slot-scope="scope">
          <el-tag
            v-for="item in scope.row.chargerTypes"
            :key="item"
            type="info"
            style="margin-right: 10px"
            >{{ item }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        :label="$t('lang.rms.fed.textOperation')"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="checkListItemInfo(scope.row)">
            {{ $t("lang.rms.fed.buttonView") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="copyListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.web.map.version.copy") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="editListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.fed.buttonEdit") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="deleteListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.fed.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="text-align: right; margin-top: 30px">
      <el-pagination
        background
        layout="total, prev, pager, next, sizes, jumper"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="paginationParams.total"
        :page-size="paginationParams.pageSize"
        :current-page="paginationParams.currentPage"
        @current-change="paginationChange"
        @size-change="paginationPageChange"
      />
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import SearchWrap from "../../Components/SearchWrap";
import CreateDialog from "../../Components/CreateDialog";
import CreatForm from "./Components/CreatForm";
import SearchForm from "./Components/SearchForm";
import robotManageRequest from "@/api/robotManage";

export default {
  name: "BusinessFeatureModel",
  components: {
    CreatForm,
    SearchWrap,
    CreateDialog,
    SearchForm,
  },
  data() {
    return {
      slopProps: {},
      showCreatDialog: false,
      editCreatDialog: true,
      creatDialogTitle: "lang.rms.api.result.warehouse.createRobotBusinessModel",
      copyDataOpen: false,
      loading: false,
      recordList: [],
      paginationParams: { pageSize: 20, currentPage: 1, total: 1 },
      searchFormData: {},
      sizeTypeArr: [],
      bizTypesArr: []
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  watch: {},
  mounted() {
    // this.getSizeTypeArr();
  },
  methods: {
    // 获取sizeType数组
    getSizeTypeArr() {
      robotManageRequest.getSizeTypeArr().then(data => {
        this.sizeTypeArr = data.data;
      });
    },
    getBizTypes() {
      $req.post('/athena/dict/queryList', {dictCode: "ROBOT_BIZ"}).then(res => {
        this.bizTypesArr = res.data
      })
    },
    handleOpenDialog() {
      this.getSizeTypeArr();
      this.getBizTypes()
    },
    onSubmit(searchFormData) {
      console.log(searchFormData);
      const { paginationParams } = this;
      this.searchFormData = searchFormData;

      this.loading = true;
      this.reqTableList(searchFormData, paginationParams);
    },
    reqTableList(searchFormData, paginationParams) {
      robotManageRequest.getBusinessPageList(searchFormData, paginationParams).then(({ data }) => {
        const { pageSize, currentPage, recordList, recordCount } = data;

        this.recordList = recordList;

        this.paginationParams = {
          pageSize,
          currentPage,
          total: recordCount,
        };
        this.loading = false;
      });
    },
    reReqTableList() {
      this.paginationParams.currentPage = 1;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationChange(currentPage) {
      this.paginationParams.currentPage = currentPage;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationPageChange(pageSize) {
      this.paginationParams.pageSize = pageSize;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    createConfirm() {
      if (this.editCreatDialog) {
        this.showCreatDialog = true;
        const $createForm = this.$refs["createForm"];
        $createForm
          .getFormValues()
          .then(val => {
            if (val) {
              const { modelData } = $createForm;
              const paramsObj = { ...modelData };
              if (!this.slopProps.id || this.copyDataOpen) {
                delete paramsObj.id;
              }
              if (paramsObj.bizTypes) {
                paramsObj.bizTypes = [paramsObj.bizTypes]
              }
              robotManageRequest.addEditBusinessItem(paramsObj, {}).then(({ code }) => {
                if (+code === 0) {
                  console.log("添加/修改成功！");
                }
                this.createCancel();
                this.reReqTableList();
                this.confirmNext();
              });
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    async confirmNext() {
      try {
        await this.$confirm(
          this.$t("lang.rms.fed.gotoSomePage", [
            this.$t("lang.rms.api.result.warehouse.robotModel"),
          ]),
        );
        this.$emit("goNextPage");
      } catch (e) {}
    },
    createCancel() {
      this.$refs["createForm"].resetFormValues();
      this.slopProps = {};
      this.editCreatDialog = true;
      this.showCreatDialog = false;
      this.copyDataOpen = false;
      this.creatDialogTitle = "lang.rms.api.result.warehouse.createRobotBusinessModel";
    },
    openDialogInSomeType(edit, data) {
      this.getSizeTypeArr();
      this.getBizTypes()
      const { id, chargerTypes, taskTypes, sizeTypes, containerTypes, name, bizTypes } = data;
      const newbizTypes = bizTypes && bizTypes.length ? bizTypes[0] : ''
      this.slopProps = {
        id,
        chargerTypes,
        taskTypes,
        sizeTypes,
        containerTypes,
        name,
        bizTypes: newbizTypes
      };
      this.editCreatDialog = edit;
      this.showCreatDialog = true;
      this.creatDialogTitle = edit
        ? "编辑业务特征模型"
        : "lang.rms.api.result.warehouse.viewRobotModelInstance";
    },
    // 查看
    checkListItemInfo(robotData) {
      robotManageRequest.getBusinessDetailInfo({}, { id: robotData.id }).then(({ data }) => {
        this.openDialogInSomeType(false, data);
      });
    },
    // 复制
    copyListItemInfo(robotData) {
      robotManageRequest.getBusinessDetailInfo({}, { id: robotData.id }).then(({ data }) => {
        this.copyDataOpen = true;
        this.openDialogInSomeType(true, data);
      });
    },
    // 编辑
    editListItemInfo(robotData) {
      robotManageRequest.getBusinessDetailInfo({}, { id: robotData.id }).then(({ data }) => {
        this.openDialogInSomeType(true, data);
      });
    },
    // 删除
    deleteListItemInfo(robotData) {
      this.$confirm(
        this.$t("lang.rms.api.result.warehouse.willDeleteToContinue"),
        this.$t("lang.rms.fed.prompt"),
        {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.rms.fed.cancel"),
          type: "warning",
        },
      )
        .then(() => {
          robotManageRequest.deleteBusinessItem({}, { id: robotData.id }).then(({ code }) => {
            if (+code === 0) {
              this.$message({
                type: "success",
                message: this.$t("lang.venus.web.common.successfullyDeleted"),
              });
            }
            this.reReqTableList();
          });
        })
        .catch(() => null);
    },
  },
};
</script>
<style lang="scss" scoped>
.align-bottom {
  vertical-align: bottom;
}
</style>
