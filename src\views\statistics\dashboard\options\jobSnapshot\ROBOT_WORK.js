export default {
  // 地图数据信息
  chart: {
    type: 'cred', // 图表类型

    // 地图数据来源
    request: {
      url: '/athena/stats/query/job/dashboard/count/',  // 请求接口
      filters: ['date', 'cycle'], // 筛选条件
      defFilters: {
        date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
        showCount: true,
        showCountGroup: true,
      },
      timer: 5000,  // 轮询时间, 如果是0, 则不轮询
    },

    // 地图数据处理
    dataHandler: {  // 数据处理
      handler: 'getCountMap',
      params: {
        type: 'cred',
        color: "#58c8c4",
        paramsName: 'robotWorkCount',
        title: '工作中机器人数量'
      },
    },
  }
}