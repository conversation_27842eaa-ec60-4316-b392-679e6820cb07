<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2021-12-27 13:59:47
 * @Description:
-->
<template>
  <div>
    <el-button type="primary" @click="dialogShow">
      {{ $t("lang.rms.fed.add") }}{{ buttonText }}
    </el-button>
    <div class="creat-dialog">
      <el-dialog
        :title="$t(titleText)"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        :modal-append-to-body="false"
        @close="dialogClose"
      >
        <slot />
        <template #footer>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogClose">{{ $t("lang.rms.fed.cancel") }}</el-button>
            <el-button v-show="editProp" type="primary" @click="dialogConfirm">
              {{ $t("lang.rms.fed.confirm") }}
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    titleText: {
      type: String,
      default: "提示",
    },
    buttonText: {
      type: String,
      default: "",
    },
    propShow: {
      type: Boolean,
      default: true,
    },
    editProp: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    // 这里存放数据
    return {
      dialogVisible: false,
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {
    propShow(newVal) {
      this.dialogVisible = newVal;
    },
  },
  // 方法集合
  methods: {
    dialogShow() {
      this.$emit("showTrueFalse", true);
      this.dialogVisible = true;
    },
    dialogClose() {
      this.$emit("createcancel");
      this.$emit("showTrueFalse", false); // 返回当前状态
      this.dialogVisible = false;
    },
    dialogConfirm() {
      this.$emit("createconfirm");
    },
  },
};
</script>
<style lang="scss" scoped>
.creat-dialog {
  text-align: left;
}
</style>
