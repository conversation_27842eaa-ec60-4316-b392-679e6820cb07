<template>
  <section class="statistics-con">
    <chart-list v-show="!currentChart" :chartList="chartList" @chooseChart="chooseChart" />

    <div v-show="currentChart" class="chart-con">
      <chart-tools :chartTypes="types" :defaultTypes="defaultTypes" @update="updateChart" @cancle="currentChart = null" />
      <div id="J_chartBox" class="chart-box" />
    </div>
  </section>
</template>

<script>
import ChartList from "./common/chart-list.vue";
import ChartTools from "./common/chart-tools.vue";
import ChartGroup, { parseChartOptions } from "../chart/myChart";
import reqMock from "../mock";
const chartGroup = new ChartGroup();
/**
 * ⚠️ 注意, 这里的isMock如果为true, 则会使用mock数据, 不会走接口
 */
const ISMOKE = false;

export default {
  name: "statisticsChart",
  components: { ChartList, ChartTools },
  data() {
    return {
      defaultChartParams: { date: new Date(), cycle: "5" },

      currentChart: null,
      chartList: [
        {
          isGroup: false,
          title: "RobotSnapshot",
          chartType: "line",
          dataKey: "robotSnapshotList",
          apiUrl: "/athena/stats/query/robot/snapshot",
        },
        {
          isGroup: false,
          title: "JobSnapshot",
          chartType: "line",
          dataKey: "jobSnapshotList",
          apiUrl: "/athena/stats/query/job/snapshot",
        },
        {
          isGroup: false,
          title: "LatticeSnapshot",
          chartType: "line",
          dataKey: "latticeSnapshotList",
          apiUrl: "/athena/stats/query/lattice/snapshot",
        },
        {
          isGroup: true,
          title: "jobList示意",
          chartType: "bar",
          dataKey: "collet",
          apiUrl: "/athena/stats/query/job/collet",
          mockUrl: 'collet.js'
        },
        // 堆叠
        {
          isGroup: true,
          title: "堆叠图",
          chartType: "bar",
          dataKey: "collet",
          apiUrl: "/athena/stats/query/job/split/count",
          mockUrl: 'collet.js'
        },
        // 柱状/饼图/环图/堆叠
      ],
      types: [],
      defaultTypes: [],
    };
  },
  mounted() {
    const $chartBox = document.getElementById("J_chartBox");
    chartGroup.setChartGroupElement($chartBox);
    window.addEventListener("resize", this.chartResize);
  },
  destroyed() {
    window.removeEventListener("resize", this.chartResize);
    chartGroup.destroy();
    console.log("destroyed");
  },
  methods: {
    chooseChart(item) {
      this.currentChart = item;
      this.$nextTick(() => chartGroup.resize());
      this._reqData(item, this.defaultChartParams).then(res => {
        if (res.resultCode != 0) return;
        chartGroup.updateChartGroup(parseChartOptions(item, res.data));
        this.types = chartGroup.types || [];
        this.defaultTypes = chartGroup.defaultTypes || [];
      });
    },

    async _reqData(item, params) {
      const { date, cycle } = params;
      if (ISMOKE && item.mockUrl) {
        return await reqMock(item.mockUrl);
      }

      return await $req.post(
        item.apiUrl,
        {
          date: $utils.Tools.formatDate(date, "yyyy-MM-dd"),
          cycle,
        },
        { intercept: false },
      );
    },

    updateChart(options) {
      // switch (options.actionType) {
      //   case "keys":
      //     myChart.keysChange(options.selectedTypes);
      //     break;
      //   case "date":
      //   case "cycle":
      //     myChart.updateChartData({ date: options.date, cycle: options.cycle });
      //     break;
      // }
    },

    chartResize() {
      chartGroup.resize();
    },
  },
};
</script>

<style lang="less" scoped>
.statistics-con {
  .g-flex();
  justify-content: flex-start;
  flex-wrap: wrap;
  max-height: 100%;
  overflow-y: auto;
}

.chart-con {
  width: 100%;
  margin: 5px 12px;

  .chart-box {
    width: 100%;
  }
}
</style>
