<template>
  <div class="pallet-detail c-box">
    <div class="detail-content">
      <div class="detail-head">
        <div class="detail-head_title">
          {{ title }}
        </div>
        <div class="detail-head_handle">
          <el-button type="primary" class="w70" size="small" @click="save">
            {{ $t("lang.rms.fed.save") }}
          </el-button>
          <el-button size="small" class="w70" @click="cancel(false)">
            {{ $t("lang.common.cancel") }}
          </el-button>
        </div>
      </div>
      <div class="detail-form">
        <el-alert
          class="mt15 mb15"
          :title="$t('lang.rms.palletPositionManage.baseinfo')"
          type="info"
          :closable="false"
        />
        <el-form
          ref="detailForm"
          :model="formObj"
          :rules="rulesObj"
          label-width="156px"
          label-suffix=":"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item
                :label="$t('lang.rms.palletPositionManage.palletLatticeCode')"
                prop="palletLatticeCode"
              >
                <el-input
                  v-model="formObj.palletLatticeCode"
                  :disabled="mode === 'edit'"
                  :placeholder="$t('lang.rms.fed.pleaseEnter')"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item
                :label="$t('lang.rms.palletPositionManage.palletRackCode')"
                prop="palletRackCode"
              >
                <!-- <el-input v-model="formObj.palletRackCode" :placeholder="$t('lang.rms.fed.choose')" /> -->
                <el-select
                  v-model="formObj.palletRackCode"
                  filterable
                  :disabled="mode === 'edit'"
                  :placeholder="$t('lang.rms.fed.choose')"
                >
                  <el-option
                    v-for="(item, index) in palletRackList"
                    :key="index"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('lang.rms.palletPositionManage.layer')" prop="layer">
                <el-input v-model="formObj.layer" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item
                :label="`${layersNum}${$t('lang.rms.palletPositionManage.layerDes')}${$t(
                  'lang.rms.palletPositionManage.height',
                )}`"
                prop="height"
              >
                <el-input v-model="formObj.height" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item
                :label="$t('lang.rms.palletPositionManage.applyPalletCode')"
                prop="applyPalletCode"
              >
                <el-input
                  v-model="formObj.applyPalletCode"
                  :placeholder="$t('lang.rms.fed.pleaseEnter')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item
                :label="$t('lang.rms.palletPositionManage.occupyPalletCode')"
                prop="occupyPalletCode"
              >
                <el-input
                  v-model="formObj.occupyPalletCode"
                  :placeholder="$t('lang.rms.fed.pleaseEnter')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item
                :label="$t('lang.rms.palletPositionManage.palletLatticeHostCode')"
                prop="palletLatticeHostCode"
              >
                <el-input
                  v-model="formObj.palletLatticeHostCode"
                  :placeholder="$t('lang.rms.fed.pleaseEnter')"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    // 新增/修改模式
    mode: {
      type: String,
      default: "add",
    },
    rowDetail: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      palletRackList: [],
      formObj: {
        palletLatticeCode: "",
        palletRackCode: "",
        layer: "",
        height: "",
        applyPalletCode: "",
        occupyPalletCode: "",
        palletLatticeHostCode: "",
      },
      rulesObj: {
        palletLatticeCode: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnterContent"),
            trigger: "change",
          },
        ],
        palletRackCode: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnterContent"),
            trigger: "change",
          },
        ],
        layer: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnterContent"),
            trigger: "change",
          },
          {
            pattern: /^[1-9]\d*$/,
            message: this.$t("lang.rms.fed.pleaseEnterAnNumber"),
          },
        ],
        height: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnterContent"),
            trigger: "change",
          },
          {
            pattern: /^[1-9]\d*$/,
            message: this.$t("lang.rms.fed.pleaseEnterAnNumber"),
          },
        ],
      },
    };
  },
  computed: {
    layersNum() {
      let num = 1;
      if (this.formObj.layer > 0) {
        num = this.formObj.layer;
      }
      return num;
    },
    title() {
      return this.mode === "add"
        ? this.$t("lang.rms.fed.add")
        : this.$t("auth.rms.mapManage.button.edit");
    },
  },
  async created() {
    await this.getPalletRackList();
    this.formObj = {
      ...this.formObj,
      ...this.rowDetail,
    };
  },
  methods: {
    async getPalletRackList() {
      const res = await $req.get("/athena/palletRack/findCodes");
      const data = res.data;
      this.palletRackList = data;
    },
    save() {
      this.$refs.detailForm.validate(valid => {
        if (valid) {
          const url =
            this.mode === "add" ? "/athena/palletLattice/add" : "/athena/palletLattice/update";
          $req.post(url, { ...this.formObj }).then(res => {
            if (res.code === 0) {
              this.$message({
                type: "success",
                message: this.$t("lang.rms.api.result.ok"),
              });
              this.cancel();
            }
          });
        }
      });
    },
    // 取消
    cancel() {
      this.$emit("updateCom", {
        currentCom: "PalletPositionManageList",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.pallet-detail {
  width: 100%;

  .detail-content {
    padding: 10px 15px;
    background: #fff;
  }
  .detail-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
  }
  .detail-head_title {
    font-size: 16px;
    display: flex;
    align-items: center;
    &::before {
      content: "";
      display: inline-block;
      height: 21px;
      width: 4px;
      border-radius: 4px;
      background: #409eff;
      margin-right: 10px;
      vertical-align: text-bottom;
    }
  }
  .detail-form_title {
    padding: 8px 4px;
    margin: 6px 0;
    background: #eee;
  }

  .el-form {
    width: 80%;
  }
  .process-des {
    display: inline-block;
    margin-left: 8px;
    width: 100%;
  }
}
</style>
