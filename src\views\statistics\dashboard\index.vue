<template>
  <div class="dashboard" ref="dashboardRef" v-loading="isDataLoad">
    <template v-if="!isDataLoad">
      <DashboardChart
        v-for="(item, index) in options"
        :key="index"
        :disabled="item.isActive || !disabled"
        :option="item"
        :gridWidth="gridWidth"
      />
    </template>
  </div>
</template>

<script>
import DashboardChart from './dashboard-chart.vue'

export default {
  name: "dashboard",
  props: {
    options: {
      type: Array
    }
  },
  components: { DashboardChart },
  data() {
    return {
      width: 0,
      gridNumber: 48,
      isDataLoad: true,
      disabled: true,
    };
  },
  mounted() {
    this.resize();
    this.isDataLoad = false;
    window.addEventListener('resize', this.resize);
  },
  computed: {
    gridWidth() {
      return this.width / this.gridNumber;
    },
  },
  destroyed() {
    window.removeEventListener('resize', this.resize);
  },
  methods: {
    resize() {
      this.width = this.$refs.dashboardRef.offsetWidth;
    }
  },
};
</script>

<style lang="less" scoped>
.dashboard {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow-x: hidden;
  position: relative;
}
</style>
