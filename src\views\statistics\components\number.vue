<template>
  <section class="number-con">
    <number-item v-for="item in list" :key="item.id" :title="item.text" :number="item.number" />
  </section>
</template>

<script>
import NumberItem from "./common/number-item.vue";
export default {
  name: "statisticsNumber",
  components: { NumberItem },
  data() {
    return {
      list: [
        { id: 0, text: "数据名称1", number: 1231 },
        { id: 1, text: "数据名称2", number: 1232 },
        { id: 2, text: "数据名称3", number: 1233 },
        { id: 3, text: "数据名称3", number: 1234 },
      ],
    };
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.number-con {
  .g-flex();
  justify-content: flex-start;
  flex-wrap: wrap;
  max-height: 100%;
  overflow-y: auto;
  padding: 0 8px;
}
</style>
