import i18n from "@lang";

/**
 *  国际化
 *
 *  货格号： lang.rms.fed.destLatticeCode
 *  货箱号 lang.rms.fed.boxCode
 *  货架号 lang.rms.fed.destShelfCode
 *  货架面 lang.rms.fed.destShelfSide
 *  工作站编号 lang.rms.fed.stationNo
 *  货架位置查询 auth.rms.shelfLocationQuery.page
 *
 */
export const getSearchFormItem = () => {
  let start = new Date();
  let end = new Date();
  start.setTime(start.getTime() - 3 * 60 * 60 * 1000);
  return [
    // 货箱号
    {
      name: "boxCode",
      value: "",
      component: "elInput",
      span: 6,
      placeholder: i18n.t("lang.rms.fed.pleaseEnter"),
      label: i18n.t("lang.rms.fed.boxCode"),
    },
    // 时间范围
    {
      name: "dataRange",
      value: [start, end],
      component: "elDatePicker",
      span: 6,
      label: i18n.t("lang.rms.fed.updateTime"),
      type: "datetimerange",
      rangeSeparator: "-",
      valueFormat: "timestamp",
    },
  ];
};

export const getSearchTableItem = options => [
  // 时间
  {
    prop: "updateTime",
    label: i18n.t("lang.rms.fed.updateTime"),
  },
  // 货格号
  {
    prop: "destLatticeCode",
    label: i18n.t("lang.rms.fed.destLatticeCode"),
  },
  // 货架面
  {
    prop: "destShelfSide",
    label: i18n.t("lang.rms.fed.destShelfSide"),
    formatter(row, column) {
      return i18n.t(options.destShelfSide.find(i => i.value === row[column])?.label || row[column]);
    },
  },
  // 货架号
  {
    prop: "destShelfCode",
    label: i18n.t("lang.rms.fed.shelfCoding"),
  },
  // 工作站编号
  {
    prop: "stationId",
    label: i18n.t("lang.rms.fed.stationNo"),
    formatter(row, column) {
      return row["destShelfCode"] ? "" : row[column];
    },
  },
];
