<template>
  <geek-main-structure>
    <el-tabs v-model="activeName" class="tab-class">
      <!-- 故障配置 -->
      <el-tab-pane
        v-if="tabNamePerssion['exceptionConfig']"
        :label="$t('lang.rms.page.menu.exceptionConfig')"
        name="exceptionConfig"
      >
        <ExceptionConfig />
      </el-tab-pane>
      <!-- 通知管理 -->
      <el-tab-pane
        v-if="tabNamePerssion['noticeManagement']"
        :label="$t('lang.rms.page.menu.noticeManagement')"
        name="noticeManagement"
      >
        <NoticeManagement />
      </el-tab-pane>
    </el-tabs>
  </geek-main-structure>
</template>

<script>
import ExceptionConfig from "./exceptionConfig";
import NoticeManagement from "./noticeManagement";
export default {
  components: { ExceptionConfig, NoticeManagement },
  data() {
    return {
      tabNamePerssion: {
        exceptionConfig: this.getTabPermission("TabNoticeConfigFaultPage", "menuNoticeConfig"),
        noticeManagement: this.getTabPermission("TabNoticeConfigNoticePage", "menuNoticeConfig"),
      },
      defaultActive: "exceptionConfig",
    };
  },
  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
  },
  mounted() {
    this.defaultActive = $utils.Tools.getRouteQueryTabName(
      this.defaultActive,
      this.tabNamePerssion,
    );
  },
};
</script>
<style lang="scss" scoped></style>
