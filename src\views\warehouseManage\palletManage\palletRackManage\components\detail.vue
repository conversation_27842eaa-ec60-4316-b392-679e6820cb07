<template>
  <div class="pallet-detail c-box">
    <div class="detail-content">
      <div class="detail-head">
        <div class="detail-head_title">
          {{ title }}
        </div>
        <div class="detail-head_handle">
          <el-button type="primary" class="w70" size="small" @click="save">
            {{ $t("lang.rms.fed.save") }}
          </el-button>
          <el-button size="small" class="w70" @click="cancel(false)">
            {{ $t("lang.common.cancel") }}
          </el-button>
        </div>
      </div>
      <div class="detail-form">
        <el-alert
          class="mt15 mb15"
          :title="$t('lang.rms.palletPositionManage.baseinfo')"
          type="info"
          :closable="false"
        />
        <el-form
          ref="detailForm"
          :model="formObj"
          :rules="rulesObj"
          label-width="156px"
          label-suffix=":"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item
                :label="$t('lang.rms.palletPositionManage.palletRackCode')"
                prop="palletRackCode"
              >
                <el-input
                  v-model="formObj.palletRackCode"
                  :disabled="mode === 'edit'"
                  :placeholder="$t('lang.rms.fed.pleaseEnter')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item
                :label="$t('lang.rms.palletRackManage.locationCellCode')"
                prop="locationCellCode"
              >
                <el-input
                  :value="formObj.locationCellCode"
                  :readonly="true"
                  :placeholder="$t('lang.rms.fed.choose')"
                >
                  <el-button slot="append" icon="el-icon-search" @click="openMapSelect" />
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('lang.rms.palletRackManage.angle')" prop="angle">
                <el-input v-model="formObj.angle" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('lang.rms.palletRackManage.totalFloors')" prop="totalFloors">
                <el-input
                  v-model="formObj.totalFloors"
                  :placeholder="$t('lang.rms.fed.pleaseEnter')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item
                :label="$t('lang.rms.web.monitor.cell.fieldPrefix.length')"
                prop="length"
              >
                <el-input v-model="formObj.length" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('lang.rms.web.monitor.cell.fieldPrefix.width')" prop="width">
                <el-input v-model="formObj.width" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('lang.rms.palletRackManage.isObstacle')" prop="obstacle">
                <el-radio-group v-model="formObj.obstacle">
                  <el-radio :label="true">{{ $t("lang.rms.fed.yes") }}</el-radio>
                  <el-radio :label="false">{{ $t("lang.rms.fed.no") }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <keep-alive>
      <MapCellSelectDialog
        ref="mapSelectDialog"
        :multiple="false"
        @change="value => onSelect(value)"
      />
    </keep-alive>
  </div>
</template>
<script>
import MapCellSelectDialog from "../../../../components/map-select-dialog";

export default {
  components: { MapCellSelectDialog },
  props: {
    // 新增/修改模式
    mode: {
      type: String,
      default: "add",
    },
    rowDetail: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      palletPositionList: [],
      formObj: {
        palletRackCode: "",
        locationCellCode: "",
        totalFloors: "",
        length: "",
        width: "",
        obstacle: "",
      },
      rulesObj: {
        locationCellCode: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnterContent"),
            trigger: "change",
          },
        ],
        palletRackCode: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnterContent"),
            trigger: "change",
          },
        ],
        angle: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnterContent"),
            trigger: "change",
          },
          {
            pattern: /^([0-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360))$/,
            message: this.$t("lang.rms.fed.pleaseEnter11"),
          },
        ],
        totalFloors: [
          {
            required: true,
            message: this.$t("lang.rms.fed.pleaseEnterContent"),
            trigger: "change",
          },
          {
            pattern: /^[1-9]\d*$/,
            message: this.$t("lang.rms.fed.pleaseEnterAnNumber"),
          },
        ],
        length: [
          // { required: true, message: this.$t("lang.rms.fed.pleaseEnterContent"), trigger: "change" },
          {
            pattern: /(^[0-9]*$)|(^[0-9]*[.]{1}[0-9]{1}$)/,
            message: this.$t("lang.rms.fed.pleaseEnter12"),
          },
        ],
        width: [
          // { required: true, message: this.$t("lang.rms.fed.pleaseEnterContent"), trigger: "change" },
          {
            pattern: /(^[0-9]*$)|(^[0-9]*[.]{1}[0-9]{1}$)/,
            message: this.$t("lang.rms.fed.pleaseEnter12"),
          },
        ],
      },
    };
  },
  computed: {
    title() {
      return this.mode === "add"
        ? this.$t("lang.rms.fed.add")
        : this.$t("auth.rms.mapManage.button.edit");
    },
  },
  created() {
    this.formObj = {
      ...this.formObj,
      ...this.rowDetail,
    };
    this.formObj.obstacle = this.rowDetail.isObstacle;
  },
  methods: {
    openMapSelect() {
      this.$refs.mapSelectDialog.open();
    },
    onSelect(value) {
      this.formObj.locationCellCode = value;
      console.log(this.formObj);
    },
    save() {
      this.$refs.detailForm.validate(valid => {
        if (valid) {
          const url = this.mode === "add" ? "/athena/palletRack/add" : "/athena/palletRack/update";
          $req.post(url, { ...this.formObj }).then(res => {
            if (res.code === 0) {
              this.$message({
                type: "success",
                message: this.$t("lang.rms.api.result.ok"),
              });
              this.cancel();
            }
          });
        }
      });
    },
    // 取消
    cancel() {
      this.$emit("updateCom", {
        currentCom: "PalletRackManageList",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.pallet-detail {
  width: 100%;

  .detail-content {
    padding: 10px 15px;
    background: #fff;
  }
  .detail-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
  }
  .detail-head_title {
    font-size: 16px;
    display: flex;
    align-items: center;
    &::before {
      content: "";
      display: inline-block;
      height: 21px;
      width: 4px;
      border-radius: 4px;
      background: #409eff;
      margin-right: 10px;
      vertical-align: text-bottom;
    }
  }
  .detail-form_title {
    padding: 8px 4px;
    margin: 6px 0;
    background: #eee;
  }

  .el-form {
    width: 80%;
  }
  .process-des {
    display: inline-block;
    margin-left: 8px;
    width: 100%;
  }
}
</style>
