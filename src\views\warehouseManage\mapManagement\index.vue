<template>
  <geek-main-structure>
    <div class="map-management-header">
      <geek-tabs-nav
        :nav-list="navList"
        :active-nav-id="activeNavId"
        @select="tabsNavChange"
      />
      <div>
        <el-button
          type="primary"
          icon="el-icon-upload"
          @click="importMap"
        >
          {{ $t("lang.rms.web.map.version.importMap") }}
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-document-add"
          @click="createMap"
        >
          {{ $t("lang.rms.fed.createAMap") }}
        </el-button>
      </div>
    </div>

    <div class="table-content">
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :expand-row-keys="expandRowKeys"
        class="map-management-table"
        :cell-class-name="customCellName"
      >
        <el-table-column type="expand">
          <table-expend
            slot-scope="props"
            :row-data="props.row"
            @refreshList="refreshList"
            @openManageQrCode="handleOpenManageQrCode"
            @openManageWaitTaskGroup="handleOpenManageWaitTaskGroup"
          />
        </el-table-column>
        <el-table-column
            prop="id"
            :label="$t('lang.rms.fed.mapID')"
            width="120"
        />
        <el-table-column
            prop="name"
            :label="$t('lang.rms.fed.mapName')"
            width="200"
            :show-overflow-tooltip="true"
        />
        <el-table-column prop="statusLabel" width="210" :label="$t('lang.rms.fed.inputState')">
          <template slot-scope="scope">
            <p :class="infoColor(scope.row.uploadStatus)">{{ $t(scope.row.statusLabel) }}</p>
          </template>
        </el-table-column>
        <el-table-column prop="remark" :label="$t('lang.rms.web.map.version.updateNotes')">
          <template slot-scope="scope">
            <p :class="infoColor(scope.row.uploadStatus)">{{ $t(scope.row.remark) }}</p>
          </template>
        </el-table-column>
        <el-table-column
            width="210"
            prop="lastUpdateTime"
            :label="$t('lang.rms.web.map.version.lastUpdate')"
        />
        <el-table-column prop="operator" width="150" :label="$t('lang.rms.web.map.version.operator')" />
        
        <el-table-column
          prop="operate"
          fixed="right"
          :label="$t('lang.rms.fed.chargerOperating')"
          width="320"
        >
          <table-operation
            slot-scope="scope"
            :row-data="scope.row"
            @refreshList="refreshList"
            @deleting="deleting"
          />
        </el-table-column>
      </el-table>

      <geek-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total-page="totalPage"
        @currentPageChange="currentPageChange"
        @pageSizeChange="pageSizeChange"
      />
    </div>
    <keep-alive>
      <component
        :is="currentComponent"
        @refreshList="refreshList"
        @updateStatusTest="updateStatus"
      />
    </keep-alive>
    <dialog-manage-qrcode
      v-if="manageQrCodeVisible"
      :visible.sync="manageQrCodeVisible"
      v-bind="manageQrCodeProps"
    />
    <dialog-manage-waitTaskGroup
      v-if="manageWaitTaskGroupVisible"
      :visible.sync="manageWaitTaskGroupVisible"
      v-bind="manageWaitTaskGroupProps"
    />
  </geek-main-structure>
</template>

<script>
import { mapMutations, mapState } from 'vuex'
import tableExpend from './components/main-table-expand'
import tableOperation from './components/main-table-operating'
import dialogImportMap from './components/dialog-import-map'
import dialogMapAddCopy from './components/dialog-map-add-copy'
import dialogFloorCreate from './components/dialog-floor-create'
import dialogIssueMap from './components/dialog-issue-map'
import dialogApplicationMap from './components/dialog-application-map'
import dialogImportFloor from './components/dialog-import-floor'
import dialogImportMask from './components/dialog-import-mask'
import dialogExportFloor from './components/dialog-export-floor'
import dialogManageQrcode from './components/dialog-manage-qrcode'
import dialogManageWaitTaskGroup from './components/dialog-manage-waitTaskGroup' // 趴窝分组弹窗
export default {
  name: 'MapManagement',
  components: {
    tableExpend,
    tableOperation,
    dialogMapAddCopy,
    dialogFloorCreate,
    dialogImportMap,
    dialogIssueMap,
    dialogApplicationMap,
    dialogImportFloor,
    dialogImportMask,
    dialogExportFloor,
    dialogManageQrcode,
    dialogManageWaitTaskGroup
  },
  data() {
    return {
      expandRowKeys: [],
      activeNavId: 'tabs-nav-1',
      navList: [
        {
          id: 'tabs-nav-1',
          name: '所有列表',
          text: 'lang.rms.web.map.version.allList'
        },
        {
          id: 'tabs-nav-2',
          name: '当前工作地图',
          text: 'lang.rms.web.map.version.cuurentMap'
        }
      ],
      tableData: [],
      totalPage: 1,
      currentPage: 1,
      pageSize: 10,
      manageQrCodeVisible: false,
      manageQrCodeProps: {},
      manageWaitTaskGroupProps: {},
      manageWaitTaskGroupVisible: false,
      loading: false,
      //延迟轮训
      updateTimeout: null,
    }
  },
  computed: {
    ...mapState('mapManagement', ['currentComponent']),
    infoColor() {
      return (uploadStatus) => {
        if ([1, 2].includes(uploadStatus)) {
          return 'uploading'
        }
        if (uploadStatus === 3) {
          return 'error'
        }
        if (uploadStatus === 0) {
          return 'ready'
        }
        return ''
      }
    }
  },
  activated() {
    this.getList()
  },
  deactivated() {
    if (this.currentComponent) this.hideDialog()
    this.$message.closeAll()
  },
  methods: {
    ...mapMutations('mapManagement', ['showDialog', 'hideDialog']),
    deleting(flag) {
      this.loading = flag
    },
    customCellName({ row, column }) {
      if (row.uploadStatus !== undefined && !['id', 'name', 'statusLabel', 'remark', 'operate'].includes(column.property)) {
        return 'hidden-cell'
      }
      return ''
    },
    importMap() {
      this.showDialog({
        currentComponent: 'dialogImportMap',
        title: this.$t('lang.rms.web.map.version.importMap'),
        rowData: null
      })
    },
    createMap() {
      this.showDialog({
        currentComponent: 'dialogMapAddCopy',
        title: this.$t('lang.rms.fed.createAMap'),
        rowData: null
      })
    },
    getList() {
      this.expandRowKeys = []
      this.tableData = []
      this.loading = true
      if (this.activeNavId === 'tabs-nav-1') {
        this.getAllList()
      } else {
        this.getCurrentList()
      }
      this.$nextTick(() => {
        this.$refs.tableRef.doLayout()
      })
    },
    refreshList() {
      this.currentPage = 1
      this.getList()
    },
    getAllList() {
      const { currentPage, pageSize } = this.$data
      $req
        .postParams('/athena/map/version/pageList', {
          currentPage,
          pageSize
        })
        .then(res => {
          const data = res.data
          const list = data.recordList
          if (list.length > 0) {
            this.tableData = this.formatTableList(list)
            this.updateStatus()
          }
          this.totalPage = data.pageCount
          this.loading = false
        })
    },
    getCurrentList() {
      $req.post('/athena/map/version/current').then(res => {
        const list = res.data ? [res.data] : []
        this.totalPage = 1
        if (list.length > 0) {
          this.tableData = this.formatTableList(list)
          this.updateStatus()
        }
        this.loading = false
      })
    },
    formatTableList(list) {
      list.forEach(item => {
        item.lastUpdateTime = $utils.Tools.formatDate(item.lastUpdateTime, 'yyyy-MM-dd hh:mm:ss')
        item.statusLabel = this.$t(item.statusI18n)
      })
      return list
    },
    //更新正在上传的地图状态
    async updateStatus() {
      //"status":1, #导入状态0-未开始  1-导入中  2-导入成功  3-导入失败
      const mapStatus = new Map([
        [0,'lang.rms.fe.map.import.status.waiting'],
        [1,'lang.rms.fe.map.import.status.processing'],
        [2,'lang.rms.fe.map.import.status.success'],
        [3,'lang.rms.fe.map.import.status.failed'],
      ])
      const res = await $req.get('/athena/map/manage/mapImportProgress', {})
      const importProgressData = res.data
      // console.log(importProgressData,this.tableData)
      if (!importProgressData.length || !this.tableData.length) return
      //筛选所有楼层正在导入的地图
      // const expandData = importProgressData.filter(item => {
      //   return item.floorId
      // })
      // this.expandRowKeys = expandData.map(item => item.mapId)
      // console.log(this.expandRowKeys)
      importProgressData.forEach(uploadMapInfo => {
        const { floorId, status, phaseI18N, resultMsgI18N, mapId, updateTime } = uploadMapInfo
        const importMapIndex = this.tableData.findIndex(item => item.id.toString() === mapId.toString())
        if (importMapIndex === -1) return
        const importMapData = this.tableData[importMapIndex]
        //判断当前导入的是整个地图还是楼层
        if (floorId) {
          let importMapFloors = importMapData.floods || []
          importMapFloors = importMapFloors.map(floorItem => {
            if (floorItem.floorId === floorId) {
              floorItem.uploadStatus = status
              // floorItem.phaseI18N = resultMsgI18N ? this.$t(resultMsgI18N) : this.$t(phaseI18N)
              floorItem.phaseI18N = resultMsgI18N ? $utils.Tools.transMsgLang(resultMsgI18N) : this.$t(phaseI18N)
            } else {
              floorItem.uploadStatus = undefined
              floorItem.phaseI18N = null
            }
            return floorItem
          })
          importMapData.floods = importMapFloors
          if (!this.expandRowKeys.includes(mapId)) this.expandRowKeys.push(mapId)
        } else {
          importMapData.uploadStatus = status
          importMapData.statusLabel = this.$t(mapStatus.get(status))
          // importMapData.remark = resultMsgI18N ? this.$t(resultMsgI18N) : this.$t(phaseI18N)
          importMapData.remark = resultMsgI18N ? $utils.Tools.transMsgLang(resultMsgI18N) : this.$t(phaseI18N)
        }
        if (status === 2) {
          importMapData.lastUpdateTime = $utils.Tools.formatDate(updateTime, 'yyyy-MM-dd hh:mm:ss')
        }
        this.tableData.splice(importMapIndex, 1, importMapData)
      })
      //判断是否都导入完成
      const statusArr = importProgressData.map(item => item.status)
      //导入地图是否成功
      const importMapSuccess = importProgressData.some(item => (item.status === 2 && !item.floorId))
      console.log(importProgressData,importMapSuccess)
      //如果有导入成功的地图，直接刷新
      if (importMapSuccess) {
        this.refreshList()
        this.updateTimeout = null
        clearTimeout(this.updateTimeout)
        return
      }
      //判断是否有导入中
      if (statusArr.includes(1)) {
        this.updateTimeout = setTimeout(() => {
          this.updateStatus()
        }, 3 * 1000)
      } else {
        const finished = importProgressData.every(item => (item.status === 3 || item.status === 2))
        if (finished) {
          this.updateTimeout = null
          clearTimeout(this.updateTimeout)
        }
      }
    },

    currentPageChange(currentPage) {
      this.currentPage = currentPage
      this.getList()
    },
    pageSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getList()
    },
    tabsNavChange(id) {
      this.activeNavId = id
      this.currentPage = 1
      this.getList()
    },
    handleOpenManageQrCode(mapId, floorId) {
      this.manageQrCodeVisible = true
      this.manageQrCodeProps = { mapId, floorId }
    },
    handleOpenManageWaitTaskGroup(mapId, floorId) {
      this.manageWaitTaskGroupVisible = true
      this.manageWaitTaskGroupProps = { mapId, floorId }
    }
  }
}
</script>
<style lang="less">
.el-table .hidden-cell {
  color: rgba(0, 0, 0, 0);
  .cell {
    user-select: none !important;
  }
}
.el-table .cell.el-tooltip{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
  max-width: 200px;
//max-width: 80px;
}
</style>
<style lang="less" scoped>
.uploading {
  color: #2ac039;
}
.error {
  color: #d9001b;
}
.ready {
  color: #ef973b
}
.map-management-header {
  .g-flex();
  padding-bottom: 12px;
}

.table-content {
  box-shadow: 0px -5px 10px -5px rgb(0 0 0 / 10%);

  .map-management-table {
    width: 100%;
  }
}
</style>
