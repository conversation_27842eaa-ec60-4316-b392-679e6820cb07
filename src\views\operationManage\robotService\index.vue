<!--维修调度-->
<template>
  <geek-main-structure class="robot-service">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        v-if="tabNamePerssion['Service']"
        :label="$t('lang.rms.fed.CallRobotForService')"
        name="Service"
      >
        <el-form
          ref="formService"
          label-position="right"
          label-width="200px"
          :model="formService"
          class="tabpanel"
          :rules="rules"
        >
          <el-form-item :label="$t('lang.rms.fed.serviceArea')">
            <el-select
              v-model="formService.recycleId"
              :no-data-text="$t('lang.rms.fed.notExistData')"
              :placeholder="$t('lang.rms.fed.choose')"
            >
              <el-option
                v-for="item in maintainArea"
                :key="item.recycleId"
                :label="item.recycleId"
                :value="item.recycleId"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang.rms.fed.robotInTask')">
            {{ curMaintainAreaData.robotId || "" }}
          </el-form-item>
          <el-form-item
            :label="$t('lang.rms.fed.robot')"
            prop="robotId"
            :rules="[
              {
                required: true,
                message: $t('lang.rms.fed.pleaseEnterTheRobotID'),
                trigger: 'blur',
              },
              { validator: checkIsInteger, trigger: 'blur' },
            ]"
          >
            <el-input v-model.number="formService.robotId" maxlength="9" />
          </el-form-item>

          <el-form-item :label="$t('lang.rms.fed.robotLoading')">
            {{ curRobotpercentum }}
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              :disabled="curMaintainAreaData.isArrivedMaintainArea"
              @click="goMaintrin"
            >
              {{ $t("lang.rms.fed.callRobot") }}
            </el-button>
          </el-form-item>
        </el-form>
        <div class="bigBtn">
          <el-button
            type="primary"
            :disabled="!curMaintainAreaData.isArrivedMaintainArea"
            @mousedown.native="pointIn"
            @mouseup.native="pointStop"
          >
            {{ $t("lang.rms.fed.moveRobot") }}
          </el-button>
        </div>
      </el-tab-pane>
      <el-tab-pane
        v-if="tabNamePerssion['Return']"
        :label="$t('lang.rms.fed.returnRobot')"
        name="Return"
      >
        <el-form
          ref="robotForm"
          label-position="right"
          label-width="200px"
          :model="formReturn"
          :rules="rules"
        >
          <el-form-item :label="$t('lang.rms.fed.serviceArea')">
            <el-select
              v-model="formReturn.recycleId"
              :no-data-text="$t('lang.rms.fed.notExistData')"
              :placeholder="$t('lang.rms.fed.choose')"
            >
              <el-option
                v-for="item in maintainArea"
                :key="item.recycleId"
                :label="item.recycleId"
                :value="item.recycleId"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang.rms.fed.robotInTask')">
            {{ curMaintainAreaData.robotId || "" }}
          </el-form-item>
          <el-form-item
            :label="$t('lang.rms.fed.robot')"
            prop="robotId"
            :rules="[
              {
                required: true,
                message: $t('lang.rms.fed.pleaseEnterTheRobotID'),
                trigger: 'blur',
              },
              { validator: checkIsInteger, trigger: 'blur' },
            ]"
          >
            <el-input v-model.number="formReturn.robotId" maxlength="9" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="recoverMaintrin">
              {{ $t("lang.rms.fed.returnPlace") }}
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </geek-main-structure>
</template>

<script>
import { MapWorker } from "@geek_map/ts-map-fe/libs/monitor2d.min.js";
let mapWorker = null;

export default {
  name: "RobotService",
  data() {
    const checkQrobot = (rule, value, callback) => {
      const reg = new RegExp("^[0-9]*$");
      let currentCheckItem;
      if (this.currentTabIndex === 0) {
        // 维修调度
        currentCheckItem = this.$refs.childUI.getDate();
      } else {
        // 机器人返场
        currentCheckItem = this.$refs.childSecondUI.getDate();
      }
      if (currentCheckItem) {
        if (!reg.test(currentCheckItem)) {
          callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
        } else {
          callback();
        }
      } else {
        callback(new Error(this.$t("lang.rms.fed.pleaseEnterTheRobotID")));
      }
    };
    return {
      tabNamePerssion: {
        Service: this.getTabPermission("TabMaintenanceScheduleOnePage", "maintenanceSchedule"),
        Return: this.getTabPermission("TabMaintenanceScheduleRobotPage", "maintenanceSchedule"),
      },
      defaultActive: "Service",
      formService: {
        robotId: null,
        recycleId: null,
      },
      formReturn: {
        robotId: null,
        recycleId: null,
      },
      maintainArea: [],
      curMaintainAreaData: {},
      curRobotpercentum: "",
      timer: null,
      currentTabIndex: 0,
      // 校验规则代码
      rules: {
        qID: [
          {
            required: true,
            type: "number",
            validator: checkQrobot,
            trigger: "blur",
          },
        ],
      },
      tasksetInterval: null,
    };
  },
  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
  },
  activated() {
    this.init();
    this.getMaintainArea();

    this.defaultActive = $utils.Tools.getRouteQueryTabName(
      this.defaultActive,
      this.tabNamePerssion,
    );
  },

  deactivated() {
    clearInterval(this.tasksetInterval);
    this.tasksetInterval = null;
    this.resetForm();
    if (mapWorker) mapWorker.destroy();
    mapWorker = null;
  },
  methods: {
    init() {
      mapWorker = new MapWorker(this.getWsUrl());
      mapWorker.init();
    },
    // 重置form表单
    resetForm() {
      this.formService = {
        robotId: null,
        recycleId: null,
      };
      this.formReturn = {
        robotId: null,
        recycleId: null,
      };
    },
    // 验证是否是正整数
    checkIsInteger(rule, value, callback) {
      if (Number.isInteger(value)) {
        callback();
      } else {
        callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
      }
    },
    getWsUrl() {
      let protocol = window.location.protocol === "http:" ? "ws" : "wss";
      let hostname = window.location.host;
      if ($req.isDev) {
        hostname = new URL($req.API_URL).hostname;
      }

      const RMSPermission = $utils.Data.getRMSPermission();
      const token = RMSPermission ? `?token=${$utils.Data.getToken()}` : "";

      return `${protocol}://${hostname}/athena-monitor${token}`;
    },
    getMaintainArea() {
      this.maintainArea = [];
      $req.get("/athena/maintain/getMaintainArea").then(res => {
        if (res.data && res.data.length > 0) {
          this.maintainArea = res.data;
          this.formService.recycleId = res.data[0].recycleId;
          this.formReturn.recycleId = res.data[0].recycleId;
          this.tasksetInterval = setInterval(this.getOneMaintainArea, 1000);
        }
      });
    },
    getOneMaintainArea() {
      $req.get("/athena/maintain/getMaintainArea").then(res => {
        if (res.data && res.data.length > 0) {
          for (let i = 0; i < res.data.length; i++) {
            const item = res.data[i];
            if (this[`form${this.activeName}`].recycleId === item.recycleId) {
              this.curMaintainAreaData = item;
              break;
            }
          }
        }

        this.getMaintainPercentum(this.curMaintainAreaData.robotId);
      });
    },
    getMaintainPercentum(robotId) {
      if (!robotId) {
        this.curRobotpercentum = "";
        return;
      }
      $req
        .post("/athena/maintain/getMaintainProgress", {
          robotId: robotId,
        })
        .then(res => {
          this.curRobotpercentum = res.data && res.data.progress + "\xa0\xa0\xa0" + "米";
        });
    },
    goMaintrin() {
      this.$refs["formService"].validate(valid => {
        if (valid) {
          const cmd = {
            instruction: "GO_MAINTAIN_TO_STAY",
            recycleId: this.formService.recycleId,
            robotId: this.formService.robotId,
          };

          const msgType = "RobotInstructionRequestMsg";
          mapWorker.reqSocket(msgType, cmd).then(res => {
            if (res.msgType !== "RobotInstructionResponseMsg") return;
            const body = res?.body || {};
            const code = body && body.hasOwnProperty("code") ? body.code : -1;
            if (code === 0) {
              this.$success(this.$t("lang.rms.fed.callRobotSucceeded"));
              this.formService.robotId = "";
            } else {
              const msg = this.transMsgLang(body?.msg || "");
              this.$error(msg);
            }
          });
        }
      });
    },
    recoverMaintrin() {
      this.$refs["robotForm"].validate(valid => {
        if (valid) {
          const data = {
            instruction: "RECOVER_IN_MAINTAIN",
            recycleId: this.formReturn.recycleId,
            robotId: Number(this.formReturn.robotId),
          };

          const msgType = "RobotInstructionRequestMsg";
          mapWorker.reqSocket(msgType, cmd).then(res => {
            if (res.msgType !== "RobotInstructionResponseMsg") return;
            const body = res?.body || {};
            const code = body && body.hasOwnProperty("code") ? body.code : -1;
            if (code === 0) {
              this.$success(this.$t("lang.rms.fed.robotReturnSuccessed"));
              this.formReturn.robotId = "";
            } else {
              const msg = this.transMsgLang(body?.msg || "");
              this.$error(msg);
              if (data.code === 10103) {
                this.formReturn.robotId = "";
              }
            }
          });
        }
      });
    },

    pointIn() {
      if (!this.curMaintainAreaData.robotId) return;
      $req.post("/athena/maintain/clearWaitPoint", {
        robotId: this.curMaintainAreaData.robotId,
      });
      this.timer = setTimeout(this.pointIn, 500);
    },
    pointStop() {
      clearTimeout(this.timer);
    },
    handleClick(tab) {
      this.currentTabIndex = Number(tab.index);
    },
  },
};
</script>

<style lang="less" scoped>
.tabpanel {
  width: 600px;
  margin: 0 auto;

  .el-select {
    width: 100%;
  }
}

.bigBtn {
  margin: 40px 0 0;
  padding: 40px 0;
  border-top: 1px solid #eeeeee;
  text-align: center;
}

.bigBtn .el-button {
  height: 200px;
  width: 200px;
  border-radius: 100px;
  font-size: 22px;
}

.bigBtn .el-button:active {
  background-color: #c00 !important;
}
</style>
