import { NodeAttrEditConf } from "@packages/type/editUiType";
import { useI18n } from "@packages/hook/useI18n";
// 坐标
export const LOCATION_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  const { t } = useI18n();
  return {
    name: "location",
    tabTitle: "lang.rms.fed.coordinate",
    labelWidth: "100px",
    formItem: [
      // 非标单元格
      {
        prop: "nonstandardNode",
        label: "lang.rms.fed.customCellCode",
        component: "elSwitch",
        appendAttrsFn(val: boolean, allData: any) {
          return {
            condition: !!allData.isQrNode,
          };
        },
      },
      // 绝对坐标X
      {
        prop: "locationX",
        label: `${t("lang.rms.fed.Location")}X`,
        describe: `${t("lang.rms.fed.Location")}/m`,
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 0.1,
        precision: 3,
        set(value: number, formData: { [k: string]: any }) {
          formData.location || (formData.location = {});
          formData.startBounds || (formData.startBounds = {});
          formData.location.x = value;
          formData.startBounds.x = value - formData.length / 2;
        },
        get(fromData: { [k: string]: any }) {
          return fromData.location?.x || 0;
        },
      },
      // 绝对坐标Y
      {
        prop: "locationY",
        label: `${t("lang.rms.fed.Location")}Y`,
        describe: `${t("lang.rms.fed.Location")}/m`,
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 0.1,
        precision: 3,
        set(value: number, formData: { [k: string]: any }) {
          formData.location || (formData.location = {});
          formData.startBounds || (formData.startBounds = {});
          formData.location.y = value;
          formData.startBounds.y = value - formData.width / 2;
        },
        get(fromData: { [k: string]: any }) {
          return fromData.location?.y || 0;
        },
      },
      // 单元格宽
      {
        prop: "length",
        label: "lang.rms.fed.cellWidth",
        describe: `${t("lang.rms.fed.cellSize")}/m`,
        component: "elInputNumber",
        min: 0,
        max: 10000,
        step: 0.1,
        precision: 3,
        set(value: number, fromData: { [k: string]: any }) {
          fromData.location || (fromData.location = {});
          fromData.startBounds || (fromData.startBounds = {});
          fromData.length = value;
          fromData.startBounds.x = fromData.location.x - value / 2;
        },
      },
      // 单元格高
      {
        prop: "width",
        label: "lang.rms.fed.cellHeight",
        describe: `${t("lang.rms.fed.cellSize")}/m`,
        component: "elInputNumber",
        min: 0,
        max: 10000,
        step: 0.1,
        precision: 3,
        set(value: number, fromData: { [k: string]: any }) {
          fromData.location || (fromData.location = {});
          fromData.startBounds || (fromData.startBounds = {});
          fromData.width = value;
          fromData.startBounds.y = fromData.location.y - value / 2;
        },
      },
      // 左下坐标X
      {
        prop: "startBoundsX",
        label: `${t("lang.rms.fed.lbLocation")}X`,
        describe: `${t("lang.rms.fed.cellAbsoluteCoordinates")}/m`,
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 0.1,
        precision: 3,
        set(value: number, fromData: { [k: string]: any }) {
          fromData.location || (fromData.location = {});
          fromData.startBounds || (fromData.startBounds = {});
          fromData.startBounds.x = value;
          fromData.location.x = value + fromData.length / 2;
        },
        get(fromData: { [k: string]: any }) {
          return fromData.startBounds?.x || 0;
        },
      },
      // 左下坐标Y
      {
        prop: "startBoundsY",
        label: `${t("lang.rms.fed.lbLocation")}Y`,
        describe: `${t("lang.rms.fed.cellAbsoluteCoordinates")}/m`,
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 0.1,
        precision: 3,
        set(value: number, fromData: { [k: string]: any }) {
          fromData.location || (fromData.location = {});
          fromData.startBounds || (fromData.startBounds = {});
          fromData.startBounds.y = value;
          fromData.location.y = value + fromData.width / 2;
        },
        get(fromData: { [k: string]: any }) {
          return fromData.startBounds?.y || 0;
        },
      },
      // 索引坐标X
      {
        prop: "indexX",
        label: `${t("lang.rms.fed.textIndexCoordinates")}X`,
        describe: "lang.rms.fed.qrCodeCellIndexXCoordinate",
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 1,
        appendAttrsFn(val: boolean, allData: any) {
          return {
            condition: !!allData.isQrNode,
          };
        },
      },
      // 索引坐标Y
      {
        prop: "indexY",
        label: `${t("lang.rms.fed.textIndexCoordinates")}Y`,
        describe: "lang.rms.fed.qrCodeCellIndexYCoordinate",
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 1,
        appendAttrsFn(val: boolean, allData: any) {
          return {
            condition: !!allData.isQrNode,
          };
        },
      },
      // 精准点位坐标功能
      {
        appendSlotName: "pinpointSlot",
        onlyComponent: true,
      },
    ],
  };
};
