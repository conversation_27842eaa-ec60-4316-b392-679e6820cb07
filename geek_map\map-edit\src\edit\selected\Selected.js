import RenderSelected from './RenderSelected'
import EventBus from '../eventBus/EventBus'
import LayerManager from '../layerManager/LayerManager'
import Control from "../control/Control";
class Selected {
  static isMultipleSelected = false
  static selectedElement = new Map()
  //判断是否点击了元素
  static isClickedElement = false
  //判断是否选中
  static isHasSelected(id) {
    return this.selectedElement.has(id)
  }
  //渲染选择态
  static renderSelected(id,$el){
    // if(Mode.mode.action !== 'default') return
    $el.zIndex = 1000
    this.isClickedElement = true
    //判断是否为多选
    if(this.isMultipleSelected){
      RenderSelected.renderSelectedByType($el)
      this.selectedElement.set(id,$el)
    }else{
      const isHas = this.isHasSelected(id)
      if(isHas) {
        //点击选中自身
        EventBus.$emit('selected',null)
        this.resetAllSelected()
        return
      }
      this.resetAllSelected()
      RenderSelected.renderSelectedByType($el)
      this.selectedElement.set(id,$el)
      this._selectedCallback(id,$el)
    }
  }
  //选中回调
  static _selectedCallback(id,$el){
    // const layerName = $el.parent.name
    // const properties = LayerManager.getProperties(layerName,id)
    const layerName = $el.layerName
    const properties = LayerManager.getProperties({layerName,id})
    EventBus.$emit('selected',[properties])
  }
  //恢复未选中态
  static resetSelected(id){
    const $el = this.selectedElement.get(id)
    $el.zIndex = 1
    RenderSelected.renderSelectedByType($el,false)
    this.selectedElement.delete(id)
  }
  //恢复所有的选中态
  static resetAllSelected() {
    [...this.selectedElement].forEach(arr => {
      const id = arr[0]
      this.resetSelected(id)
    })
    this.selectedElement.clear()
    Control.enableDrag(true)
    this.isMultipleSelected = false
  }
  //获取选中的对象
  static getSelected(id){
    const selected = this.selectedElement.get(id)
    // if(!selected) return throw Error("无该选中内容")
    return selected
  }
  //获取素有的选中数据
  static getAllSelected() {
    const selectedData = [];
    [...this.selectedElement].forEach(arr => {
      const [id,$el] = arr
      const {layerName} = $el
      const info = LayerManager.getProperties({layerName,id})
      selectedData.push({...info})
    })
    // EventBus.$emit('selected',selectedData)
    return selectedData
  }
}
export default Selected
