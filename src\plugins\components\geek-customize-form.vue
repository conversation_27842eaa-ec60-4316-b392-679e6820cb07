<template>
  <div class="geek-customize-form">
    <!-- v-bind会自动将form的配置属性赋值过来 -->
    <el-form
      ref="customizeForm"
      v-bind="queryForm.attrs"
      :model="queryForm.props"
      :rules="queryForm.rules"
      size="small"
      :class="{ 'form-block': queryForm.attrs && !queryForm.attrs.inline }"
    >
      <template v-for="(config, prop) in queryForm.configs">
        <slot v-if="config.slotName" :name="config.slotName" :queryForm="queryForm" :config="config" />
        <el-form-item v-else :key="prop" :prop="prop" :label="$t(config.label)">
          <!-- 文本框 类-->
          <component
            :is="'el-' + config.tag"
            v-if="
              config.tag === 'input' ||
              config.tag === 'input-number' ||
              config.tag === 'time-picker' ||
              config.tag === 'date-picker' ||
              config.tag === 'time-select' ||
              config.tag === 'switch'
            "
            :key="config.prop"
            v-model="queryForm.props[prop]"
            clearable
            v-bind="config"
            placement="left"
            :placeholder="dynamicPlaceholder(config)"
            :start-placeholder="$t(config['start-placeholder'])"
            :end-placeholder="$t(config['end-placeholder'])"
          />
          <!-- 选择框类 -->
          <el-radio-group
            v-if="config.tag === 'radio-group'"
            v-model="queryForm.props[prop]"
            :placeholder="dynamicPlaceholder(config)"
          >
            <el-radio v-for="item in config.options" :key="item.value" :label="$t(item.value)">
              {{ $t(item.label) }}
            </el-radio>
          </el-radio-group>
          <el-checkbox-group v-if="config.tag === 'checkbox-group'" v-model="queryForm.props[prop]">
            <el-checkbox v-for="item in config.options" :key="item.value" :label="$t(item.value)">
              {{ $t(item.label) }}
            </el-checkbox>
          </el-checkbox-group>
          <el-select
            v-if="config.tag === 'select'"
            v-model="queryForm.props[prop]"
            :multiple="config.multiple"
            :filterable="config.filterable"
            :collapse-tags="config.collapseTags || false"
            :clearable="config.clearable ? false : true"
            :placeholder="dynamicPlaceholder(config)"
          >
            <el-option
              v-for="item in config.options"
              :key="item.value"
              :value="item.value"
              :label="$t(item.label)"
              :disabled="item.hasOwnProperty('disStatus') ? item.disStatus : false"
            />
          </el-select>
        </el-form-item>
      </template>
      <el-form-item v-if="formConfig.operations && formConfig.operations.length > 0" class="operation-button">
        <el-button
          v-for="item in formConfig.operations"
          v-bind="item"
          :key="item.label"
          :size="item.mini ? item.mini : 'mini'"
          :icon="item.icon ? item.icon : ''"
          :disabled="item.hasOwnProperty('disabled') ? item.disabled : false"
          @click="operationHandler(item.handler)"
        >
          {{ $t(item.label) }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: "GeekCustomizeForm",
  props: {
    formConfig: {
      required: true,
      configs: {
        required: true,
      },
      operations: {
        default: () => [],
      },
    },
  },
  data() {
    return {
      queryForm: {},
    };
  },
  watch: {
    formConfig: {
      deep: true,
      handler() {
        this.queryFormFun();
      },
    },
  },
  created() {
    this.queryFormFun();
  },
  methods: {
    setFormData(formData) {
      for (let key in formData) {
        this.queryForm.props[key] = formData[key];
      }
    },
    getFormData() {
      return this.queryForm.props;
    },

    queryFormFun() {
      let queryForm = {
        configs: this.formConfig.configs,
        attrs: Object.assign({ labelPosition: "top" }, this.formConfig.attrs),
        props: {},
        rules: {},
      };
      Object.keys(this.formConfig["configs"]).forEach(prop => {
        // 这里如果设置了默认项就是默认值，没有就是undefined
        queryForm.props[prop] = this.formConfig["configs"][prop].default;
        queryForm.rules[prop] = this.formConfig["configs"][prop].rules;
      });
      this.queryForm = queryForm;
    },
    dynamicPlaceholder(config) {
      let placeHolderLang = config.placeholder;
      if (placeHolderLang) return this.$t(placeHolderLang);

      if (
        config.tag === "select" ||
        config.tag === "time-picker" ||
        config.tag === "date-picker" ||
        config.tag === "time-select"
      ) {
        placeHolderLang = "lang.rms.fed.pleaseChoose";
      } else {
        placeHolderLang = "lang.rms.fed.pleaseEnter";
      }
      return this.$t(placeHolderLang);
    },
    operationHandler(handler) {
      if (handler === "on-reset") {
        this.$refs.customizeForm.resetFields();

        for (let key in this.queryForm.props) {
          this.queryForm.props[key] = "";
        }
        this.$emit(handler, this.queryForm.props);
      } else {
        this.$emit(handler, this.queryForm.props);
      }
    },
  },
};
</script>

<style lang="less">
.geek-customize-form {
  .el-form-item__label {
    padding-bottom: 0;
    font-size: 13px;
    font-weight: 800;
  }

  .el-date-editor.el-input {
    width: 185px;
  }

  .el-form-item {
    margin-bottom: 6px;
  }

  .operation-button {
    vertical-align: bottom;
  }
}
</style>
