<template>
  <div>
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    </div>
    <!-- 列表信息 -->
    <div class="table-content">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column
          :label="$t('lang.rms.fed.lineNumber')"
          width="50"
          align="center"
          :formatter="formatIndex"
        />
        <!--消息ID-->
        <el-table-column prop="taskId" :label="$t('lang.rms.fed.taskId')" align="center" />
        <!--通道类型-->
        <el-table-column prop="channelId" :label="$t('lang.rms.fed.channelId')" align="center" />
        <!--通道类型-->
        <el-table-column
          prop="channelType"
          :label="$t('lang.rms.fed.channelType')"
          align="center"
        />
        <!-- 客户编码 -->
        <el-table-column prop="clientCode" :label="$t('lang.rms.fed.clientCode')" align="center" />
        <!-- 仓库编码 -->
        <el-table-column
          prop="warehouseCode"
          :label="$t('lang.rms.fed.warehouseCode')"
          align="center"
        />
        <!--   <el-table-column prop="version" :label="$t('版本')" align="center">
        </el-table-column>-->
        <el-table-column prop="msgType" :label="$t('lang.rms.fed.msgType')" align="center" />

        <el-table-column prop="enable" :label="$t('lang.rms.fed.callbackMsgStatus')" align="center">
          <!--          <template slot-scope="scope">{{ statusList[scope.row.status] }}</template>-->
          <template slot-scope="scope">{{ $t(statusList[scope.row.status]) }}</template>
        </el-table-column>
        <el-table-column prop="retryTimes" :label="$t('lang.rms.fed.retryTimes')" align="center" />

        <el-table-column prop="createTime" :label="$t('lang.rms.fed.createTime')" align="center">
          <template slot-scope="scope">{{ setTime(scope.row.createTime) }}</template>
        </el-table-column>

        <el-table-column prop="updateTime" :label="$t('lang.rms.fed.updateTime')" align="center">
          <template slot-scope="scope">{{ setTime(scope.row.updateTime) }}</template>
        </el-table-column>

        <el-table-column fixed="right" :label="$t('lang.rms.fed.operation')" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewMsg(scope.row)">
              {{ $t("lang.rms.fed.textDetails") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pageOption.currentPage"
        :page-size="pageOption.pageSize"
        :page-count="taskCount"
        :page-sizes="[10, 25, 50, 100]"
        layout="sizes, prev, pager, next"
        @size-change="pageSizeChange"
        @current-change="currentPageChange"
      />
    </div>
    <el-dialog
      :title="$t('lang.rms.fed.textDetails')"
      :visible.sync="msgDialog"
      :before-close="closeDialog"
      center
      :append-to-body="true"
      width="50%"
    >
      <el-form label-position="top" label-width="80px" :model="itemData" class="padding_20">
        <el-form-item :label="$t('lang.rms.fed.callbackSuccessChannelIds')">
          <el-input
            v-model="itemData.successChannelIds"
            type="text"
            :rows="2"
            readonly="readonly"
          />
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.errorDesc')">
          <el-input v-model="itemData.errorDesc" type="textarea" :rows="8" readonly="readonly" />
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.msgContent')">
          <el-input v-model="itemData.content" type="textarea" :rows="8" readonly="readonly" />
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "CallbackMsg",
  components: {},
  data() {
    return {
      // 搜索内容
      form: {
        taskId: "",
        channelId: "",
        channelType: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          taskId: {
            label: "lang.rms.fed.taskId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          channelId: {
            label: "lang.rms.fed.channelId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          channelType: {
            label: "lang.rms.fed.channelType",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          // date: {
          //   label: "lang.rms.fed.selectDate",
          //   default: new Date(),
          //   valueFormat: "yyyy-MM-dd",
          //   tag: "date-picker",
          //   placeholder: "lang.rms.fed.pleaseChoose",
          // },
        },
        rules: [],
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      // 设置table的数据
      tableData: [],
      // 当前数据总数
      taskCount: 0,
      pageOption: {
        // 当前页数
        currentPage: 1,
        // 每页展示数
        pagesize: 10,
      },
      itemData: {},
      // 解决刷新页面，国际化不生效的问题
      statusList: {
        0: "lang.rms.fed.callbackMsgStatusSending",
        1: "lang.rms.fed.callbackMsgStatusSuccess",
        2: "lang.rms.fed.callbackMsgStatusFail",
        3: "lang.rms.fed.callbackMsgStatusIgnore",
      },
      isEdit: false,
      // 报文内容弹框
      msgDialog: false,
    };
  },
  computed: {},
  created() {
    this.getTableList();
  },
  methods: {
    /* 格式化内容 */
    formatterTags(row, column, cellValue) {
      if (cellValue) {
        return cellValue.join(",");
      } else {
        return cellValue;
      }
    },

    formatImmediate(row, column, cellValue) {
      const { immediateList } = this;
      for (let index = immediateList.length - 1; index >= 0; index -= 1) {
        if (immediateList[index].key === cellValue + "") {
          return immediateList[index].value;
        }
      }
      return cellValue;
    },
    formatIndex(row, column, cellValue, index) {
      return ((this.pageOption.currentPage - 1) * this.pageOption.pagesize || 0) + index + 1;
    },
    currentPageChange(currentPage) {
      this.pageOption.currentPage = currentPage;
      this.getTableList();
    },
    pageSizeChange(pageSize) {
      this.pageOption.pagesize = pageSize;
      this.getTableList();
    },
    // 编辑
    viewMsg(data) {
      this.itemData = data;
      // this.itemData.content = this.itemData.content//注释掉 不明白为什么这样写
      this.msgDialog = true;
    },
    closeDialog() {
      this.msgDialog = false;
      this.itemData = {};
    },
    setTime(value) {
      if (!value) {
        return null;
      }
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss");
    },
    // 重置搜索参数
    onReset() {
      this.pageOption.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    // 查询
    onQuery(val) {
      this.pageOption.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    getTableList() {
      const data = { language: $utils.Data.getLocalLang() };
      const { taskId, channelId, channelType } = this.form;
      const pageData =
        "?currentPage=" + this.pageOption.currentPage + "&pageSize=" + this.pageOption.pagesize;
      if (taskId) {
        data.taskId = taskId;
      }
      if (channelId) {
        data.channelId = channelId;
      }
      if (channelType) {
        data.channelType = channelType;
      }
      // 回调消息列表
      $req.post("/athena/apiCallback/callbackMsgPageList" + pageData, data).then((data = {}) => {
        const { currentPage = 0, pageSize = 0, pageCount = 0, recordList = [] } = data.data || {};
        this.tableData = recordList.map(item => {
          const descr = this.$t(item.descr);
          console.log(descr);
          return { ...item, descr };
        });
        this.pageOption.pagesize = pageSize;
        this.pageOption.currentPage = currentPage;
        this.taskCount = pageCount;
      });
    },
  },
};
</script>
<style scoped lang="scss">
.form-content {
  border-bottom: 5px solid #eee;
  padding-bottom: 10px;
}

.table-content {
  padding-top: 15px;

  .btn-opt {
    padding: 3px 5px;
    min-height: 10px;
  }
}
:deep(.el-table .el-table__row .el-table__cell div) {
  word-break: break-all;
}
</style>
