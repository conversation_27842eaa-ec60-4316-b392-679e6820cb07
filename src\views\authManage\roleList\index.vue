<template>
  <!-- 用户列表 -->
  <component :is="currentPge" />
</template>

<script>
import { mapState } from "vuex";
import RoleList from "./components/role-list";
import RoleDetail from "./components/role-detail";
export default {
  // name: "RoleList",
  components: {
    RoleList,
    RoleDetail,
  },
  data() {
    return {
      currentPge: "RoleList",
    };
  },
  computed: {
    ...mapState("roleList", ["pageShow"]),
  },
  watch: {
    pageShow(e) {
      this.currentPge = e ? "RoleDetail" : "RoleList";
    },
  },
  methods: {},
};
</script>

<style lang="less" scoped></style>
