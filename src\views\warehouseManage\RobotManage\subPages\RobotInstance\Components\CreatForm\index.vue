<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2021-12-27 14:30:15
 * @Description:
-->
<template>
  <el-form
    ref="createForm"
    :model="modelData"
    class="creat-form"
    label-position="right"
    label-width="120px"
    :disabled="!editProp"
    :rules="rules"
  >
    <div class="form-group-title division">
      {{ $t("lang.rms.api.result.warehouse.baseRobotAttr") }}:
    </div>
    <!-- 机器人ID -->
    <el-form-item :label="$t('lang.rms.fed.listRobotId')" prop="robotId">
      <el-input
        v-model.number="modelData.robotId"
        :placeholder="$t('lang.rms.fed.pleaseEnterTheRobotID')"
        maxlength="8"
      />
    </el-form-item>
    <!-- 机器人别名 -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotAlias')" prop="hostCode">
      <el-input
        v-model="modelData.hostCode"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterRobotAlias')"
      />
    </el-form-item>
    <!-- 机器人型号 -->
    <el-form-item :label="$t('lang.rms.api.result.warehouse.robotModel')" prop="robotModelId">
      <el-select v-model="modelData.robotModelId" :placeholder="$t('lang.rms.fed.choose')">
        <el-option
          v-for="item in modalList"
          :key="item.id"
          :label="item.product"
          :value="item.robotModelId"
        />
      </el-select>
    </el-form-item>
    <!-- 支持的sizeType -->
    <!-- <el-form-item :label="$t('lang.rms.api.result.warehouse.supportedSizeType')" prop="sizeTypes">
      <el-select
        v-model="modelData.sizeTypes"
        filterable
        multiple
        allow-create
        default-first-option
        :placeholder="$t('lang.rms.fed.choose')"
        @change="sizeTypesChange(modelData.sizeTypes)"
      >
        <el-option
          v-for="item in sizeTypeDict"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
      <div v-if="sizeTypeParamTip" class="error-tip">{{ sizeTypeParamTip }}</div>
    </el-form-item> -->
    <!-- 生产批次 -->
    <el-form-item
      :label="$t('lang.rms.api.result.warehouse.productionBatch')"
      prop="productionBatch"
    >
      <el-input
        v-model="modelData.productionBatch"
        :placeholder="$t('lang.rms.api.result.warehouse.pleaseEnterRobotBatch')"
      />
    </el-form-item>
  </el-form>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import robotManageRequest from "@/api/robotManage";
import { mapState } from "vuex";

export default {
  name: "ModelCreateForm",
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    slopProps: {
      type: Object,
      default() {
        return {};
      },
    },
    editProp: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    // 这里存放数据
    return {
      sizeTypeParamTip: null,
      modelData: { ...this.slopProps },
      modalList: [],
      rules: {
        robotModelId: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.enterRobotModel"),
            trigger: "blur",
          },
        ],
        robotId: [
          {
            required: true,
            message: this.$t("lang.rms.api.result.warehouse.pleaseEnterRobotIdAndMustNo"),
            trigger: "blur",
            type: "number",
          },
        ],
        hostCode: [
          // {
          //   required: true,
          //   message: this.$t("lang.rms.api.result.warehouse.pleaseEnterRobotAlias"),
          //   trigger: "blur",
          // },
          { min: 1, max: 15, message: "长度在 1 到 15 个字符", trigger: "blur" },
        ],
        productionBatch: [
          // {
          //   required: true,
          //   message: this.$t("lang.rms.api.result.warehouse.pleaseEnterRobotBatch"),
          //   trigger: "blur",
          // },
          { min: 1, max: 15, message: "长度在 1 到 15 个字符", trigger: "blur" },
        ],
      },
    };
  },
  // 监听属性 类似于data概念
  computed: {
    ...mapState("containerModal", ["sizeTypeDict"]),
  },
  // 监控data中的数据变化
  watch: {
    slopProps(newObj) {
      newObj.robotId = newObj.robotId ? newObj.robotId / 1 : "";
      this.modelData = { ...newObj };
    },
    // 'modelData.sizeTypes'(val) {
    //   if (val) this.sizeTypesChange(val)
    // },
  },
  mounted() {
    this.getModalList();
  },
  // 方法集合
  methods: {
    getModalList() {
      robotManageRequest
        .getRobotModalPageList(
          {
            product: "",
            displayName: "",
            actualProduct: "",
            businessModelId: "",
            protocolModelId: "",
            mechanismModelId: "",
          },
          {
            currentPage: 1,
            pageSize: 1000,
          },
        )
        .then(({ data }) => {
          this.modalList = data.recordList;
        });
    },
    getFormValues() {
      const $form = this.$refs["createForm"];
      return $form.validate();
    },
    resetFormValues() {
      this.$refs["createForm"].resetFields();
    },
    // sizeTypesChange(data) {
    //   if (data.length === 0) {
    //     this.sizeTypeParamTip = null
    //     return
    //   }
    //   const reg = /^(?!\d)[a-zA-Z0-9]*$/
    //   if (data) {
    //     data.forEach((item) => {
    //       if (reg.test(item)) {
    //         this.sizeTypeParamTip = null
    //       } else {
    //         this.sizeTypeParamTip = this.$t('lang.venus.web.check.canNotStartWithNum')
    //       }
    //     })
    //   }
    // }
  },
};
</script>
<style lang="scss" scoped>
.creat-form .el-input,
.creat-form .el-select {
  width: 100%;
}
.form-group-title {
  font-size: 16px;
  font-weight: bold;
  margin: 3px 0 20px;
  text-align: left;
}
.division {
  margin-bottom: 20px;
}
.error-tip {
  position: absolute;
  top: 100%;
  color: red;
  font-size: 12px;
  line-height: 1;
  margin-top: 2px;
}
</style>
