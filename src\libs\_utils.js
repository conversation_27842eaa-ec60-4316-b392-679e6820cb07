/* ! <AUTHOR> at 2021/01 */

import Data from "./data";
import Type from "./type";
import Tools from "./tools";

export default {
  // 标记需初始化的内容或参数判断flag
  _initStatus: {
    config: true, // 标记是否需要init系统config
    menuList: true, // 标记是否需要init系统MenuList
  },
  _staticConfig: null,
  _sessionId: null,
  _sessionIframePath: null,

  _$map2dIframe: null,
  _$mapEditIframe: null,
  /**
   * 给iframe通信
   * @param {*} name：monitor2D | monitor3D | singleEdit2D
   * @param {*} msgType
   * @param {*} data: {msgType:"",data:xxxx}
   * @returns
   */
  postMessage(name, data) {
    const routeName = ["singleMap2D", "singleEdit2D", "robotControl"];
    const currentRouteName = $app?.$route.name;
    if (!routeName.includes(currentRouteName)) return;
    let $iframe;
    switch (name) {
      case "monitor2D":
        $iframe = $utils._$map2dIframe;
        if (!$iframe) {
          $iframe = document.getElementById("J_Monitor2D");
          $utils._$map2dIframe = $iframe;
        }
        break;
      case "singleEdit2D":
        $iframe = $utils._$mapEditIframe;
        if (!$iframe) {
          $iframe = document.getElementById("J_Edit2D");
          $utils._$mapEditIframe = $iframe;
        }
        break;
    }

    if (!$iframe?.contentWindow) return;

    const contentWindow = $iframe.contentWindow;
    if (contentWindow.onload) {
      contentWindow.postMessage(data, window.location.origin);
    } else {
      contentWindow.onload = () => {
        contentWindow.postMessage(data, window.location.origin);
      };
    }
  },
  destroyIframe() {
    $utils._$map2dIframe = null;
    $utils._$mapEditIframe = null;
  },

  Data,
  Type,
  Tools,
};
