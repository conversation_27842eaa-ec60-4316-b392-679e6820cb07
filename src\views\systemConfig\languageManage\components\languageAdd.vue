<template>
  <div class="ui-language-add">
    <div>
      {{ $t("lang.rms.fed.upload") + $t("lang.rms.fed.languagePackage") }}
    </div>
    <el-form ref="languageForm" label-width="150px" class="ui-languageform">
      <el-form-item :label="$t('lang.rms.fed.languagePackageFile')" prop="pass">
        <el-upload
          ref="languageUpload"
          class="language-upload"
          action=""
          accept=".xlsx"
          :auto-upload="false"
          :on-change="handleChange"
          :on-remove="handleRemove"
          :http-request="uploadLanguage"
          :file-list="fileList"
        >
          <el-button size="small" type="primary">{{ $t("lang.rms.fed.upload") }}</el-button>
          <!-- <div slot="tip" class="el-upload__tip">{{ $t("请上传xlsx文件") }}</div>   -->
        </el-upload>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.languageType')" prop="checkPass">
        <el-select v-model="languageCode">
          <el-option
            v-for="item in languageOption"
            :key="item.languageCode"
            :value="item.languageCode"
            :label="item.languageName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="ui-language-addBtn">
        <el-button @click="cancel()">
          {{ $t("lang.rms.fed.cancel") }}
        </el-button>
        <el-button
          class="confirm_btn"
          type="primary"
          :loading="uploading"
          :disabled="fileList.length === 0 || !languageCode"
          @click="submit()"
        >
          {{ $t("lang.rms.fed.confirm") }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
/**
 * lang.rms.fed.languagePackageFile:"语言包文件"
 * lang.rms.fed.languageType:"语言类型"
 */

export default {
  name: "LanguageAdd",
  props: {
    languageOption: Array,
  },
  data() {
    return {
      languageCode: "",
      fileList: [],
      uploading: false,
    };
  },
  methods: {
    submit() {
      this.uploading = true;
      this.$refs.languageUpload.submit();
    },
    clear() {
      this.languageCode = "";
      this.fileList = [];
    },
    uploadLanguage(params) {
      const formData = new FormData();
      formData.append("file", params.file);
      formData.append("languageCode", this.languageCode);

      $req
        .post("/athena/api/coreresource/i18n/importI18nItem", formData)
        .then(res => {
          this.uploading = false;
          if (res.code === 0) {
            this.$success(this.$t(res.msg));
            this.setLocalLang({ apiUrl: this.languageCode });
            this.clear();
          } else {
            this.$error(this.$t(res.msg));
          }
        })
        .catch(e => {
          this.uploading = false;
        });
    },
    handleChange(file, fileList) {
      this.fileList = [file];
    },
    handleRemove() {
      this.fileList = [];
    },
    setLocalLang(lang) {
      const { Data } = $utils;
      const { apiUrl } = lang;
      let localLang = Data.getLocalLang();
      if (apiUrl === localLang) {
        Data.setI18nMessage($app.$i18n, apiUrl, true);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.ui-language-add {
  padding: 15px;
  box-shadow: 0px -5px 10px -5px rgb(0 0 0 / 10%);
}
.ui-languageform {
  padding-left: 80px;
  margin-top: 30px;
}
.ui-language-addBtn {
  margin-top: 30px;
  .confirm_btn {
    width: 100px;
  }
}
</style>
