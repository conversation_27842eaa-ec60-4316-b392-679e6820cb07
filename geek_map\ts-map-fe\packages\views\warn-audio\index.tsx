/* ! <AUTHOR> at 2022/08/31 */
import { useState, useEffect, useRef, createRef, RefObject } from "react";
import { Button, Popover } from "antd";
import type { SizeType } from "antd/es/config-provider/SizeContext";
import { useTranslation } from "react-i18next";
import { $eventBus } from "../../singleton";

import voiceOff from "../../imgs/mapWarnAudio_images/VoiceOff.png";
import voiceOn from "../../imgs/mapWarnAudio_images/VoiceOn.png";
import voice from "../../imgs/mapWarnAudio_audio/music.mp3";

type PropsType = {
  cardMsgId: any;
};

function WarnAudio(props: PropsType) {
  const { t } = useTranslation();
  const [isPlay, setIsPlay] = useState(true); //是否正在播放
  const [audioIcon, setAudioIcon] = useState(""); //播放暂停图片
  const [open, setOpen] = useState(false);
  const [visible, setVisible] = useState(false);
  const [size, setSize] = useState<SizeType>("small"); // default is 'middle'

  const audioMusic = useRef();

  let timer: any = useRef(); //解决react 没办法clearInterval的问题
  let queryFaultMessageTimer: any = useRef();
  // 数据 驱动语音事件绑定
  useEffect(() => {
    const dataId = props.cardMsgId ? props.cardMsgId.toString() : "";
    console.log("---   dataId ---", dataId);

    if (dataId) {
      if (timer.current) {
        playEnd();
      }

      playStart(dataId);
    }
    // else {
    //   playEnd();
    // }

    return () => {
      clearInterval(timer.current);
    };
  }, [props.cardMsgId]);

  const playAudio = () => {
    if (isPlay) {
      const aa: any = audioMusic.current;
      aa.play();
      timer.current = setInterval(() => {
        const aa: any = audioMusic.current;
        aa.play();
      }, 3000);
    }
  };

  const playStart = (id: string | number) => {
    setVisible(true);
    setIsPlay(true);
    setAudioIcon(voiceOff);

    playAudio();
    queryFaultMessage(id);
  };

  const playEnd = () => {
    resetTimer();
  };

  const resetTimer = () => {
    setAudioIcon("");
    setIsPlay(false);
    setVisible(false);// 这里会让小喇叭消失

    timer.current && clearInterval(timer.current);
    const aa: any = audioMusic.current;

    if (aa) {
      aa.pause(); // z
      aa.load();
    }
    queryFaultMessageTimer.current && clearInterval(queryFaultMessageTimer.current);
  };

  // 查询地图异常信息是否已经恢复正常
  const queryFaultMessage = (id: string | number) => {
    _$utils.reqGet("/athena/fault/getFaultMessage?id=" + id).then(res => {
      console.log("res点击已读的数据=====", res);
      console.log("res点击已读的数据code=====", res.code);
      console.log("res点击已读的数据data.faultStatus=====", res.data.faultStatus);
      if (!res.code && res.data.faultStatus === 1) {
        resetTimer();
      }
    });
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
  };

  const cancelClick = () => {
    setOpen(false);
  };

  const confirmClick = () => {
    setOpen(false);

    if (isPlay) {
      playEnd();
    }
  };

  return (
    <section className="map2d-audio-icon">
      <Popover
        title=""
        trigger="click"
        open={open}
        onOpenChange={handleOpenChange}
        content={
          <div className="ui-popover-bottom">
            <p>{t("lang.rms.fed.isStopAudio")}</p>
            <Button type="text" size={size} onClick={cancelClick}>
              {t("lang.common.cancel")}
            </Button>
            <Button type="primary" size={size} onClick={confirmClick}>
              {t("lang.rms.fed.confirm")}
            </Button>
          </div>
        }
      >
        {visible && <img src={audioIcon} className="audio-icon" />}
      </Popover>
      <audio ref={audioMusic} src={voice}></audio>
    </section>
  );
}

export default WarnAudio;
