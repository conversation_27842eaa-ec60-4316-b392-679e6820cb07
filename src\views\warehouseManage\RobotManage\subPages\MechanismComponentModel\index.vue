<template>
  <div>
    <SearchWrap>
      <SearchForm @onsubmit="onSubmit" />
    </SearchWrap>
    <div style="text-align: right">
      <CreateDialog
        :prop-show="showCreatDialog"
        :edit-prop="editCreatDialog"
        :button-text="$t('lang.rms.api.result.warehouse.robotMechanismCompentModel')"
        :title-text="$t(creatDialogTitle)"
        @createconfirm="createConfirm"
        @createcancel="createCancel"
      >
        <CreatForm ref="createForm" :slop-props="slopProps" :edit-prop="editCreatDialog" />
      </CreateDialog>
    </div>
    <el-table :loading="loading" :data="recordList" style="width: 100%">
      <!-- <el-table-column
        :label="$t('lang.rms.api.result.warehouse.mechanismComponentNo')"
        prop="componentCode"
        min-width="200"
      /> -->
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.mechanism.component.spuName')"
        prop="name"
        min-width="200"
      />
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.affiliatedMechanism')"
        prop="mechanismModelId"
        min-width="200"
      >
        <template slot-scope="scope">
          <span>{{ `${scope.row.mechanismCode}(${scope.row.mechanismModelId})` }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.lengthWidthHeight')"
        prop="length"
        min-width="200"
      >
        <template slot-scope="scope">
          <span>{{
            `${scope.row.length || "0"}/${scope.row.width || "0"}/${scope.row.beginHeight || "0"}`
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.mechainsmComponentSn')"
        prop="sn"
        min-width="120"
      >
      </el-table-column>
      <el-table-column
        fixed="right"
        :label="$t('lang.rms.fed.textOperation')"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="checkListItemInfo(scope.row)">
            {{ $t("lang.rms.fed.buttonView") }}
          </el-button>
          <!-- <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="copyListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.web.map.version.copy") }}
          </el-button> -->
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="editListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.fed.buttonEdit") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="deleteListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.fed.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="text-align: right; margin-top: 30px">
      <el-pagination
        background
        layout="total, prev, pager, next, sizes, jumper"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="paginationParams.total"
        :page-size="paginationParams.pageSize"
        :current-page="paginationParams.currentPage"
        @current-change="paginationChange"
        @size-change="paginationPageChange"
      />
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import SearchWrap from "../../Components/SearchWrap";
import CreateDialog from "../../Components/CreateDialog";
import CreatForm from "./Components/CreatForm";
import SearchForm from "./Components/SearchForm";
import robotManageRequest from "@/api/robotManage";

export default {
  name: "MechanismComponentModel",
  components: {
    CreatForm,
    SearchWrap,
    CreateDialog,
    SearchForm,
  },
  data() {
    return {
      slopProps: {},
      showCreatDialog: false,
      editCreatDialog: true,
      creatDialogTitle: "lang.rms.api.result.warehouse.createRobotCompentModel",
      copyDataOpen: false,
      loading: false,
      recordList: [],
      paginationParams: { pageSize: 20, currentPage: 1, total: 0 },
      searchFormData: {},
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  watch: {},
  methods: {
    onSubmit(searchFormData) {
      const { paginationParams } = this;
      this.searchFormData = searchFormData;

      this.reqTableList(searchFormData, paginationParams);
    },
    reqTableList(searchFormData, paginationParams) {
      this.loading = true;
      robotManageRequest
        .getMechanismComponentPageList(searchFormData, paginationParams)
        .then(({ data }) => {
          const { pageSize, currentPage, recordList, recordCount } = data;
          this.recordList = recordList;
          this.paginationParams = {
            pageSize,
            currentPage,
            total: recordCount,
          };
          this.loading = false;
        });
    },
    reReqTableList() {
      this.paginationParams.currentPage = 1;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationChange(currentPage) {
      this.paginationParams.currentPage = currentPage;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationPageChange(pageSize) {
      this.paginationParams.pageSize = pageSize;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    createConfirm() {
      if (this.editCreatDialog) {
        this.showCreatDialog = true;
        const $createForm = this.$refs["createForm"];
        $createForm.getFormValues().then(val => {
          if (val) {
            const { modelData } = $createForm;
            const paramsObj = { ...modelData };
            if (!this.slopProps.id || this.copyDataOpen) {
              delete paramsObj.id;
            }
            robotManageRequest.addEditMechanismComponentItem(paramsObj, {}).then(({ code }) => {
              if (+code === 0) {
                console.log("添加/修改成功！");
              }
              this.createCancel();
              this.reReqTableList();
              this.confirmNext();
            });
          }
        });
      }
    },
    async confirmNext() {
      try {
        await this.$confirm(
          this.$t("lang.rms.fed.gotoSomePage", [
            this.$t("lang.rms.api.result.warehouse.businessModel"),
          ]),
        );
        this.$emit("goNextPage");
      } catch (e) {}
    },
    createCancel() {
      this.$refs["createForm"].resetFormValues();
      this.slopProps = {};
      this.editCreatDialog = true;
      this.showCreatDialog = false;
      this.copyDataOpen = false;
      this.creatDialogTitle = "lang.rms.api.result.warehouse.createRobotCompentModel";
    },
    openDialogInSomeType(edit, data) {
      const { id, name, mechanismModelId, length, width, beginHeight, sn } = data;
      this.slopProps = { id, name, mechanismModelId, length, width, beginHeight, sn };
      this.editCreatDialog = edit;
      this.showCreatDialog = true;
      this.creatDialogTitle = edit
        ? "lang.rms.api.result.warehouse.editRobotCompoentModel"
        : "lang.rms.api.result.warehouse.viewRobotCompoentModel";
    },
    // 查看
    checkListItemInfo(robotData) {
      robotManageRequest
        .getMechanismComponentDetailInfo({}, { id: robotData.id })
        .then(({ data }) => {
          this.openDialogInSomeType(false, data);
        });
    },
    // 复制
    copyListItemInfo(robotData) {
      robotManageRequest
        .getMechanismComponentDetailInfo({}, { id: robotData.id })
        .then(({ data }) => {
          this.copyDataOpen = true;
          this.openDialogInSomeType(true, data);
        });
    },
    // 编辑
    editListItemInfo(robotData) {
      robotManageRequest
        .getMechanismComponentDetailInfo({}, { id: robotData.id })
        .then(({ data }) => {
          this.openDialogInSomeType(true, data);
        });
    },
    // 删除
    deleteListItemInfo(robotData) {
      this.$confirm(
        this.$t("lang.rms.api.result.warehouse.willDeleteToContinue"),
        this.$t("lang.rms.fed.prompt"),
        {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.rms.fed.cancel"),
          type: "warning",
        },
      )
        .then(() => {
          robotManageRequest
            .deleteMechanismComponentItem({}, { id: robotData.id })
            .then(({ code }) => {
              if (+code === 0) {
                this.$message({
                  type: "success",
                  message: this.$t("lang.venus.web.common.successfullyDeleted"),
                });
              }
              this.reReqTableList();
            });
        })
        .catch(() => null);
    },
  },
};
</script>
<style lang="scss" scoped>
.align-bottom {
  vertical-align: bottom;
}
</style>
