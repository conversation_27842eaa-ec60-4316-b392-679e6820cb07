<template>
  <div>
    <SearchWrap>
      <SearchForm @onsubmit="onSubmit" />
    </SearchWrap>
    <div style="text-align: right">
      <CreateDialog
        :prop-show="showCreatDialog"
        :edit-prop="editCreatDialog"
        :button-text="$t('lang.rms.api.result.warehouse.robotExample')"
        :title-text="$t(creatDialogTitle)"
        @showTrueFalse="handleOpenDialog"
        @createconfirm="createConfirm"
        @createcancel="createCancel"
      >
        <CreatForm ref="createForm" :slop-props="slopProps" :edit-prop="editCreatDialog" />
      </CreateDialog>
    </div>
    <el-table :loading="loading" :data="recordList" style="width: 100%">
      <!-- 机器人id -->
      <el-table-column prop="robotId" :label="$t('lang.mb.robotManage.robotId')" min-width="120" />
      <!-- 机器人别名 -->
      <el-table-column
        prop="hostCode"
        :label="$t('lang.rms.api.result.warehouse.robotAlias')"
        min-width="120"
      />
      <!-- 机器人型号 -->
      <el-table-column
        prop="robotModelEntity"
        :label="$t('lang.rms.api.result.warehouse.robotModel')"
        min-width="120"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.robotModelEntity?.product }}</span>
        </template>
      </el-table-column>
      <!-- 机器人管理状态 -->
      <el-table-column
        prop="manageStatus"
        :label="$t('lang.rms.api.result.warehouse.robotManageStatus')"
        min-width="160"
      >
        <template slot-scope="scope">
          <span>{{ manageStatusDesc[scope.row.manageStatus] }}</span>
        </template>
      </el-table-column>
      <!-- 机器人工作状态 -->
      <el-table-column
        prop="workStatus"
        :label="$t('lang.rms.api.result.warehouse.robotWorkStatus')"
        min-width="160"
      >
        <template slot-scope="scope">
          <span>{{ workStatusDesc[scope.row.workStatus] }}</span>
        </template>
      </el-table-column>
      <!-- 锁定状态 -->
      <el-table-column
        prop="controlStatus"
        :label="$t('lang.rms.api.result.warehouse.lockedStatus')"
        min-width="160"
      >
        <template slot-scope="scope">
          <span>{{ controlStatusDesc[scope.row.controlStatus] }}</span>
        </template>
      </el-table-column>
      <!-- 连接状态 -->
      <el-table-column
        prop="commStatus"
        :label="$t('lang.rms.api.result.warehouse.connectionStatus')"
        min-width="160"
      >
        <template slot-scope="scope">
          <span>{{ commStatusDesc[scope.row.commStatus] }}</span>
        </template>
      </el-table-column>
      <!-- 机器人固件版本 -->
      <el-table-column
        prop="firmwareVersion"
        :label="$t('lang.rms.api.result.warehouse.robotFirmwareVersion')"
        min-width="120"
      />
      <!-- <el-table-column label="sizeType" min-width="200">
        <template slot-scope="scope">
          <el-tag
            v-for="item in scope.row.sizeTypes"
            :key="item"
            type="info"
            style="margin-right: 10px"
            >{{ item }}</el-tag
          >
        </template>
      </el-table-column> -->
      <!-- 生产批次 -->
      <el-table-column
        prop="productionBatch"
        :label="$t('lang.rms.api.result.warehouse.productionBatch')"
        min-width="120"
      />
      <!-- 上次充电时间 -->
      <el-table-column
        :label="$t('lang.rms.api.result.warehouse.lastChargingTime')"
        min-width="160"
      >
        <template slot-scope="scope">
          <span>{{ timeFormat(scope.row.lastChargeTime) }}</span>
        </template>
      </el-table-column>
      <!-- 上电时间 -->
      <el-table-column :label="$t('lang.rms.api.result.warehouse.powerOnTime')" min-width="160">
        <template slot-scope="scope">
          <span>{{ timeFormat(scope.row.uptime) }}</span>
        </template>
      </el-table-column>
      <!-- 掉线时间 -->
      <el-table-column :label="$t('lang.rms.api.result.warehouse.offTime')" min-width="160">
        <template slot-scope="scope">
          <span>{{ timeFormat(scope.row.lastCommTime) }}</span>
        </template>
      </el-table-column>
      <!-- 操作 -->
      <el-table-column
        :label="$t('lang.mb.robotManage.operate')"
        width="160"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="checkListItemInfo(scope.row)">
            {{ $t("lang.rms.fed.buttonView") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="editListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.fed.buttonEdit") }}
          </el-button>
          <el-button
            v-if="!isRoleGuest"
            type="text"
            size="small"
            @click="deleteListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.fed.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="text-align: right; margin-top: 30px">
      <el-pagination
        background
        layout="total, prev, pager, next, sizes, jumper"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="paginationParams.total"
        :page-size="paginationParams.pageSize"
        :current-page="paginationParams.currentPage"
        @current-change="paginationChange"
        @size-change="paginationPageChange"
      />
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import SearchWrap from "../../Components/SearchWrap";
import CreateDialog from "../../Components/CreateDialog";
import CreatForm from "./Components/CreatForm";
import SearchForm from "./Components/SearchForm";
import robotManageRequest from "@/api/robotManage";
import { mapActions } from "vuex";

export default {
  name: "RobotInstance",
  components: {
    CreatForm,
    SearchWrap,
    CreateDialog,
    SearchForm,
  },
  data() {
    return {
      slopProps: {},
      showCreatDialog: false,
      editCreatDialog: true,
      creatDialogTitle: "lang.rms.api.result.warehouse.createRobotModelInstance",
      loading: false,
      recordList: [],
      paginationParams: { pageSize: 20, currentPage: 1, total: 0 },
      searchFormData: {},
      // 机器人管理状态码
      manageStatusDesc: {
        // all: this.$t('lang.rms.fed.whole'),
        UNREGISTERED: this.$t("lang.rms.api.result.warehouse.unRegister"),
        REGISTERED: this.$t("lang.rms.api.result.warehouse.register"),
        BLOCKED: this.$t("lang.rms.api.result.warehouse.stop"),
      },
      // 机器人工作状态码
      workStatusDesc: {
        // all: this.$t('lang.rms.fed.whole'),
        NORMAL: this.$t("lang.rms.api.result.warehouse.normalPresence"),
        SLEEPING: this.$t("lang.rms.api.result.warehouse.sleep"),
        REMOVE_FROM_SYSTEM: this.$t("lang.rms.api.result.warehouse.departure"),
      },
      // 锁定状态码
      controlStatusDesc: {
        // all: this.$t('lang.rms.fed.whole'),
        NORMAL: this.$t("lang.rms.api.result.warehouse.normal"),
        STOP: this.$t("lang.rms.api.result.warehouse.robotStop"),
        SUSPEND: this.$t("lang.rms.api.result.warehouse.robotStopAndStopTask"),
        LOCK: this.$t("lang.rms.api.result.warehouse.robotLock"),
      },
      // 连接状态码
      commStatusDesc: {
        // all: this.$t('lang.rms.fed.whole'),
        NORMAL: this.$t("lang.venus.common.dict.onlineStatus.online"),
        OFFLINE: this.$t("lang.venus.common.dict.onlineStatus.dropLine"),
        DISCONNECTED: this.$t("lang.venus.common.dict.onlineStatus.reconecting"),
      },
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  watch: {},
  methods: {
    ...mapActions("containerModal", ["findDistinctSizeType"]),
    handleOpenDialog() {
      // this.findDistinctSizeType();
      if (this.$refs?.createForm) {
        this.$refs.createForm.getModalList();
      }
    },
    timeFormat(v) {
      if (v) {
        return $utils.Tools.formatDate(v, "yyyy-MM-dd hh:mm:ss");
      }
      return "";
    },
    onSubmit(searchFormData) {
      console.log(searchFormData);
      const { paginationParams } = this;
      this.searchFormData = searchFormData;

      this.reqTableList(searchFormData, paginationParams);
    },
    reqTableList(searchFormData, paginationParams) {
      this.loading = true;
      robotManageRequest.getRobotPageList(searchFormData, paginationParams).then(({ data }) => {
        const { pageSize, currentPage, recordList, recordCount } = data;
        this.recordList = recordList;
        this.paginationParams = {
          pageSize,
          currentPage,
          total: recordCount,
        };
        this.loading = false;
      });
    },
    reReqTableList() {
      this.paginationParams.currentPage = 1;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationChange(currentPage) {
      this.paginationParams.currentPage = currentPage;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    paginationPageChange(pageSize) {
      this.paginationParams.pageSize = pageSize;
      this.reqTableList(this.searchFormData, this.paginationParams);
    },
    createConfirm() {
      if (this.editCreatDialog) {
        this.showCreatDialog = true;
        const $createForm = this.$refs["createForm"];
        $createForm
          .getFormValues()
          .then(val => {
            if (val) {
              const { modelData } = $createForm;
              const paramsObj = { ...modelData };
              if (!this.slopProps.id || this.copyDataOpen) {
                delete paramsObj.id;
              }
              // if ($createForm.sizeTypeParamTip) {
              //   return
              // }

              robotManageRequest.addEditRobotItem(paramsObj, {}).then(({ code }) => {
                if (+code === 0) {
                  console.log("添加/修改成功！");
                }
                this.reReqTableList();
                this.createCancel();
              });
            }
          })
          .catch(e => console.log(e));
      }
    },
    createCancel() {
      this.$refs["createForm"].resetFormValues();
      this.slopProps = {};
      this.editCreatDialog = true;
      this.showCreatDialog = false;
      this.creatDialogTitle = "lang.rms.api.result.warehouse.createRobotModelInstance";
    },
    openDialogInSomeType(edit, data) {
      // this.findDistinctSizeType();
      const { id, robotModelId, robotId, hostCode, productionBatch } = data;
      // const newsizeTypes = sizeTypes && sizeTypes.length ? sizeTypes.split(',') : []
      this.slopProps = { id, robotModelId, robotId, hostCode, productionBatch };
      this.editCreatDialog = edit;
      this.showCreatDialog = true;
      this.creatDialogTitle = edit
        ? "lang.rms.api.result.warehouse.editRobotModelInstance"
        : "lang.rms.api.result.warehouse.viewRobotModelInstance";
    },
    // 查看
    checkListItemInfo(robotData) {
      robotManageRequest.getRobotDetailInfo({}, { id: robotData.id }).then(({ data }) => {
        this.openDialogInSomeType(false, data);
      });
    },
    // 编辑
    editListItemInfo(robotData) {
      robotManageRequest.getRobotDetailInfo({}, { id: robotData.id }).then(({ data }) => {
        this.openDialogInSomeType(true, data);
      });
    },
    // 删除
    deleteListItemInfo(robotData) {
      this.$confirm(
        this.$t("lang.rms.api.result.warehouse.willDeleteToContinue"),
        this.$t("lang.rms.fed.prompt"),
        {
          confirmButtonText: this.$t("lang.rms.fed.confirm"),
          cancelButtonText: this.$t("lang.rms.fed.cancel"),
          type: "warning",
        },
      )
        .then(() => {
          robotManageRequest.delRobotItem({}, { id: robotData.id }).then(({ code }) => {
            if (+code === 0) {
              this.$message({
                type: "success",
                message: this.$t("lang.venus.web.common.successfullyDeleted"),
              });
            }
            this.reReqTableList();
          });
        })
        .catch(() => null);
    },
  },
};
</script>
<style lang="scss" scoped>
.align-bottom {
  vertical-align: bottom;
}
</style>
