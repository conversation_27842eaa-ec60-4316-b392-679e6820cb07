import {getGlobalViewport} from "../global";
import CtrlPoint from '../element/baseElement/CtrlPoint';
import Line from '../element/baseElement/Line'
import Polygon from '../element/baseElement/Polygon'
import {Graphics} from "pixi.js";
import Mode from '../Mode'
import LayerManager from '../layerManager/LayerManager'
import {pixi2cad,createId} from "../utils/utils";
import EventBus from '../eventBus/EventBus'
export default class AreaEvent {
  static addEvents() {
    const {options:{name,isRect = false}} = Mode.mode
    const vp = getGlobalViewport()
    const operateLayerInstance = LayerManager.get('OPERATE')
    const $operateLayer = operateLayerInstance.container
    const $line = new Graphics()
    const $polygon = new Graphics()
    const $container = new Graphics();
    let paths = []
    let events;
    $container.addChild($line)
    $container.addChild($polygon)
    $operateLayer.addChild($container)
    //渲染面
    const renderPolygon = (paths) => {
      //渲染线
      $line.clear()
      Line.render($line,paths)
      $polygon.clear()
      Polygon.render($polygon,paths)
    }
    //绘制完成
    const finished = () => {
      if(paths.length < 3){
        $operateLayer.removeChildren()
        Mode.resetMode()
        paths = []
        return
      }
      //将pixi坐标转化为cad坐标
      const cadPaths = paths.map(p => {
        const location = pixi2cad(p)
        return location
      })
      const addOp = {
        id: 'AREA',
        data:[{id:createId(),controlPoints:cadPaths,areaType:name,isRect}]
      }
      $operateLayer.removeChildren()
      Mode.resetMode()
      LayerManager.addElements(addOp)
      paths = []
    }
    //绘制矩形面
    const rectEvents = {
      clicked:e => {
        const {x,y} = e.world
        const $point = CtrlPoint.render(x,y)
        $container.addChild($point)
        paths.push({x,y})
        renderPolygon(paths)
        if(paths.length === 2){
          const {x:x1,y:y1} = paths[0]
          const {x:x2,y:y2} = paths[1]
          paths = [paths[0],{x:x2,y:y1},paths[1],{x:x1,y:y2}]
          finished()
        }
      },
      mousemove:e => {
        const moveP = e.data.getLocalPosition(vp);
        if(!paths.length) return
        const {x:x1,y:y1} = paths[0]
        const {x:mx,y:my} = moveP
        const movePaths = [paths[0],{x:mx,y:y1},moveP,{x:x1,y:my},paths[0]]
        renderPolygon(movePaths)
      },
      keydown:e => {
        const {key} = e
        if(key === 'Escape'){
          finished()
          EventBus.$emit('keydown:Escape')
        }
      }
    }
    //绘制多边形面
    const polygonEvents = {
      clicked:e => {
        const {x,y} = e.world
        const $point = CtrlPoint.render(x,y)
        $container.addChild($point)
        paths.push({x,y})
        renderPolygon(paths)
      },
      mousemove:e => {
        const moveP = e.data.getLocalPosition(vp);
        if(!paths.length) return
        const movePaths = [...paths,moveP,paths[0]]
        renderPolygon(movePaths)
      },
      keydown:e => {
        const {key} = e
        if(key === 'Escape'){
          EventBus.$emit('keydown:Escape')
          finished()
        }
      }
    }
    events = isRect ? rectEvents : polygonEvents
    return events
  }
}
