<template>
  <div class="edit-user-dialog">
    <!-- 编辑用户 -->
    <el-dialog
      :title="$t('lang.rms.fed.buttonEdit')"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      @closed="onClose"
    >
      <el-form ref="rulesForm" :model="formObj" :rules="rulesObj">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.inputUserName')" prop="userName">
              <el-input
                v-model="formObj.userName"
                :disabled="true"
                :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.inputFullName')" prop="realName">
              <el-input
                v-model="formObj.realName"
                :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.inputTelephone')" prop="phone">
              <el-input
                v-model="formObj.phone"
                :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.role')" prop="roleIds">
              <el-select
                v-model="formObj.roleIds"
                :placeholder="$t('lang.rms.fed.pleaseChoose')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in roleList"
                  :key="item.roleId"
                  :label="item.name"
                  :value="item.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="onClose">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.save") }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formObj: {
        userName: "", // 用户名
        userId: "", // 修改id
        realName: "", // 姓名
        phone: "", // 电话
        roleIds: "", // 角色
      },
      loading: false,
      roleList: [],
      rulesObj: {
        userName: [{ required: true, message: "不能为空, 请输入", trigger: "change" }],
        realName: [{ required: true, message: "不能为空, 请输入", trigger: "change" }],
        // phone: [{ required: true, message: "不能为空, 请输入", trigger: "change" }],
        roleIds: [{ required: true, message: "不能为空, 请选择", trigger: "change" }],
      },
      dialogVisible: true,
    };
  },
  computed: {
    isEdit() {
      let currentUsername = $utils.Data.getUserInfo() 
      return currentUsername === this.formObj.userName
    }
  },
  created() {},
  methods: {
    setUserInfo(userInfo) {
      this.formObj = {
        userName: userInfo.userName, // 用户名
        userId: userInfo.userId, // 修改id
        realName: userInfo.realName, // 姓名
        phone: userInfo.phone ? userInfo.phone : "", // 电话
        roleNames: userInfo.roleNames,
        roleIds: "", // 角色
      };
      this.getRoleList();
      console.log(userInfo);
    },
    getRoleList() {
      $req.get("/athena/api/coreresource/auth/role/pageQuery/v1").then(res => {
        if (res.code === 0) {
          res.data.recordList.forEach(item => {
            if (item.name == this.formObj.roleNames) {
              this.formObj.roleIds = item.roleId;
            }
          });
          this.roleList = res.data.recordList;
        }
      });
    },
    onClose() {
      this.$emit("close");
    },
    onSubmit() {
      this.$refs.rulesForm.validate(valid => {
        if (valid) {
          const params = {
            user: {
              userId: this.formObj.userId,
              userName: this.formObj.userName,
              realName: this.formObj.realName,
              phone: this.formObj.phone,
            },
            roleIds: [Number(this.formObj.roleIds)],
          };
          this.loading = true;
          $req.post("/athena/api/coreresource/auth/user/updateUser/v1", params).then(res => {
            if (res.code === 0) {
              this.loading = false;
              this.$message.success(res.msg);
              this.$emit("close", "success");
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="less">
.edit-user-dialog {
  .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 520px;
    max-height: calc(100% - 30px);
  }
  .el-dialog__body {
    padding: 15px 20px;
    flex: 1;
    overflow: auto;
  }
  .el-input {
    width: 220px;
  }
}
</style>
