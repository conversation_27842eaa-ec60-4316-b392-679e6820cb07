<template>
  <div class="notification-con">
    <edit-dialog
      v-if="showDialog"
      :show-dialog="showDialog"
      :dialog-data="dialogData"
      @closeDialog="ctrlDialog(false)"
      @submit="submitData"
    />
    <el-card class="con-inner">
      <el-table
        v-loading="tableLoading"
        class="table-style"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column prop="title" :label="$t('lang.rms.email.config.configuration.item')"> </el-table-column>
        <el-table-column width="400" prop="value" :label="$t('lang.rms.email.config.currentValue')"> </el-table-column>
        <el-table-column prop="desc" :label="$t('lang.rms.fed.describe')"> </el-table-column>
      </el-table>
      <div v-if="!isRoleGuest" class="edit-btn">
        <el-button type="primary" style="width: 100px" @click="ctrlDialog(true)">{{ $t('lang.rms.fed.buttonEdit') }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
// 编辑dialog
import editDialog from "./components/editDialog";
export default {
  name: "NotificationManagement",
  components: { editDialog },
  data() {
    return {
      showDialog: false,
      tableLoading: false,
      // 项目名称
      appName: {
        title: "项目名称",
        value: "",
        desc: "项目名称",
      },
      sendMail: {
        title: "发件箱地址",
        value: "",
        desc: "发件人的邮箱地址和密码",
      },
      receiveMail: {
        title: "收件箱地址",
        value: "",
        desc: "异常通知的邮件，支持设置多个，多个用逗号隔开",
      },
      notifyRate: {
        title: "通知频率",
        value: "",
        desc: "设置邮件通知的频率，单位为分钟",
      },
      isNotify: {
        title: "异常恢复后是否通知",
        value: "",
        desc: "邮件通知开关，开启true,关闭是false,默认为false",
      },
      // 传入dialog的数据
      dialogData: {
        isNotify: true,
      },
      // 更新邮件id
      updateId: null,
    };
  },
  computed: {
    tableData() {
      const { appName, sendMail, receiveMail, notifyRate, isNotify } = this;
      return [appName, sendMail, receiveMail, notifyRate, isNotify];
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  mounted() {
    this.reqEmailData();
  },
  methods: {
    // 请求接口
    reqEmailData() {
      this.tableLoading = true;
      $req
        .get("/athena/email/config/getEmailConfig")
        .then(res => {
          this.tableLoading = false;
          if (!res.data) return;
          const {
            id,
            senderAddress,
            senderAddressPwd,
            inboxAddress,
            isNoticeResolved,
            frequency,
            emailContentParam,
          } = res.data;
          const appName = emailContentParam.appName;
          const inboxAddressList = inboxAddress.map(item => item.emailAddress);
          this.updateId = id;
          this.appName.value = appName;
          this.sendMail.value = senderAddress;
          this.receiveMail.value = inboxAddressList.join(",");
          this.notifyRate.value = frequency;
          this.isNotify.value = isNoticeResolved.toString();
          // 截取发送邮件的前半段
          const emailNameIndex = senderAddress.lastIndexOf("@");
          const emailName = senderAddress.substr(0, emailNameIndex);
          this.dialogData = {
            appName,
            sendMail: emailName,
            sendMailPassword: senderAddressPwd,
            receiveMail: inboxAddressList,
            rate: frequency,
            isNotify: isNoticeResolved,
          };
        })
        .catch(err => {
          this.tableLoading = false;
        });
    },
    // 发送email内容
    submitData(submitData) {
      this.ctrlDialog(false);
      const { appName, isNotify, rate, receiveMail, sendMail, sendMailPassword } = submitData;
      const params = {
        id: this.updateId,
        inboxAddress: receiveMail.map(email => {
          return { emailAddress: email };
        }),
        isNoticeResolved: isNotify,
        frequency: rate,
        senderAddress: sendMail,
        senderAddressPwd: sendMailPassword,
        languageCode: $app.$i18n.locale,
        emailContentParam: { appName },
      };
      $req.post("/athena/email/config/updateEmailConfig", params).then(res => {
        const { code, msg } = res;
        let messageInfo = {
          type: code === 0 ? "success" : "warning",
          message: this.$t(msg),
        };
        this.$message(messageInfo);
        this.reqEmailData();
      });
    },
    // 控制dialog显隐
    ctrlDialog(flag) {
      this.showDialog = flag;
    },
  },
};
</script>
<style lang="less">
.el-table .cell.el-tooltip {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
  max-width: 400px;
  //max-width: 80px;
}
</style>
<style scoped lang="scss">
.notification-con {
  height: 100%;
  .con-inner {
    height: 100% !important;
    .edit-btn {
      float: right;
      margin-top: 20px;
    }
  }
}
</style>
